LICENSE
NOTICE
README.txt
setup.py
hy3dgen/__init__.py
hy3dgen/rembg.py
hy3dgen/text2image.py
hy3dgen.egg-info/PKG-INFO
hy3dgen.egg-info/SOURCES.txt
hy3dgen.egg-info/dependency_links.txt
hy3dgen.egg-info/requires.txt
hy3dgen.egg-info/top_level.txt
hy3dgen/shapegen/__init__.py
hy3dgen/shapegen/pipelines.py
hy3dgen/shapegen/postprocessors.py
hy3dgen/shapegen/preprocessors.py
hy3dgen/shapegen/schedulers.py
hy3dgen/shapegen/utils.py
hy3dgen/shapegen/models/__init__.py
hy3dgen/shapegen/models/conditioner.py
hy3dgen/shapegen/models/autoencoders/__init__.py
hy3dgen/shapegen/models/autoencoders/attention_blocks.py
hy3dgen/shapegen/models/autoencoders/attention_processors.py
hy3dgen/shapegen/models/autoencoders/model.py
hy3dgen/shapegen/models/autoencoders/surface_extractors.py
hy3dgen/shapegen/models/autoencoders/volume_decoders.py
hy3dgen/shapegen/models/denoisers/__init__.py
hy3dgen/shapegen/models/denoisers/hunyuan3ddit.py
hy3dgen/texgen/__init__.py
hy3dgen/texgen/pipelines.py
hy3dgen/texgen/differentiable_renderer/__init__.py
hy3dgen/texgen/differentiable_renderer/camera_utils.py
hy3dgen/texgen/differentiable_renderer/mesh_processor.py
hy3dgen/texgen/differentiable_renderer/mesh_render.py
hy3dgen/texgen/differentiable_renderer/mesh_utils.py
hy3dgen/texgen/differentiable_renderer/setup.py
hy3dgen/texgen/hunyuanpaint/__init__.py
hy3dgen/texgen/hunyuanpaint/pipeline.py
hy3dgen/texgen/hunyuanpaint/unet/__init__.py
hy3dgen/texgen/hunyuanpaint/unet/modules.py
hy3dgen/texgen/utils/__init__.py
hy3dgen/texgen/utils/alignImg4Tex_utils.py
hy3dgen/texgen/utils/counter_utils.py
hy3dgen/texgen/utils/dehighlight_utils.py
hy3dgen/texgen/utils/imagesuper_utils.py
hy3dgen/texgen/utils/multiview_utils.py
hy3dgen/texgen/utils/simplify_mesh_utils.py
hy3dgen/texgen/utils/uv_warp_utils.py