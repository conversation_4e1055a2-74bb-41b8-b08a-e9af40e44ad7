#!/usr/bin/env python3
"""
Background Services Manager
Handles automatic startup and monitoring of background services like Hunyuan3D-2 native server
"""

import os
import sys
import time
import subprocess
import threading
import requests
import psutil
from pathlib import Path
from typing import Optional, Dict, Any, Callable
import logging

# Set up logging - integrate with main app's logging system
try:
    # Try to use the main app's logging system
    from utils.logging_system import get_logger
    logger = get_logger()
    logger.info("Background services using main app logging system", component="BACKGROUND_SERVICES")
except ImportError:
    # Fallback to basic logging if main app logging not available
    import logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    logger.info("Background services using fallback logging")

class BackgroundServiceManager:
    """Manages background services for the 3D AI Studio application."""
    
    def __init__(self):
        self.services = {}
        self.monitoring_active = False
        self.monitor_thread = None
        
    def register_service(self, service_name: str, service_config: Dict[str, Any]):
        """Register a background service."""
        self.services[service_name] = {
            'config': service_config,
            'process': None,
            'status': 'stopped',
            'last_check': 0,
            'restart_count': 0,
            'max_restarts': service_config.get('max_restarts', 3)
        }
        logger.info(f"Registered service: {service_name}", component="BACKGROUND_SERVICES")
    
    def start_service(self, service_name: str, progress_callback: Optional[Callable] = None) -> bool:
        """Start a specific background service."""
        if service_name not in self.services:
            logger.error(f"Service {service_name} not registered")
            return False
        
        service = self.services[service_name]
        config = service['config']
        
        try:
            if progress_callback:
                progress_callback(f"Starting {service_name}...")
            
            logger.info(f"Starting service: {service_name}", component="BACKGROUND_SERVICES")

            # Check if already running
            if self.is_service_running(service_name):
                logger.info(f"Service {service_name} already running", component="BACKGROUND_SERVICES")
                if progress_callback:
                    progress_callback(f"{service_name} already running")
                return True
            
            # Start the service process
            if config['type'] == 'batch_file':
                success = self._start_batch_service(service_name, config, progress_callback)
            elif config['type'] == 'python_script':
                success = self._start_python_service(service_name, config, progress_callback)
            else:
                logger.error(f"Unknown service type: {config['type']}")
                return False
            
            if success:
                service['status'] = 'starting'
                service['restart_count'] = 0
                
                # Wait for service to be ready
                if progress_callback:
                    progress_callback(f"Waiting for {service_name} to be ready...")
                
                ready = self._wait_for_service_ready(service_name, config, progress_callback)
                if ready:
                    service['status'] = 'running'
                    service['start_time'] = time.time()  # Track start time for uptime calculation
                    logger.info(f"Service {service_name} started successfully and is ready", component="BACKGROUND_SERVICES")
                    if progress_callback:
                        progress_callback(f"{service_name} started successfully")
                    return True
                else:
                    service['status'] = 'failed'
                    logger.error(f"Service {service_name} failed to become ready within timeout", component="BACKGROUND_SERVICES")
                    return False
            else:
                service['status'] = 'failed'
                return False
                
        except Exception as e:
            logger.error(f"Error starting service {service_name}: {e}")
            service['status'] = 'failed'
            return False
    
    def _start_batch_service(self, service_name: str, config: Dict[str, Any], progress_callback: Optional[Callable] = None) -> bool:
        """Start a batch file service."""
        try:
            batch_file = Path(config['batch_file'])
            if not batch_file.exists():
                logger.error(f"Batch file not found: {batch_file}")
                return False
            
            # Set working directory
            cwd = config.get('working_dir', batch_file.parent)
            
            # Set environment variables
            env = os.environ.copy()
            if 'env_vars' in config:
                env.update(config['env_vars'])
            
            if progress_callback:
                progress_callback(f"Executing batch file: {batch_file.name}")
            
            # Start the batch file in a hidden window (invisible)
            if os.name == 'nt':  # Windows
                # Use CREATE_NO_WINDOW to make it completely invisible
                creation_flags = subprocess.CREATE_NO_WINDOW
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
            else:  # Linux/Mac
                creation_flags = 0
                startupinfo = None

            process = subprocess.Popen(
                [str(batch_file)],
                cwd=str(cwd),
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                creationflags=creation_flags,
                startupinfo=startupinfo
            )
            
            self.services[service_name]['process'] = process
            logger.info(f"Started batch service {service_name} with PID {process.pid} (hidden window)", component="BACKGROUND_SERVICES")
            return True

        except Exception as e:
            logger.error(f"Error starting batch service {service_name}: {e}", component="BACKGROUND_SERVICES")
            return False
    
    def _start_python_service(self, service_name: str, config: Dict[str, Any], progress_callback: Optional[Callable] = None) -> bool:
        """Start a Python script service."""
        try:
            script_file = Path(config['script_file'])
            if not script_file.exists():
                logger.error(f"Script file not found: {script_file}")
                return False
            
            # Set working directory
            cwd = config.get('working_dir', script_file.parent)
            
            # Set environment variables
            env = os.environ.copy()
            if 'env_vars' in config:
                env.update(config['env_vars'])
            
            if progress_callback:
                progress_callback(f"Executing Python script: {script_file.name}")
            
            # Start the Python script
            python_exe = config.get('python_exe', sys.executable)
            process = subprocess.Popen(
                [python_exe, str(script_file)],
                cwd=str(cwd),
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.services[service_name]['process'] = process
            logger.info(f"Started Python service {service_name} with PID {process.pid}")
            return True
            
        except Exception as e:
            logger.error(f"Error starting Python service {service_name}: {e}")
            return False
    
    def _wait_for_service_ready(self, service_name: str, config: Dict[str, Any], progress_callback: Optional[Callable] = None) -> bool:
        """Wait for a service to become ready."""
        timeout = config.get('startup_timeout', 300)  # 5 minutes default
        check_interval = config.get('check_interval', 5)  # 5 seconds default
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if self._check_service_health(service_name, config):
                return True
            
            if progress_callback:
                elapsed = int(time.time() - start_time)
                progress_callback(f"Waiting for {service_name} ({elapsed}s/{timeout}s)...")
            
            time.sleep(check_interval)
        
        logger.error(f"Service {service_name} failed to become ready within {timeout} seconds")
        return False
    
    def _check_service_health(self, service_name: str, config: Dict[str, Any]) -> bool:
        """Check if a service is healthy and ready."""
        try:
            # For batch file services, prioritize health check over process status
            # because batch files often spawn other processes and exit
            if config.get('type') == 'batch_file' and 'health_check' in config:
                health_config = config['health_check']

                if health_config['type'] == 'http':
                    url = health_config['url']
                    timeout = health_config.get('timeout', 5)

                    try:
                        response = requests.get(url, timeout=timeout)
                        expected_status = health_config.get('expected_status', [200, 404])

                        if response.status_code in expected_status:
                            return True
                        else:
                            return False
                    except requests.RequestException:
                        return False

            # For other service types, check if process is still running
            service = self.services[service_name]
            process = service.get('process')

            if process and process.poll() is not None:
                # For batch files, this is expected - they spawn other processes
                if config.get('type') == 'batch_file':
                    logger.debug(f"Batch file {service_name} has completed (this is normal)")
                    # For batch files, rely on health check instead
                    if 'health_check' in config:
                        return self._check_service_health(service_name, config)
                    else:
                        # No health check available, assume still running
                        return True
                else:
                    logger.warning(f"Service {service_name} process has terminated")
                    return False

            # Check health endpoint if configured (for non-batch services)
            if 'health_check' in config and config.get('type') != 'batch_file':
                health_config = config['health_check']

                if health_config['type'] == 'http':
                    url = health_config['url']
                    timeout = health_config.get('timeout', 5)

                    response = requests.get(url, timeout=timeout)
                    expected_status = health_config.get('expected_status', [200, 404])

                    if response.status_code in expected_status:
                        return True
                    else:
                        return False

            # If no health check configured, assume healthy if process is running
            return True

        except Exception as e:
            logger.debug(f"Health check failed for {service_name}: {e}")
            return False
    
    def is_service_running(self, service_name: str) -> bool:
        """Check if a service is currently running."""
        if service_name not in self.services:
            return False
        
        service = self.services[service_name]
        return service['status'] == 'running' and self._check_service_health(service_name, service['config'])
    
    def stop_service(self, service_name: str) -> bool:
        """Stop a specific service."""
        if service_name not in self.services:
            logger.error(f"Service {service_name} not registered")
            return False
        
        try:
            service = self.services[service_name]
            process = service.get('process')
            
            if process:
                logger.info(f"Stopping service {service_name} (PID {process.pid})")
                
                # Try graceful termination first
                process.terminate()
                try:
                    process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    # Force kill if graceful termination fails
                    logger.warning(f"Force killing service {service_name}")
                    process.kill()
                    process.wait()
                
                service['process'] = None
            
            service['status'] = 'stopped'
            logger.info(f"Service {service_name} stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping service {service_name}: {e}")
            return False
    
    def start_monitoring(self):
        """Start monitoring all registered services."""
        if self.monitoring_active:
            logger.info("Service monitoring already active")
            return
        
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitor_services, daemon=True)
        self.monitor_thread.start()
        logger.info("Started service monitoring")
    
    def stop_monitoring(self):
        """Stop monitoring services."""
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("Stopped service monitoring")
    
    def _monitor_services(self):
        """Monitor services and restart if needed."""
        last_status_log = 0
        status_log_interval = 300  # Log status every 5 minutes

        while self.monitoring_active:
            try:
                for service_name, service in self.services.items():
                    if service['status'] == 'running':
                        config = service['config']

                        # Check service health
                        is_healthy = self._check_service_health(service_name, config)

                        if not is_healthy:
                            # For batch services, be more lenient
                            if config.get('type') == 'batch_file':
                                logger.info(f"Batch service {service_name} health check failed - this may be normal during startup")

                                # Only restart batch services if they've been running for a while
                                # and health check consistently fails
                                service['consecutive_failures'] = service.get('consecutive_failures', 0) + 1

                                if service['consecutive_failures'] >= 3:  # 3 consecutive failures
                                    logger.warning(f"Batch service {service_name} has failed health checks consistently")

                                    # Attempt restart if under restart limit
                                    if service['restart_count'] < service['max_restarts']:
                                        logger.info(f"Attempting to restart {service_name} (attempt {service['restart_count'] + 1})")
                                        service['restart_count'] += 1
                                        service['status'] = 'restarting'
                                        service['consecutive_failures'] = 0

                                        # Stop and restart
                                        self.stop_service(service_name)
                                        time.sleep(10)  # Wait longer for batch services
                                        self.start_service(service_name)
                                    else:
                                        logger.error(f"Service {service_name} exceeded max restart attempts")
                                        service['status'] = 'failed'
                            else:
                                # For non-batch services, restart immediately
                                logger.warning(f"Service {service_name} is unhealthy")

                                # Attempt restart if under restart limit
                                if service['restart_count'] < service['max_restarts']:
                                    logger.info(f"Attempting to restart {service_name} (attempt {service['restart_count'] + 1})")
                                    service['restart_count'] += 1
                                    service['status'] = 'restarting'

                                    # Stop and restart
                                    self.stop_service(service_name)
                                    time.sleep(5)  # Wait before restart
                                    self.start_service(service_name)
                                else:
                                    logger.error(f"Service {service_name} exceeded max restart attempts")
                                    service['status'] = 'failed'
                        else:
                            # Reset consecutive failures on successful health check
                            service['consecutive_failures'] = 0

                # Periodic status logging
                current_time = time.time()
                if current_time - last_status_log >= status_log_interval:
                    self.log_services_status()
                    last_status_log = current_time

                # Check less frequently for batch services
                time.sleep(45)  # Check every 45 seconds (less aggressive)

            except Exception as e:
                logger.error(f"Error in service monitoring: {e}")
                time.sleep(45)
    
    def get_service_status(self, service_name: str) -> Dict[str, Any]:
        """Get the status of a specific service."""
        if service_name not in self.services:
            return {'status': 'unknown', 'error': 'Service not registered'}

        service = self.services[service_name]
        process = service.get('process')

        # Calculate uptime if process is running
        uptime = None
        if process and service.get('start_time'):
            uptime_seconds = time.time() - service['start_time']
            hours = int(uptime_seconds // 3600)
            minutes = int((uptime_seconds % 3600) // 60)
            uptime = f"{hours}h {minutes}m"

        return {
            'status': service['status'],
            'restart_count': service['restart_count'],
            'max_restarts': service['max_restarts'],
            'running': self.is_service_running(service_name),
            'pid': process.pid if process else None,
            'uptime': uptime,
            'description': service['config'].get('description', 'Background service')
        }
    
    def get_all_services_status(self) -> Dict[str, Dict[str, Any]]:
        """Get the status of all registered services."""
        return {name: self.get_service_status(name) for name in self.services.keys()}

    def log_services_status(self):
        """Log the current status of all services to the main app logs."""
        try:
            status = self.get_all_services_status()

            if not status:
                logger.info("No background services registered", component="BACKGROUND_SERVICES")
                return

            logger.info("Background Services Status Report:", component="BACKGROUND_SERVICES")

            for service_name, service_status in status.items():
                status_indicator = "RUNNING" if service_status['running'] else "STOPPED"
                restart_info = f"(restarts: {service_status['restart_count']}/{service_status['max_restarts']})"

                logger.info(f"  {status_indicator} {service_name}: {service_status['status']} {restart_info}", component="BACKGROUND_SERVICES")

                # Add health check info for running services
                if service_status['running'] and service_name in self.services:
                    config = self.services[service_name]['config']
                    if 'health_check' in config:
                        health_url = config['health_check'].get('url', 'N/A')
                        logger.info(f"    Health check: {health_url}", component="BACKGROUND_SERVICES")

        except Exception as e:
            logger.error(f"Error logging services status: {e}", component="BACKGROUND_SERVICES")
    
    def start_all_services(self, progress_callback: Optional[Callable] = None) -> bool:
        """Start all registered services."""
        success = True
        
        for service_name in self.services.keys():
            if progress_callback:
                progress_callback(f"Starting {service_name}...")
            
            if not self.start_service(service_name, progress_callback):
                success = False
                logger.error(f"Failed to start service: {service_name}")
        
        return success
    
    def stop_all_services(self) -> bool:
        """Stop all registered services."""
        success = True
        
        for service_name in self.services.keys():
            if not self.stop_service(service_name):
                success = False
                logger.error(f"Failed to stop service: {service_name}")
        
        return success


# Global service manager instance
service_manager = BackgroundServiceManager()

def register_hunyuan3d_service():
    """Register the Hunyuan3D-2 native server as a background service."""
    # Get the workspace root dynamically
    workspace_root = os.path.dirname(os.path.abspath(__file__))

    hunyuan3d_config = {
        'type': 'batch_file',
        'batch_file': os.path.join(workspace_root, 'Resources', 'Hunyuan3D2_WinPortable', 'run-with-text_to_3d.bat'),
        'working_dir': os.path.join(workspace_root, 'Resources', 'Hunyuan3D2_WinPortable'),
        'startup_timeout': 900,  # 15 minutes for model loading (increased for text-to-3D models)
        'check_interval': 20,    # Check every 20 seconds (less frequent for batch services)
        'max_restarts': 2,       # Allow 2 restart attempts for batch services
        'health_check': {
            'type': 'http',
            'url': 'http://localhost:8080/',
            'timeout': 15,
            'expected_status': [200, 404, 422]  # Any of these indicates server is responding
        },
        'env_vars': {
            # Ensure text-to-3D is enabled
            'HUNYUAN3D_ENABLE_T23D': '1',
            # Optimize for startup
            'PYTHONUNBUFFERED': '1'
        },
        'batch_service': True,   # Flag to indicate this is a batch service
        'process_spawns_others': True,  # Batch file spawns other processes
        'description': 'Hunyuan3D-2 Native Server with Text-to-3D Support'
    }

    service_manager.register_service('hunyuan3d_native', hunyuan3d_config)
    logger.info("Registered Hunyuan3D-2 native server service with text-to-3D support", component="BACKGROUND_SERVICES")

def initialize_background_services():
    """Initialize all background services."""
    logger.info("Initializing background services...", component="BACKGROUND_SERVICES")

    # Register services
    register_hunyuan3d_service()

    # Start monitoring
    service_manager.start_monitoring()

    # Log initial status
    service_manager.log_services_status()

    logger.info("Background services initialized successfully", component="BACKGROUND_SERVICES")

def start_background_services(progress_callback: Optional[Callable] = None):
    """Start all background services."""
    logger.info("Starting background services...", component="BACKGROUND_SERVICES")

    if progress_callback:
        progress_callback("Starting background services...")

    success = service_manager.start_all_services(progress_callback)

    # Log detailed status after startup attempt
    service_manager.log_services_status()

    if success:
        logger.info("All background services started successfully", component="BACKGROUND_SERVICES")
        if progress_callback:
            progress_callback("Background services started successfully")
    else:
        logger.warning("Some background services failed to start", component="BACKGROUND_SERVICES")
        if progress_callback:
            progress_callback("Some background services failed to start")

    return success

def stop_background_services():
    """Stop all background services."""
    logger.info("Stopping background services...")
    
    service_manager.stop_monitoring()
    success = service_manager.stop_all_services()
    
    if success:
        logger.info("All background services stopped successfully")
    else:
        logger.warning("Some background services failed to stop cleanly")
    
    return success

def get_service_status(service_name: str = None) -> Dict[str, Any]:
    """Get service status."""
    if service_name:
        return service_manager.get_service_status(service_name)
    else:
        return service_manager.get_all_services_status()

if __name__ == "__main__":
    # Test the background service manager
    print("🧪 Testing Background Service Manager")
    
    # Initialize services
    initialize_background_services()
    
    # Start services
    def progress_callback(message):
        print(f"PROGRESS: {message}")

    success = start_background_services(progress_callback)

    if success:
        print("SUCCESS: Background services test completed successfully")

        # Show status
        status = get_service_status()
        print(f"STATUS: Service status: {status}")

        # Wait a bit then stop
        time.sleep(10)
        stop_background_services()
    else:
        print("ERROR: Background services test failed")
