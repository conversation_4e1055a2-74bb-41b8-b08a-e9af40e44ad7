import React, { useRef, useEffect, useState } from 'react';
import { <PERSON>vas, useFrame, useLoader } from '@react-three/fiber';
import { OrbitControls, Stage, useGLTF } from '@react-three/drei';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { PLYLoader } from 'three/examples/jsm/loaders/PLYLoader';
import { Mesh, PointsMaterial, Points, Vector3 } from 'three';
import { Info, X, Clock, Settings, Cpu, Zap, Image, Type } from 'lucide-react';
import * as THREE from 'three';

// Error boundary for 3D model loading
class ModelErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    console.error('ModelErrorBoundary: Error caught:', error);
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('ModelErrorBoundary caught an error:', error);
    console.error('Error info:', errorInfo);
    console.error('Stack trace:', error.stack);
  }

  render() {
    if (this.state.hasError) {
      console.error('Model loading error, showing fallback:', this.state.error);
      // Return fallback sphere with error indication
      return (
        <mesh>
          <sphereGeometry args={[1, 32, 32]} />
          <meshStandardMaterial color="red" wireframe={!this.props.isTextured} />
        </mesh>
      );
    }

    return this.props.children;
  }
}

// Rotating mesh component
const RotatingMesh = ({ isTextured, isDarkMode }) => {
  const meshRef = useRef();

  useFrame((state, delta) => {
    if (meshRef.current) {
      // Rotate slowly (0.3 radians per second)
      meshRef.current.rotation.y += delta * 0.3;
    }
  });

  return (
    <mesh ref={meshRef}>
      <sphereGeometry args={[1.0, 32, 32]} />
      <meshStandardMaterial
        color={isDarkMode ? '#303030' : '#404040'}
        wireframe={!isTextured}
      />
    </mesh>
  );
};

// Safe GLB Model component - memoized to prevent unnecessary re-renders
const GLBModel = React.memo(({ modelUrl, isTextured }) => {
  console.log('GLBModel: Starting to load:', modelUrl);

  const modelRef = useRef();
  const [modelScale, setModelScale] = useState(1.0);
  const [modelPosition, setModelPosition] = useState([0, 0, 0]);

  // Use useGLTF - this must be called unconditionally
  console.log('GLBModel: Calling useGLTF...');
  const gltf = useGLTF(modelUrl);
  const scene = gltf?.scene;

  console.log('GLBModel: useGLTF result:', { gltf, scene });

  // Check if scene is valid
  if (!scene) {
    console.error('GLBModel: No scene found in GLTF, returning fallback');
    return (
      <mesh>
        <sphereGeometry args={[1, 32, 32]} />
        <meshStandardMaterial color="yellow" wireframe={!isTextured} />
      </mesh>
    );
  }

  // Auto-fit model to viewport (only run once when scene loads)
  useEffect(() => {
    if (scene) {
      // Calculate bounding box for auto-fitting
      const box = new THREE.Box3().setFromObject(scene);
      const center = box.getCenter(new THREE.Vector3());
      const size = box.getSize(new THREE.Vector3());

      // Get the maximum dimension
      const maxDim = Math.max(size.x, size.y, size.z);

      // Calculate scale to fit nicely in viewport (target size of ~2 units)
      const targetSize = 2.0;
      const scale = maxDim > 0 ? targetSize / maxDim : 1.0;

      // Center the model
      const position = [-center.x * scale, -center.y * scale, -center.z * scale];

      setModelScale(scale);
      setModelPosition(position);
    }
  }, [scene]); // Only depend on scene, not isTextured

  // Apply wireframe/textured mode and enhance materials (separate effect)
  useEffect(() => {
    if (scene) {
      scene.traverse((node) => {
        if (node instanceof Mesh) {
          // Ensure material is an array or single material
          const materials = Array.isArray(node.material) ? node.material : [node.material];
          materials.forEach((material) => {
            if (material) {
              material.wireframe = !isTextured;

              // Enhance material properties for better lighting response
              if (material instanceof THREE.MeshStandardMaterial) {
                // Preserve original material properties but ensure good lighting response
                material.roughness = Math.max(material.roughness, 0.1); // Minimum roughness for realism
                material.metalness = Math.max(material.metalness, 0.0); // Keep original metalness

                // Ensure the material responds to lighting
                material.needsUpdate = true;

                // Only slightly adjust very dark materials to prevent loss of detail
                if (material.color) {
                  const hsl = {};
                  material.color.getHSL(hsl);
                  if (hsl.l < 0.15) { // Only adjust extremely dark materials
                    material.color.setHSL(hsl.h, hsl.s, Math.max(hsl.l, 0.2));
                  }
                }
              } else if (material instanceof THREE.MeshPhongMaterial) {
                // For Phong materials, ensure reasonable shininess
                material.shininess = Math.max(material.shininess, 10);
                material.needsUpdate = true;

                // Only slightly adjust very dark materials
                if (material.color) {
                  const hsl = {};
                  material.color.getHSL(hsl);
                  if (hsl.l < 0.15) { // Only adjust extremely dark materials
                    material.color.setHSL(hsl.h, hsl.s, Math.max(hsl.l, 0.2));
                  }
                }
              }
            }
          });
        }
      });
    }
  }, [scene, isTextured]);

  useFrame((state, delta) => {
    if (modelRef.current) {
      // Rotate slowly (0.3 radians per second)
      modelRef.current.rotation.y += delta * 0.3;
    }
  });

  return (
    <primitive
      ref={modelRef}
      object={scene}
      scale={modelScale}
      position={modelPosition}
    />
  );
});

// PLY Model component (for point clouds) - memoized to prevent unnecessary re-renders
const PLYModel = React.memo(({ modelUrl, isTextured }) => {
  console.log('PLYModel loading:', modelUrl);

  const geometry = useLoader(PLYLoader, modelUrl);
  const pointsRef = useRef();

  // Fix orientation and centering
  useEffect(() => {
    if (geometry) {
      geometry.computeBoundingBox();
      const box = geometry.boundingBox;
      const center = box.getCenter(new THREE.Vector3());

      // Center the geometry
      geometry.translate(-center.x, -center.y, -center.z);

      // Rotate to fix upside-down orientation (rotate 180 degrees around X axis)
      geometry.rotateX(Math.PI);

      // Scale to fit nicely in view
      const size = box.getSize(new THREE.Vector3());
      const maxDim = Math.max(size.x, size.y, size.z);
      const scale = 2 / maxDim;
      geometry.scale(scale, scale, scale);
    }
  }, [geometry]);

  useFrame((state, delta) => {
    if (pointsRef.current) {
      // Rotate slowly (0.2 radians per second - slower rotation)
      pointsRef.current.rotation.y += delta * 0.2;
    }
  });

  return (
    <points ref={pointsRef}>
      <primitive object={geometry} />
      <pointsMaterial
        size={0.003}
        vertexColors={true}
        sizeAttenuation={true}
        alphaTest={0.1}
        transparent={false}
      />
    </points>
  );
});


interface GenerationStats {
  generationMode?: 'text-to-3d' | 'image-to-3d';
  prompt?: string;
  enhancedPrompt?: string;
  imageModel?: string;
  settings?: {
    ss_steps?: number;
    ss_cfg_strength?: number;
    slat_steps?: number;
    slat_cfg_strength?: number;
    seed?: number;
    simplify?: number;
    texture_size?: number;
    enable_lighting_optimizer?: boolean;
    enable_delighter?: boolean;
    delighter_quality?: string;
  };
  fileInfo?: {
    type?: string;
    size?: number;
    vertices?: number;
    faces?: number;
    polygons?: number;
    textureResolution?: string;
    hasTextures?: boolean;
  };
  timing?: {
    totalTime?: number;
    textToImageTime?: number;
    modelGenerationTime?: number;
    delighterTime?: number;
  };
}

interface ModelViewerProps {
  modelUrl?: string | null;
  isTextured?: boolean;
  isDarkMode?: boolean;
  generationStats?: GenerationStats;
}

export const ModelViewer: React.FC<ModelViewerProps> = ({
  modelUrl,
  isTextured = true,
  isDarkMode = false,
  generationStats,
}) => {
  const [loadingError, setLoadingError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [fileType, setFileType] = useState<string | null>(null);
  const [isInfoExpanded, setIsInfoExpanded] = useState(false);

  // Debug logging - only depend on modelUrl, not isTextured
  React.useEffect(() => {
    console.log('ModelViewer props:', { modelUrl, isTextured, isDarkMode });
    if (modelUrl) {
      setIsLoading(true);
      setLoadingError(null);
      setFileType(null);

      // Test if the URL is accessible and get file info
      fetch(modelUrl, { method: 'HEAD' })
        .then(response => {
          console.log('Model URL test response:', response.status, response.statusText);
          const contentType = response.headers.get('content-type');
          console.log('Content-Type:', contentType);
          console.log('Content-Length:', response.headers.get('content-length'));

          if (!response.ok) {
            setLoadingError(`HTTP ${response.status}: ${response.statusText}`);
          } else {
            // Determine file type from Content-Type header
            if (contentType?.includes('model/gltf-binary')) {
              setFileType('glb');
            } else if (contentType?.includes('model/gltf+json')) {
              setFileType('gltf');
            } else if (contentType?.includes('application/octet-stream')) {
              // Could be PLY or GLB, check URL for hints
              const urlLower = modelUrl.toLowerCase();
              if (urlLower.includes('ply') || urlLower.endsWith('.ply')) {
                setFileType('ply');
              } else {
                setFileType('glb'); // Default to GLB for binary files
              }
            } else {
              setFileType('unknown');
            }

            // Also test if we can actually fetch the file content
            return fetch(modelUrl);
          }
          setIsLoading(false);
        })
        .then(response => {
          if (response) {
            console.log('Full fetch response:', response.status, response.headers.get('content-type'));
            return response.blob();
          }
        })
        .then(blob => {
          if (blob) {
            console.log('GLB file blob size:', blob.size, 'bytes');
            console.log('GLB file type:', blob.type);
          }
          setIsLoading(false);
        })
        .catch(error => {
          console.error('Model URL test failed:', error);
          setLoadingError(`Network error: ${error.message}`);
          setIsLoading(false);
        });
    }
  }, [modelUrl]); // Only depend on modelUrl, not isTextured or isDarkMode

  // Determine file type
  const getFileExtension = (url: string) => {
    return url.split('.').pop()?.toLowerCase();
  };

  const renderModel = () => {
    if (!modelUrl) {
      console.log('No modelUrl provided, showing default sphere');
      return <RotatingMesh isTextured={isTextured} isDarkMode={isDarkMode} />;
    }

    console.log('Model URL:', modelUrl, 'Detected file type:', fileType);

    if (fileType === 'ply') {
      return (
        <React.Suspense fallback={<RotatingMesh isTextured={isTextured} isDarkMode={isDarkMode} />}>
          <PLYModel
            key={modelUrl} // Use modelUrl as key to persist component across isTextured changes
            modelUrl={modelUrl}
            isTextured={isTextured}
          />
        </React.Suspense>
      );
    } else if (fileType === 'glb' || fileType === 'gltf') {
      return (
        <React.Suspense fallback={<RotatingMesh isTextured={isTextured} isDarkMode={isDarkMode} />}>
          <ModelErrorBoundary isTextured={isTextured}>
            <GLBModel
              key={modelUrl} // Use modelUrl as key to persist component across isTextured changes
              modelUrl={modelUrl}
              isTextured={isTextured}
            />
          </ModelErrorBoundary>
        </React.Suspense>
      );
    } else {
      console.log('Unknown or unsupported file type:', fileType, 'showing default sphere');
      return <RotatingMesh isTextured={isTextured} isDarkMode={isDarkMode} />;
    }
  };

  // Show error state if there's a loading error
  if (loadingError) {
    return (
      <div className={`w-full h-full rounded-lg flex items-center justify-center ${
        isDarkMode
          ? 'bg-gradient-to-br from-gray-800 to-gray-900 text-white'
          : 'bg-gradient-to-br from-white to-gray-100 text-gray-800'
      }`}>
        <div className="text-center p-4">
          <div className="text-red-500 mb-2">⚠️ Model Loading Error</div>
          <div className="text-sm">{loadingError}</div>
          <div className="text-xs mt-2 opacity-70">URL: {modelUrl}</div>
        </div>
      </div>
    );
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className={`w-full h-full rounded-lg flex items-center justify-center ${
        isDarkMode
          ? 'bg-gradient-to-br from-gray-800 to-gray-900 text-white'
          : 'bg-gradient-to-br from-white to-gray-100 text-gray-800'
      }`}>
        <div className="text-center">
          <div className="animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
          <div className="text-sm">Testing model URL...</div>
        </div>
      </div>
    );
  }

  // Format file size
  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'Unknown';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  // Format time duration
  const formatTime = (seconds?: number) => {
    if (!seconds) return 'Unknown';
    if (seconds < 60) return `${seconds.toFixed(1)}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds.toFixed(0)}s`;
  };

  return (
    <div className={`w-full h-full rounded-lg overflow-hidden relative ${
      isDarkMode
        ? 'bg-gradient-to-br from-gray-800 to-gray-900'
        : 'bg-gradient-to-br from-white to-gray-100'
    }`}>
      <Canvas
        camera={{ position: [2, 2, 3], fov: 45 }}
        gl={{
          antialias: true,
          alpha: true,
          preserveDrawingBuffer: false,
          // Enhanced rendering quality
          powerPreference: "high-performance",
          stencil: false,
          depth: true,
          // Enable multisampling for better antialiasing
          samples: 4,
          // Better color reproduction
          outputColorSpace: "srgb",
          toneMapping: THREE.ACESFilmicToneMapping,
          toneMappingExposure: 1.0
        }}
        style={{
          background: isDarkMode
            ? 'linear-gradient(135deg, #1f2937 0%, #111827 100%)'
            : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)'
        }}
      >
        {/* Balanced studio lighting setup */}
        {/* Ambient light for overall base illumination */}
        <ambientLight intensity={0.4} color="#ffffff" />

        {/* Key light - main directional light */}
        <directionalLight
          position={[5, 5, 5]}
          intensity={1.0}
          color="#ffffff"
          castShadow
          shadow-mapSize-width={2048}
          shadow-mapSize-height={2048}
          shadow-camera-near={0.1}
          shadow-camera-far={50}
          shadow-camera-left={-10}
          shadow-camera-right={10}
          shadow-camera-top={10}
          shadow-camera-bottom={-10}
        />

        {/* Fill light - softer light from opposite side */}
        <directionalLight
          position={[-3, 3, -2]}
          intensity={0.6}
          color="#ffffff"
        />

        {/* Rim light - for edge definition */}
        <directionalLight
          position={[0, 2, -5]}
          intensity={0.8}
          color="#ffffff"
        />

        {/* Environment lighting for natural look */}
        <hemisphereLight
          skyColor="#ffffff"
          groundColor="#f0f0f0"
          intensity={0.6}
        />

        {renderModel()}

        <OrbitControls
          enableZoom={true}
          enablePan={true}
          enableRotate={true}
          rotateSpeed={0.6}
          zoomSpeed={1.0}
          panSpeed={0.8}
          target={[0, 0, 0]}
          minDistance={0.5}
          maxDistance={15}
          autoRotate={false}
          enableDamping={true}
          dampingFactor={0.08}
          // Smooth zoom and rotation
          screenSpacePanning={false}
          maxPolarAngle={Math.PI}
          minPolarAngle={0}
        />
      </Canvas>

      {/* Info Panel Overlay */}
      <div className="absolute top-4 left-4 z-10">
        {/* Info Icon Button */}
        <button
          onClick={() => setIsInfoExpanded(!isInfoExpanded)}
          className={`p-2 rounded-lg transition-all duration-200 ${
            isDarkMode
              ? 'bg-gray-800/80 hover:bg-gray-700/90 text-white'
              : 'bg-white/80 hover:bg-white/95 text-gray-800'
          } backdrop-blur-sm shadow-lg border ${
            isDarkMode ? 'border-gray-600/50' : 'border-gray-200/50'
          }`}
          title="Generation Stats"
        >
          {isInfoExpanded ? (
            <X className="w-4 h-4" />
          ) : (
            <Info className="w-4 h-4" />
          )}
        </button>

        {/* Expanded Info Panel */}
        {isInfoExpanded && generationStats && (
          <div className={`mt-2 p-4 rounded-lg transition-all duration-200 ${
            isDarkMode
              ? 'bg-gray-800/90 text-white border-gray-600/50'
              : 'bg-white/90 text-gray-800 border-gray-200/50'
          } backdrop-blur-sm shadow-xl border min-w-[280px] max-w-[320px]`}>

            {/* Header */}
            <div className="flex items-center gap-2 mb-3 pb-2 border-b border-gray-300/20">
              {generationStats.generationMode === 'text-to-3d' ? (
                <Type className="w-4 h-4 text-blue-500" />
              ) : (
                <Image className="w-4 h-4 text-green-500" />
              )}
              <h3 className="font-semibold text-sm">
                {generationStats.generationMode === 'text-to-3d' ? 'Text-to-3D' : 'Image-to-3D'} Generation
              </h3>
            </div>

            {/* Generation Details */}
            <div className="space-y-3 text-xs">

              {/* Prompt Information */}
              {generationStats.prompt && (
                <div>
                  <div className="font-medium text-gray-500 mb-1">Prompt:</div>
                  <div className="text-xs bg-gray-100/10 rounded p-2 break-words">
                    {generationStats.prompt.length > 100
                      ? `${generationStats.prompt.substring(0, 100)}...`
                      : generationStats.prompt}
                  </div>
                </div>
              )}

              {/* Image Model */}
              {generationStats.imageModel && (
                <div className="flex justify-between">
                  <span className="text-gray-500">Image Model:</span>
                  <span className="font-medium">{generationStats.imageModel.toUpperCase()}</span>
                </div>
              )}

              {/* Generation Settings */}
              {generationStats.settings && (
                <div>
                  <div className="font-medium text-gray-500 mb-2 flex items-center gap-1">
                    <Settings className="w-3 h-3" />
                    Generation Settings
                  </div>
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    {generationStats.settings.seed && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">Seed:</span>
                        <span>{generationStats.settings.seed}</span>
                      </div>
                    )}
                    {generationStats.settings.ss_steps && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">SS Steps:</span>
                        <span>{generationStats.settings.ss_steps}</span>
                      </div>
                    )}
                    {generationStats.settings.slat_steps && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">SLAT Steps:</span>
                        <span>{generationStats.settings.slat_steps}</span>
                      </div>
                    )}
                    {generationStats.settings.texture_size && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">Texture:</span>
                        <span>{generationStats.settings.texture_size}px</span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* File Information */}
              {generationStats.fileInfo && (
                <div>
                  <div className="font-medium text-gray-500 mb-2 flex items-center gap-1">
                    <Cpu className="w-3 h-3" />
                    Model Statistics
                  </div>
                  <div className="space-y-1">
                    {generationStats.fileInfo.type && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">Format:</span>
                        <span className="font-medium">{generationStats.fileInfo.type.toUpperCase()}</span>
                      </div>
                    )}
                    {generationStats.fileInfo.size && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">File Size:</span>
                        <span>{formatFileSize(generationStats.fileInfo.size)}</span>
                      </div>
                    )}
                    {generationStats.fileInfo.vertices !== undefined && generationStats.fileInfo.vertices > 0 && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">Vertex Count:</span>
                        <span className="font-medium">{generationStats.fileInfo.vertices.toLocaleString()}</span>
                      </div>
                    )}
                    {generationStats.fileInfo.faces !== undefined && generationStats.fileInfo.faces > 0 && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">Polygon Count:</span>
                        <span className="font-medium">{generationStats.fileInfo.faces.toLocaleString()}</span>
                      </div>
                    )}
                    {generationStats.fileInfo.textureResolution && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">Texture Resolution:</span>
                        <span className="font-medium">{generationStats.fileInfo.textureResolution}</span>
                      </div>
                    )}
                    {generationStats.fileInfo.hasTextures !== undefined && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">Textures:</span>
                        <span className={`font-medium ${generationStats.fileInfo.hasTextures ? 'text-green-600' : 'text-gray-400'}`}>
                          {generationStats.fileInfo.hasTextures ? 'Yes' : 'No'}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Timing Information */}
              {generationStats.timing && (
                <div>
                  <div className="font-medium text-gray-500 mb-2 flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    Performance Stats
                  </div>
                  <div className="space-y-1">
                    {generationStats.timing.totalTime && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">Total Time:</span>
                        <span className="font-medium">{formatTime(generationStats.timing.totalTime)}</span>
                      </div>
                    )}
                    {generationStats.timing.textToImageTime && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">Text-to-Image:</span>
                        <span>{formatTime(generationStats.timing.textToImageTime)}</span>
                      </div>
                    )}
                    {generationStats.timing.modelGenerationTime && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">3D Generation:</span>
                        <span>{formatTime(generationStats.timing.modelGenerationTime)}</span>
                      </div>
                    )}
                    {generationStats.timing.delighterTime && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">Enhancement:</span>
                        <span>{formatTime(generationStats.timing.delighterTime)}</span>
                      </div>
                    )}
                    {generationStats.fileInfo && generationStats.fileInfo.vertices && generationStats.timing.totalTime && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">Render Rate:</span>
                        <span className="text-xs text-gray-400">
                          {Math.round(generationStats.fileInfo.vertices / generationStats.timing.totalTime).toLocaleString()} verts/sec
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Enhancement Features */}
              {generationStats.settings && (generationStats.settings.enable_lighting_optimizer || generationStats.settings.enable_delighter) && (
                <div>
                  <div className="font-medium text-gray-500 mb-2 flex items-center gap-1">
                    <Zap className="w-3 h-3" />
                    Enhancements
                  </div>
                  <div className="space-y-1">
                    {generationStats.settings.enable_lighting_optimizer && (
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-xs">Lighting Optimizer</span>
                      </div>
                    )}
                    {generationStats.settings.enable_delighter && (
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                        <span className="text-xs">Agisoft De-Lighter ({generationStats.settings.delighter_quality})</span>
                      </div>
                    )}
                  </div>
                </div>
              )}

            </div>
          </div>
        )}
      </div>
    </div>
  );
};