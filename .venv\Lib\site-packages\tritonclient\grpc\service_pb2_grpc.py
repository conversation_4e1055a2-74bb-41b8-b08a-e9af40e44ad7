# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from tritonclient.grpc import service_pb2 as grpc__service__pb2

GRPC_GENERATED_VERSION = '1.67.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in grpc_service_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class GRPCInferenceServiceStub(object):
    """@@
    @@.. cpp:var:: service InferenceService
    @@
    @@   Inference Server GRPC endpoints.
    @@
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.ServerLive = channel.unary_unary(
                '/inference.GRPCInferenceService/ServerLive',
                request_serializer=grpc__service__pb2.ServerLiveRequest.SerializeToString,
                response_deserializer=grpc__service__pb2.ServerLiveResponse.FromString,
                _registered_method=True)
        self.ServerReady = channel.unary_unary(
                '/inference.GRPCInferenceService/ServerReady',
                request_serializer=grpc__service__pb2.ServerReadyRequest.SerializeToString,
                response_deserializer=grpc__service__pb2.ServerReadyResponse.FromString,
                _registered_method=True)
        self.ModelReady = channel.unary_unary(
                '/inference.GRPCInferenceService/ModelReady',
                request_serializer=grpc__service__pb2.ModelReadyRequest.SerializeToString,
                response_deserializer=grpc__service__pb2.ModelReadyResponse.FromString,
                _registered_method=True)
        self.ServerMetadata = channel.unary_unary(
                '/inference.GRPCInferenceService/ServerMetadata',
                request_serializer=grpc__service__pb2.ServerMetadataRequest.SerializeToString,
                response_deserializer=grpc__service__pb2.ServerMetadataResponse.FromString,
                _registered_method=True)
        self.ModelMetadata = channel.unary_unary(
                '/inference.GRPCInferenceService/ModelMetadata',
                request_serializer=grpc__service__pb2.ModelMetadataRequest.SerializeToString,
                response_deserializer=grpc__service__pb2.ModelMetadataResponse.FromString,
                _registered_method=True)
        self.ModelInfer = channel.unary_unary(
                '/inference.GRPCInferenceService/ModelInfer',
                request_serializer=grpc__service__pb2.ModelInferRequest.SerializeToString,
                response_deserializer=grpc__service__pb2.ModelInferResponse.FromString,
                _registered_method=True)
        self.ModelStreamInfer = channel.stream_stream(
                '/inference.GRPCInferenceService/ModelStreamInfer',
                request_serializer=grpc__service__pb2.ModelInferRequest.SerializeToString,
                response_deserializer=grpc__service__pb2.ModelStreamInferResponse.FromString,
                _registered_method=True)
        self.ModelConfig = channel.unary_unary(
                '/inference.GRPCInferenceService/ModelConfig',
                request_serializer=grpc__service__pb2.ModelConfigRequest.SerializeToString,
                response_deserializer=grpc__service__pb2.ModelConfigResponse.FromString,
                _registered_method=True)
        self.ModelStatistics = channel.unary_unary(
                '/inference.GRPCInferenceService/ModelStatistics',
                request_serializer=grpc__service__pb2.ModelStatisticsRequest.SerializeToString,
                response_deserializer=grpc__service__pb2.ModelStatisticsResponse.FromString,
                _registered_method=True)
        self.RepositoryIndex = channel.unary_unary(
                '/inference.GRPCInferenceService/RepositoryIndex',
                request_serializer=grpc__service__pb2.RepositoryIndexRequest.SerializeToString,
                response_deserializer=grpc__service__pb2.RepositoryIndexResponse.FromString,
                _registered_method=True)
        self.RepositoryModelLoad = channel.unary_unary(
                '/inference.GRPCInferenceService/RepositoryModelLoad',
                request_serializer=grpc__service__pb2.RepositoryModelLoadRequest.SerializeToString,
                response_deserializer=grpc__service__pb2.RepositoryModelLoadResponse.FromString,
                _registered_method=True)
        self.RepositoryModelUnload = channel.unary_unary(
                '/inference.GRPCInferenceService/RepositoryModelUnload',
                request_serializer=grpc__service__pb2.RepositoryModelUnloadRequest.SerializeToString,
                response_deserializer=grpc__service__pb2.RepositoryModelUnloadResponse.FromString,
                _registered_method=True)
        self.SystemSharedMemoryStatus = channel.unary_unary(
                '/inference.GRPCInferenceService/SystemSharedMemoryStatus',
                request_serializer=grpc__service__pb2.SystemSharedMemoryStatusRequest.SerializeToString,
                response_deserializer=grpc__service__pb2.SystemSharedMemoryStatusResponse.FromString,
                _registered_method=True)
        self.SystemSharedMemoryRegister = channel.unary_unary(
                '/inference.GRPCInferenceService/SystemSharedMemoryRegister',
                request_serializer=grpc__service__pb2.SystemSharedMemoryRegisterRequest.SerializeToString,
                response_deserializer=grpc__service__pb2.SystemSharedMemoryRegisterResponse.FromString,
                _registered_method=True)
        self.SystemSharedMemoryUnregister = channel.unary_unary(
                '/inference.GRPCInferenceService/SystemSharedMemoryUnregister',
                request_serializer=grpc__service__pb2.SystemSharedMemoryUnregisterRequest.SerializeToString,
                response_deserializer=grpc__service__pb2.SystemSharedMemoryUnregisterResponse.FromString,
                _registered_method=True)
        self.CudaSharedMemoryStatus = channel.unary_unary(
                '/inference.GRPCInferenceService/CudaSharedMemoryStatus',
                request_serializer=grpc__service__pb2.CudaSharedMemoryStatusRequest.SerializeToString,
                response_deserializer=grpc__service__pb2.CudaSharedMemoryStatusResponse.FromString,
                _registered_method=True)
        self.CudaSharedMemoryRegister = channel.unary_unary(
                '/inference.GRPCInferenceService/CudaSharedMemoryRegister',
                request_serializer=grpc__service__pb2.CudaSharedMemoryRegisterRequest.SerializeToString,
                response_deserializer=grpc__service__pb2.CudaSharedMemoryRegisterResponse.FromString,
                _registered_method=True)
        self.CudaSharedMemoryUnregister = channel.unary_unary(
                '/inference.GRPCInferenceService/CudaSharedMemoryUnregister',
                request_serializer=grpc__service__pb2.CudaSharedMemoryUnregisterRequest.SerializeToString,
                response_deserializer=grpc__service__pb2.CudaSharedMemoryUnregisterResponse.FromString,
                _registered_method=True)
        self.TraceSetting = channel.unary_unary(
                '/inference.GRPCInferenceService/TraceSetting',
                request_serializer=grpc__service__pb2.TraceSettingRequest.SerializeToString,
                response_deserializer=grpc__service__pb2.TraceSettingResponse.FromString,
                _registered_method=True)
        self.LogSettings = channel.unary_unary(
                '/inference.GRPCInferenceService/LogSettings',
                request_serializer=grpc__service__pb2.LogSettingsRequest.SerializeToString,
                response_deserializer=grpc__service__pb2.LogSettingsResponse.FromString,
                _registered_method=True)


class GRPCInferenceServiceServicer(object):
    """@@
    @@.. cpp:var:: service InferenceService
    @@
    @@   Inference Server GRPC endpoints.
    @@
    """

    def ServerLive(self, request, context):
        """@@  .. cpp:var:: rpc ServerLive(ServerLiveRequest) returns
        @@       (ServerLiveResponse)
        @@
        @@     Check liveness of the inference server.
        @@
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ServerReady(self, request, context):
        """@@  .. cpp:var:: rpc ServerReady(ServerReadyRequest) returns
        @@       (ServerReadyResponse)
        @@
        @@     Check readiness of the inference server.
        @@
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ModelReady(self, request, context):
        """@@  .. cpp:var:: rpc ModelReady(ModelReadyRequest) returns
        @@       (ModelReadyResponse)
        @@
        @@     Check readiness of a model in the inference server.
        @@
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ServerMetadata(self, request, context):
        """@@  .. cpp:var:: rpc ServerMetadata(ServerMetadataRequest) returns
        @@       (ServerMetadataResponse)
        @@
        @@     Get server metadata.
        @@
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ModelMetadata(self, request, context):
        """@@  .. cpp:var:: rpc ModelMetadata(ModelMetadataRequest) returns
        @@       (ModelMetadataResponse)
        @@
        @@     Get model metadata.
        @@
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ModelInfer(self, request, context):
        """@@  .. cpp:var:: rpc ModelInfer(ModelInferRequest) returns
        @@       (ModelInferResponse)
        @@
        @@     Perform inference using a specific model.
        @@
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ModelStreamInfer(self, request_iterator, context):
        """@@  .. cpp:var:: rpc ModelStreamInfer(stream ModelInferRequest) returns
        @@       (stream ModelStreamInferResponse)
        @@
        @@     Perform streaming inference.
        @@
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ModelConfig(self, request, context):
        """@@  .. cpp:var:: rpc ModelConfig(ModelConfigRequest) returns
        @@       (ModelConfigResponse)
        @@
        @@     Get model configuration.
        @@
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ModelStatistics(self, request, context):
        """@@  .. cpp:var:: rpc ModelStatistics(
        @@                     ModelStatisticsRequest)
        @@                   returns (ModelStatisticsResponse)
        @@
        @@     Get the cumulative inference statistics for a model.
        @@
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RepositoryIndex(self, request, context):
        """@@  .. cpp:var:: rpc RepositoryIndex(RepositoryIndexRequest) returns
        @@       (RepositoryIndexResponse)
        @@
        @@     Get the index of model repository contents.
        @@
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RepositoryModelLoad(self, request, context):
        """@@  .. cpp:var:: rpc RepositoryModelLoad(RepositoryModelLoadRequest) returns
        @@       (RepositoryModelLoadResponse)
        @@
        @@     Load or reload a model from a repository.
        @@
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RepositoryModelUnload(self, request, context):
        """@@  .. cpp:var:: rpc RepositoryModelUnload(RepositoryModelUnloadRequest)
        @@       returns (RepositoryModelUnloadResponse)
        @@
        @@     Unload a model.
        @@
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SystemSharedMemoryStatus(self, request, context):
        """@@  .. cpp:var:: rpc SystemSharedMemoryStatus(
        @@                     SystemSharedMemoryStatusRequest)
        @@                   returns (SystemSharedMemoryStatusRespose)
        @@
        @@     Get the status of all registered system-shared-memory regions.
        @@
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SystemSharedMemoryRegister(self, request, context):
        """@@  .. cpp:var:: rpc SystemSharedMemoryRegister(
        @@                     SystemSharedMemoryRegisterRequest)
        @@                   returns (SystemSharedMemoryRegisterResponse)
        @@
        @@     Register a system-shared-memory region.
        @@
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SystemSharedMemoryUnregister(self, request, context):
        """@@  .. cpp:var:: rpc SystemSharedMemoryUnregister(
        @@                     SystemSharedMemoryUnregisterRequest)
        @@                   returns (SystemSharedMemoryUnregisterResponse)
        @@
        @@     Unregister a system-shared-memory region.
        @@
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CudaSharedMemoryStatus(self, request, context):
        """@@  .. cpp:var:: rpc CudaSharedMemoryStatus(
        @@                     CudaSharedMemoryStatusRequest)
        @@                   returns (CudaSharedMemoryStatusRespose)
        @@
        @@     Get the status of all registered CUDA-shared-memory regions.
        @@
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CudaSharedMemoryRegister(self, request, context):
        """@@  .. cpp:var:: rpc CudaSharedMemoryRegister(
        @@                     CudaSharedMemoryRegisterRequest)
        @@                   returns (CudaSharedMemoryRegisterResponse)
        @@
        @@     Register a CUDA-shared-memory region.
        @@
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CudaSharedMemoryUnregister(self, request, context):
        """@@  .. cpp:var:: rpc CudaSharedMemoryUnregister(
        @@                     CudaSharedMemoryUnregisterRequest)
        @@                   returns (CudaSharedMemoryUnregisterResponse)
        @@
        @@     Unregister a CUDA-shared-memory region.
        @@
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def TraceSetting(self, request, context):
        """@@  .. cpp:var:: rpc TraceSetting(TraceSettingRequest)
        @@                   returns (TraceSettingResponse)
        @@
        @@     Update and get the trace setting of the Triton server.
        @@
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def LogSettings(self, request, context):
        """@@  .. cpp:var:: rpc LogSettings(LogSettingsRequest)
        @@                   returns (LogSettingsResponse)
        @@
        @@     Update and get the log settings of the Triton server.
        @@
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_GRPCInferenceServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'ServerLive': grpc.unary_unary_rpc_method_handler(
                    servicer.ServerLive,
                    request_deserializer=grpc__service__pb2.ServerLiveRequest.FromString,
                    response_serializer=grpc__service__pb2.ServerLiveResponse.SerializeToString,
            ),
            'ServerReady': grpc.unary_unary_rpc_method_handler(
                    servicer.ServerReady,
                    request_deserializer=grpc__service__pb2.ServerReadyRequest.FromString,
                    response_serializer=grpc__service__pb2.ServerReadyResponse.SerializeToString,
            ),
            'ModelReady': grpc.unary_unary_rpc_method_handler(
                    servicer.ModelReady,
                    request_deserializer=grpc__service__pb2.ModelReadyRequest.FromString,
                    response_serializer=grpc__service__pb2.ModelReadyResponse.SerializeToString,
            ),
            'ServerMetadata': grpc.unary_unary_rpc_method_handler(
                    servicer.ServerMetadata,
                    request_deserializer=grpc__service__pb2.ServerMetadataRequest.FromString,
                    response_serializer=grpc__service__pb2.ServerMetadataResponse.SerializeToString,
            ),
            'ModelMetadata': grpc.unary_unary_rpc_method_handler(
                    servicer.ModelMetadata,
                    request_deserializer=grpc__service__pb2.ModelMetadataRequest.FromString,
                    response_serializer=grpc__service__pb2.ModelMetadataResponse.SerializeToString,
            ),
            'ModelInfer': grpc.unary_unary_rpc_method_handler(
                    servicer.ModelInfer,
                    request_deserializer=grpc__service__pb2.ModelInferRequest.FromString,
                    response_serializer=grpc__service__pb2.ModelInferResponse.SerializeToString,
            ),
            'ModelStreamInfer': grpc.stream_stream_rpc_method_handler(
                    servicer.ModelStreamInfer,
                    request_deserializer=grpc__service__pb2.ModelInferRequest.FromString,
                    response_serializer=grpc__service__pb2.ModelStreamInferResponse.SerializeToString,
            ),
            'ModelConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.ModelConfig,
                    request_deserializer=grpc__service__pb2.ModelConfigRequest.FromString,
                    response_serializer=grpc__service__pb2.ModelConfigResponse.SerializeToString,
            ),
            'ModelStatistics': grpc.unary_unary_rpc_method_handler(
                    servicer.ModelStatistics,
                    request_deserializer=grpc__service__pb2.ModelStatisticsRequest.FromString,
                    response_serializer=grpc__service__pb2.ModelStatisticsResponse.SerializeToString,
            ),
            'RepositoryIndex': grpc.unary_unary_rpc_method_handler(
                    servicer.RepositoryIndex,
                    request_deserializer=grpc__service__pb2.RepositoryIndexRequest.FromString,
                    response_serializer=grpc__service__pb2.RepositoryIndexResponse.SerializeToString,
            ),
            'RepositoryModelLoad': grpc.unary_unary_rpc_method_handler(
                    servicer.RepositoryModelLoad,
                    request_deserializer=grpc__service__pb2.RepositoryModelLoadRequest.FromString,
                    response_serializer=grpc__service__pb2.RepositoryModelLoadResponse.SerializeToString,
            ),
            'RepositoryModelUnload': grpc.unary_unary_rpc_method_handler(
                    servicer.RepositoryModelUnload,
                    request_deserializer=grpc__service__pb2.RepositoryModelUnloadRequest.FromString,
                    response_serializer=grpc__service__pb2.RepositoryModelUnloadResponse.SerializeToString,
            ),
            'SystemSharedMemoryStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.SystemSharedMemoryStatus,
                    request_deserializer=grpc__service__pb2.SystemSharedMemoryStatusRequest.FromString,
                    response_serializer=grpc__service__pb2.SystemSharedMemoryStatusResponse.SerializeToString,
            ),
            'SystemSharedMemoryRegister': grpc.unary_unary_rpc_method_handler(
                    servicer.SystemSharedMemoryRegister,
                    request_deserializer=grpc__service__pb2.SystemSharedMemoryRegisterRequest.FromString,
                    response_serializer=grpc__service__pb2.SystemSharedMemoryRegisterResponse.SerializeToString,
            ),
            'SystemSharedMemoryUnregister': grpc.unary_unary_rpc_method_handler(
                    servicer.SystemSharedMemoryUnregister,
                    request_deserializer=grpc__service__pb2.SystemSharedMemoryUnregisterRequest.FromString,
                    response_serializer=grpc__service__pb2.SystemSharedMemoryUnregisterResponse.SerializeToString,
            ),
            'CudaSharedMemoryStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.CudaSharedMemoryStatus,
                    request_deserializer=grpc__service__pb2.CudaSharedMemoryStatusRequest.FromString,
                    response_serializer=grpc__service__pb2.CudaSharedMemoryStatusResponse.SerializeToString,
            ),
            'CudaSharedMemoryRegister': grpc.unary_unary_rpc_method_handler(
                    servicer.CudaSharedMemoryRegister,
                    request_deserializer=grpc__service__pb2.CudaSharedMemoryRegisterRequest.FromString,
                    response_serializer=grpc__service__pb2.CudaSharedMemoryRegisterResponse.SerializeToString,
            ),
            'CudaSharedMemoryUnregister': grpc.unary_unary_rpc_method_handler(
                    servicer.CudaSharedMemoryUnregister,
                    request_deserializer=grpc__service__pb2.CudaSharedMemoryUnregisterRequest.FromString,
                    response_serializer=grpc__service__pb2.CudaSharedMemoryUnregisterResponse.SerializeToString,
            ),
            'TraceSetting': grpc.unary_unary_rpc_method_handler(
                    servicer.TraceSetting,
                    request_deserializer=grpc__service__pb2.TraceSettingRequest.FromString,
                    response_serializer=grpc__service__pb2.TraceSettingResponse.SerializeToString,
            ),
            'LogSettings': grpc.unary_unary_rpc_method_handler(
                    servicer.LogSettings,
                    request_deserializer=grpc__service__pb2.LogSettingsRequest.FromString,
                    response_serializer=grpc__service__pb2.LogSettingsResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'inference.GRPCInferenceService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('inference.GRPCInferenceService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class GRPCInferenceService(object):
    """@@
    @@.. cpp:var:: service InferenceService
    @@
    @@   Inference Server GRPC endpoints.
    @@
    """

    @staticmethod
    def ServerLive(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/inference.GRPCInferenceService/ServerLive',
            grpc__service__pb2.ServerLiveRequest.SerializeToString,
            grpc__service__pb2.ServerLiveResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ServerReady(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/inference.GRPCInferenceService/ServerReady',
            grpc__service__pb2.ServerReadyRequest.SerializeToString,
            grpc__service__pb2.ServerReadyResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ModelReady(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/inference.GRPCInferenceService/ModelReady',
            grpc__service__pb2.ModelReadyRequest.SerializeToString,
            grpc__service__pb2.ModelReadyResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ServerMetadata(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/inference.GRPCInferenceService/ServerMetadata',
            grpc__service__pb2.ServerMetadataRequest.SerializeToString,
            grpc__service__pb2.ServerMetadataResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ModelMetadata(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/inference.GRPCInferenceService/ModelMetadata',
            grpc__service__pb2.ModelMetadataRequest.SerializeToString,
            grpc__service__pb2.ModelMetadataResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ModelInfer(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/inference.GRPCInferenceService/ModelInfer',
            grpc__service__pb2.ModelInferRequest.SerializeToString,
            grpc__service__pb2.ModelInferResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ModelStreamInfer(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_stream(
            request_iterator,
            target,
            '/inference.GRPCInferenceService/ModelStreamInfer',
            grpc__service__pb2.ModelInferRequest.SerializeToString,
            grpc__service__pb2.ModelStreamInferResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ModelConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/inference.GRPCInferenceService/ModelConfig',
            grpc__service__pb2.ModelConfigRequest.SerializeToString,
            grpc__service__pb2.ModelConfigResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ModelStatistics(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/inference.GRPCInferenceService/ModelStatistics',
            grpc__service__pb2.ModelStatisticsRequest.SerializeToString,
            grpc__service__pb2.ModelStatisticsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def RepositoryIndex(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/inference.GRPCInferenceService/RepositoryIndex',
            grpc__service__pb2.RepositoryIndexRequest.SerializeToString,
            grpc__service__pb2.RepositoryIndexResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def RepositoryModelLoad(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/inference.GRPCInferenceService/RepositoryModelLoad',
            grpc__service__pb2.RepositoryModelLoadRequest.SerializeToString,
            grpc__service__pb2.RepositoryModelLoadResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def RepositoryModelUnload(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/inference.GRPCInferenceService/RepositoryModelUnload',
            grpc__service__pb2.RepositoryModelUnloadRequest.SerializeToString,
            grpc__service__pb2.RepositoryModelUnloadResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SystemSharedMemoryStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/inference.GRPCInferenceService/SystemSharedMemoryStatus',
            grpc__service__pb2.SystemSharedMemoryStatusRequest.SerializeToString,
            grpc__service__pb2.SystemSharedMemoryStatusResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SystemSharedMemoryRegister(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/inference.GRPCInferenceService/SystemSharedMemoryRegister',
            grpc__service__pb2.SystemSharedMemoryRegisterRequest.SerializeToString,
            grpc__service__pb2.SystemSharedMemoryRegisterResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SystemSharedMemoryUnregister(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/inference.GRPCInferenceService/SystemSharedMemoryUnregister',
            grpc__service__pb2.SystemSharedMemoryUnregisterRequest.SerializeToString,
            grpc__service__pb2.SystemSharedMemoryUnregisterResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CudaSharedMemoryStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/inference.GRPCInferenceService/CudaSharedMemoryStatus',
            grpc__service__pb2.CudaSharedMemoryStatusRequest.SerializeToString,
            grpc__service__pb2.CudaSharedMemoryStatusResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CudaSharedMemoryRegister(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/inference.GRPCInferenceService/CudaSharedMemoryRegister',
            grpc__service__pb2.CudaSharedMemoryRegisterRequest.SerializeToString,
            grpc__service__pb2.CudaSharedMemoryRegisterResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CudaSharedMemoryUnregister(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/inference.GRPCInferenceService/CudaSharedMemoryUnregister',
            grpc__service__pb2.CudaSharedMemoryUnregisterRequest.SerializeToString,
            grpc__service__pb2.CudaSharedMemoryUnregisterResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def TraceSetting(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/inference.GRPCInferenceService/TraceSetting',
            grpc__service__pb2.TraceSettingRequest.SerializeToString,
            grpc__service__pb2.TraceSettingResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def LogSettings(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/inference.GRPCInferenceService/LogSettings',
            grpc__service__pb2.LogSettingsRequest.SerializeToString,
            grpc__service__pb2.LogSettingsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
