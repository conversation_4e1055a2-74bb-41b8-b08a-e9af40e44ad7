const { app, BrowserWindow, shell, dialog } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');

// Keep a global reference of the window object
let mainWindow;
let serverProcess;

const isDev = process.env.NODE_ENV === 'development';
const serverPort = 5000;

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1920,
    height: 1080,
    minWidth: 1600,
    minHeight: 900,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: true
    },
    icon: path.join(__dirname, '../assets/icon.png'), // We'll create this
    title: '3D AI Studio',
    show: false, // Don't show until ready
    titleBarStyle: 'default',
    autoHideMenuBar: false
  });

  // Set up the menu
  const { Menu } = require('electron');
  const template = [
    {
      label: 'File',
      submenu: [
        {
          label: 'New Project',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow.webContents.reload();
          }
        },
        { type: 'separator' },
        {
          label: 'Exit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Help',
      submenu: [
        {
          label: 'About 3D AI Studio',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'About 3D AI Studio',
              message: '3D AI Studio',
              detail: 'AI-powered 3D model generation from images\nPowered by Microsoft Trellis\n\nVersion 1.0.0'
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);

  // Load the app
  if (isDev) {
    mainWindow.loadURL(`http://localhost:${serverPort}`);
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadURL(`http://localhost:${serverPort}`);
  }

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();

    // Focus on the window
    if (isDev) {
      mainWindow.focus();
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });
}

function startServer() {
  return new Promise((resolve, reject) => {
    // Get the path to the Python app
    const appPath = path.join(__dirname, '..', '..', '..');
    const pythonPath = path.join(appPath, '.venv', 'Scripts', 'python.exe');
    const appScript = path.join(appPath, 'app.py');

    console.log('Starting server...');
    console.log('App path:', appPath);
    console.log('Python path:', pythonPath);
    console.log('App script:', appScript);

    // Check if virtual environment exists
    if (!fs.existsSync(pythonPath)) {
      reject(new Error('Virtual environment not found. Please run setup first.'));
      return;
    }

    // Start the Flask server
    serverProcess = spawn(pythonPath, [appScript], {
      cwd: appPath,
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let serverReady = false;

    serverProcess.stdout.on('data', (data) => {
      const output = data.toString();
      console.log('Server:', output);

      // Check if server is ready - look for the specific Flask startup message
      if ((output.includes('Running on http://127.0.0.1:5000') ||
           output.includes('* Running on http://127.0.0.1:5000') ||
           output.includes('Press CTRL+C to quit')) && !serverReady) {
        console.log('Server detected as ready!');
        serverReady = true;
        setTimeout(resolve, 500); // Give server a moment to fully start
      }
    });

    serverProcess.stderr.on('data', (data) => {
      const output = data.toString();
      console.log('Server stderr:', output);

      // Sometimes Flask outputs to stderr, check there too
      if ((output.includes('Running on http://127.0.0.1:5000') ||
           output.includes('* Running on http://127.0.0.1:5000')) && !serverReady) {
        console.log('Server detected as ready via stderr!');
        serverReady = true;
        setTimeout(resolve, 500);
      }
    });

    serverProcess.on('close', (code) => {
      console.log(`Server process exited with code ${code}`);
      if (!serverReady) {
        reject(new Error(`Server failed to start (exit code: ${code})`));
      }
    });

    serverProcess.on('error', (error) => {
      console.error('Failed to start server:', error);
      reject(error);
    });

    // Fallback: Try to connect to server every 2 seconds
    const checkServer = async () => {
      try {
        const http = require('http');
        const req = http.request({
          hostname: '127.0.0.1',
          port: serverPort,
          path: '/status',
          method: 'GET',
          timeout: 1000
        }, (res) => {
          if (res.statusCode === 200 && !serverReady) {
            console.log('Server detected via HTTP check!');
            serverReady = true;
            resolve();
          }
        });

        req.on('error', () => {
          // Server not ready yet, ignore
        });

        req.end();
      } catch (error) {
        // Ignore connection errors
      }
    };

    // Check server every 2 seconds
    const serverCheckInterval = setInterval(() => {
      if (!serverReady) {
        checkServer();
      } else {
        clearInterval(serverCheckInterval);
      }
    }, 2000);

    // Timeout after 10 minutes (Hunyuan3D-2 texture pipeline loading can take 5+ minutes)
    setTimeout(() => {
      clearInterval(serverCheckInterval);
      if (!serverReady) {
        reject(new Error('Server startup timeout'));
      }
    }, 600000); // 10 minutes
  });
}

function stopServer() {
  if (serverProcess) {
    console.log('Stopping server...');
    serverProcess.kill();
    serverProcess = null;
  }
}

// App event handlers
app.whenReady().then(async () => {
  try {
    console.log('Starting 3D AI Studio...');

    // Check if server is already running
    const http = require('http');
    const checkExistingServer = () => {
      return new Promise((resolve) => {
        const req = http.request({
          hostname: '127.0.0.1',
          port: serverPort,
          path: '/status',
          method: 'GET',
          timeout: 2000
        }, (res) => {
          if (res.statusCode === 200) {
            console.log('Server already running, connecting...');
            resolve(true);
          } else {
            resolve(false);
          }
        });

        req.on('error', () => {
          resolve(false);
        });

        req.end();
      });
    };

    const serverRunning = await checkExistingServer();

    if (!serverRunning) {
      console.log('Starting new server...');
      await startServer();
    }

    createWindow();
  } catch (error) {
    console.error('Failed to start application:', error);
    dialog.showErrorBox('Startup Error',
      `Failed to start 3D AI Studio:\n\n${error.message}\n\nPlease ensure the application is properly installed.`);
    app.quit();
  }
});

app.on('window-all-closed', () => {
  stopServer();
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

app.on('before-quit', () => {
  stopServer();
});

// Handle app quit
process.on('SIGINT', () => {
  stopServer();
  app.quit();
});

process.on('SIGTERM', () => {
  stopServer();
  app.quit();
});
