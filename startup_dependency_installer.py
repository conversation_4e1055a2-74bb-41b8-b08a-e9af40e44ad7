#!/usr/bin/env python3
"""
Startup Dependency Installer
Automatically installs critical dependencies at startup to prevent import errors
"""

import sys
import subprocess
import os
from pathlib import Path

def install_critical_dependencies():
    """Install critical dependencies that are needed at startup."""
    
    print("[STARTUP] Checking critical dependencies...", flush=True)
    
    # Get the virtual environment pip
    app_root = Path(__file__).parent
    venv_path = app_root / ".venv"
    
    if os.name == 'nt':  # Windows
        pip_exe = venv_path / "Scripts" / "pip.exe"
        python_exe = venv_path / "Scripts" / "python.exe"
    else:  # Linux/Mac
        pip_exe = venv_path / "bin" / "pip"
        python_exe = venv_path / "bin" / "python"
    
    if not pip_exe.exists():
        print(f"[STARTUP] Warning: Pip not found at {pip_exe}", flush=True)
        return False
    
    # Critical dependencies that should always be available
    critical_deps = [
        'imageio',
        'Pillow',
        'numpy',
        'requests',
        'flask',
        'flask-cors'
    ]

    # Optional but useful dependencies
    optional_deps = [
        'rembg'  # Background removal tool
    ]
    
    installed_count = 0
    
    for dep in critical_deps:
        try:
            # Check if already installed
            result = subprocess.run([
                str(python_exe), '-c', f'import {dep.lower().replace("-", "_")}; print("OK")'
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print(f"[STARTUP] {dep}: Already available", flush=True)
                installed_count += 1
                continue
                
        except Exception:
            pass
        
        # Install if not available
        try:
            print(f"[STARTUP] Installing {dep}...", flush=True)
            result = subprocess.run([
                str(pip_exe), 'install', dep, '--quiet'
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                print(f"[STARTUP] {dep}: Installed successfully", flush=True)
                installed_count += 1
            else:
                print(f"[STARTUP] {dep}: Installation failed - {result.stderr}", flush=True)
                
        except Exception as e:
            print(f"[STARTUP] {dep}: Installation error - {e}", flush=True)
    
    print(f"[STARTUP] Critical dependencies check complete: {installed_count}/{len(critical_deps)} available", flush=True)
    return installed_count >= len(critical_deps) // 2  # Success if more than half available

def fix_pil_imports():
    """Fix common PIL import issues."""
    try:
        # Test PIL import
        import PIL
        from PIL import Image
        print("[STARTUP] PIL imports working correctly", flush=True)
        return True
    except ImportError as e:
        print(f"[STARTUP] PIL import issue detected: {e}", flush=True)
        
        # Try to install/reinstall Pillow
        app_root = Path(__file__).parent
        venv_path = app_root / ".venv"
        
        if os.name == 'nt':  # Windows
            pip_exe = venv_path / "Scripts" / "pip.exe"
        else:  # Linux/Mac
            pip_exe = venv_path / "bin" / "pip"
        
        try:
            print("[STARTUP] Reinstalling Pillow to fix PIL imports...", flush=True)
            result = subprocess.run([
                str(pip_exe), 'install', '--upgrade', '--force-reinstall', 'Pillow'
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                print("[STARTUP] Pillow reinstalled successfully", flush=True)
                return True
            else:
                print(f"[STARTUP] Pillow reinstall failed: {result.stderr}", flush=True)
                return False
                
        except Exception as e:
            print(f"[STARTUP] Error reinstalling Pillow: {e}", flush=True)
            return False

def install_imageio_redundancy():
    """Install imageio in both main venv and ensure it's available."""
    try:
        # Test imageio import
        import imageio
        print("[STARTUP] ImageIO already available", flush=True)
        return True
    except ImportError:
        print("[STARTUP] ImageIO not available, installing...", flush=True)
        
        app_root = Path(__file__).parent
        venv_path = app_root / ".venv"
        
        if os.name == 'nt':  # Windows
            pip_exe = venv_path / "Scripts" / "pip.exe"
        else:  # Linux/Mac
            pip_exe = venv_path / "bin" / "pip"
        
        try:
            # Install imageio and imageio-ffmpeg
            for package in ['imageio', 'imageio-ffmpeg']:
                result = subprocess.run([
                    str(pip_exe), 'install', package, '--upgrade'
                ], capture_output=True, text=True, timeout=120)
                
                if result.returncode == 0:
                    print(f"[STARTUP] {package} installed successfully", flush=True)
                else:
                    print(f"[STARTUP] {package} installation failed: {result.stderr}", flush=True)
            
            # Test import again
            import imageio
            print("[STARTUP] ImageIO now available", flush=True)
            return True
            
        except Exception as e:
            print(f"[STARTUP] Error installing ImageIO: {e}", flush=True)
            return False

def check_hunyuan3d_setup():
    """Check and setup Hunyuan3D-2 direct pipeline."""
    try:
        print("[STARTUP] Checking Hunyuan3D-2 setup...", flush=True)

        from hunyuan3d_direct_pipeline import get_hunyuan3d_direct_pipeline

        pipeline = get_hunyuan3d_direct_pipeline()

        # Check if initial setup is needed
        if not pipeline.check_initial_setup():
            print("[STARTUP] Hunyuan3D-2 initial setup required...", flush=True)

            def progress_callback(message):
                print(f"[STARTUP] {message}", flush=True)

            if pipeline.run_initial_setup(progress_callback):
                print("[STARTUP] Hunyuan3D-2 initial setup completed", flush=True)
                return True
            else:
                print("[STARTUP] Hunyuan3D-2 initial setup failed", flush=True)
                return False
        else:
            print("[STARTUP] Hunyuan3D-2 initial setup already completed", flush=True)
            return True

    except Exception as e:
        print(f"[STARTUP] Error checking Hunyuan3D-2 setup: {e}", flush=True)
        return False

def run_startup_checks():
    """Run all startup dependency checks."""
    print("=" * 60, flush=True)
    print("STARTUP DEPENDENCY CHECKS", flush=True)
    print("=" * 60, flush=True)
    
    success_count = 0
    
    # Install critical dependencies
    if install_critical_dependencies():
        success_count += 1
    
    # Fix PIL imports
    if fix_pil_imports():
        success_count += 1
    
    # Ensure imageio redundancy
    if install_imageio_redundancy():
        success_count += 1

    # Check Hunyuan3D-2 setup
    if check_hunyuan3d_setup():
        success_count += 1

    print("=" * 60, flush=True)
    print(f"STARTUP CHECKS COMPLETE: {success_count}/4 successful", flush=True)
    print("=" * 60, flush=True)

    return success_count >= 3  # Success if at least 3/4 checks pass

if __name__ == "__main__":
    success = run_startup_checks()
    sys.exit(0 if success else 1)
