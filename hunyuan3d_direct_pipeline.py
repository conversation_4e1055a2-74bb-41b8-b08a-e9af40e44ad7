"""
Hunyuan3D-2 Direct Pipeline Integration
Loads Hunyuan3D-2 models directly into the application environment
"""

import os
import sys
import time
import subprocess
import torch
import trimesh
from pathlib import Path
from typing import Dict, Any, Optional, Callable
import tempfile
import uuid

# Set up logging
try:
    from utils.logging_system import get_logger
    logger = get_logger()
except ImportError:
    import logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

class Hunyuan3DDirectPipeline:
    """Direct integration of Hunyuan3D-2 pipeline into the application."""
    
    def __init__(self):
        self.winportable_path = Path("Resources/Hunyuan3D2_WinPortable")
        self.hunyuan3d_path = self.winportable_path / "Hunyuan3D-2"
        self.python_path = self.winportable_path / "python_standalone" / "python.exe"
        
        # Pipeline components
        self.i23d_worker = None
        self.texgen_worker = None
        self.t2i_worker = None
        self.rmbg_worker = None
        self.face_reduce_worker = None
        self.floater_remove_worker = None
        self.degenerate_face_remove_worker = None
        
        # Pipeline status
        self.is_initialized = False
        self.has_texturegen = False
        self.has_t2i = False
        
        # Configuration
        self.model_path = "tencent/Hunyuan3D-2mini"
        self.subfolder = "hunyuan3d-dit-v2-mini-turbo"
        self.texgen_model_path = "tencent/Hunyuan3D-2"
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.enable_flashvdm = True
        self.low_vram_mode = True
        
    def check_initial_setup(self) -> bool:
        """Check if initial setup (compilation and model download) has been completed."""
        try:
            # Check if texture generation components are compiled
            custom_rasterizer_path = self.hunyuan3d_path / "hy3dgen" / "texgen" / "custom_rasterizer"
            differentiable_renderer_path = self.hunyuan3d_path / "hy3dgen" / "texgen" / "differentiable_renderer"
            
            # Check if compiled files exist
            compiled_files = [
                custom_rasterizer_path / "build",
                differentiable_renderer_path / "build",
                self.hunyuan3d_path / "hy3dgen" / "texgen" / "differentiable_renderer" / "mesh_processor.cp312-win_amd64.pyd"
            ]
            
            compilation_complete = all(path.exists() for path in compiled_files)
            
            # Check if models are downloaded
            hf_cache_path = self.winportable_path / "HuggingFaceHub"
            models_downloaded = (hf_cache_path / "models--tencent--Hunyuan3D-2mini").exists()
            
            logger.info(f"Initial setup status - Compilation: {compilation_complete}, Models: {models_downloaded}", 
                       component="HUNYUAN3D_DIRECT")
            
            return compilation_complete and models_downloaded
            
        except Exception as e:
            logger.error(f"Error checking initial setup: {e}", component="HUNYUAN3D_DIRECT")
            return False
    
    def run_initial_setup(self, progress_callback: Optional[Callable] = None) -> bool:
        """Run the initial setup scripts if needed."""
        try:
            if self.check_initial_setup():
                logger.info("Initial setup already completed", component="HUNYUAN3D_DIRECT")
                return True
            
            logger.info("Running initial setup for Hunyuan3D-2...", component="HUNYUAN3D_DIRECT")
            
            if progress_callback:
                progress_callback("Running initial Hunyuan3D-2 setup...")
            
            # Step 1: Compile and install texture generation components
            if progress_callback:
                progress_callback("Compiling texture generation components...")
            
            compile_script = self.winportable_path / "1-compile-install-texture-gen.bat"
            if compile_script.exists():
                logger.info("Running texture generation compilation...", component="HUNYUAN3D_DIRECT")
                result = subprocess.run(
                    [str(compile_script)],
                    cwd=str(self.winportable_path),
                    capture_output=True,
                    text=True,
                    timeout=1800  # 30 minutes timeout
                )
                
                if result.returncode != 0:
                    logger.error(f"Texture compilation failed: {result.stderr}", component="HUNYUAN3D_DIRECT")
                    return False
                
                logger.info("Texture generation compilation completed", component="HUNYUAN3D_DIRECT")
            
            # Step 2: Download models
            if progress_callback:
                progress_callback("Downloading Hunyuan3D-2 models...")
            
            download_script = self.winportable_path / "2-download-models.bat"
            if download_script.exists():
                logger.info("Downloading Hunyuan3D-2 models...", component="HUNYUAN3D_DIRECT")
                result = subprocess.run(
                    [str(download_script)],
                    cwd=str(self.winportable_path),
                    capture_output=True,
                    text=True,
                    timeout=3600  # 60 minutes timeout
                )
                
                if result.returncode != 0:
                    logger.error(f"Model download failed: {result.stderr}", component="HUNYUAN3D_DIRECT")
                    return False
                
                logger.info("Model download completed", component="HUNYUAN3D_DIRECT")
            
            # Verify setup completion
            setup_complete = self.check_initial_setup()
            if setup_complete:
                logger.info("Initial setup completed successfully", component="HUNYUAN3D_DIRECT")
            else:
                logger.error("Initial setup verification failed", component="HUNYUAN3D_DIRECT")
            
            return setup_complete
            
        except subprocess.TimeoutExpired:
            logger.error("Initial setup timed out", component="HUNYUAN3D_DIRECT")
            return False
        except Exception as e:
            logger.error(f"Error during initial setup: {e}", component="HUNYUAN3D_DIRECT")
            return False
    
    def setup_environment(self):
        """Setup the Python environment for Hunyuan3D-2."""
        try:
            # Add Hunyuan3D-2 path to Python path
            hunyuan3d_str = str(self.hunyuan3d_path)
            if hunyuan3d_str not in sys.path:
                sys.path.insert(0, hunyuan3d_str)
            
            # Set environment variables
            os.environ['HF_HUB_CACHE'] = str(self.winportable_path / "HuggingFaceHub")
            os.environ['HY3DGEN_MODELS'] = str(self.winportable_path / "HuggingFaceHub")
            os.environ['PYTHONPYCACHEPREFIX'] = str(self.winportable_path / "pycache")
            
            logger.info("Hunyuan3D-2 environment setup completed", component="HUNYUAN3D_DIRECT")
            return True
            
        except Exception as e:
            logger.error(f"Error setting up environment: {e}", component="HUNYUAN3D_DIRECT")
            return False
    
    def initialize_pipelines(self, progress_callback: Optional[Callable] = None) -> bool:
        """Initialize all Hunyuan3D-2 pipeline components."""
        try:
            if progress_callback:
                progress_callback("Initializing Hunyuan3D-2 pipelines...")
            
            logger.info("Initializing Hunyuan3D-2 pipelines...", component="HUNYUAN3D_DIRECT")
            
            # Setup environment
            if not self.setup_environment():
                return False
            
            # Import Hunyuan3D modules
            if progress_callback:
                progress_callback("Loading Hunyuan3D-2 modules...")
            
            from hy3dgen.shapegen import (
                FaceReducer, FloaterRemover, DegenerateFaceRemover, 
                Hunyuan3DDiTFlowMatchingPipeline
            )
            from hy3dgen.rembg import BackgroundRemover
            
            # Initialize background removal
            if progress_callback:
                progress_callback("Initializing background removal...")
            
            self.rmbg_worker = BackgroundRemover()
            logger.info("Background removal worker initialized", component="HUNYUAN3D_DIRECT")
            
            # Initialize shape generation pipeline
            if progress_callback:
                progress_callback("Loading shape generation pipeline...")
            
            torch.set_default_device("cpu")
            self.i23d_worker = Hunyuan3DDiTFlowMatchingPipeline.from_pretrained(
                self.model_path,
                subfolder=self.subfolder,
                use_safetensors=True,
                device=self.device,
            )
            
            if self.enable_flashvdm:
                mc_algo = 'mc' if self.device in ['cpu', 'mps'] else 'dmc'
                self.i23d_worker.enable_flashvdm(mc_algo=mc_algo)
            
            logger.info("Shape generation pipeline initialized", component="HUNYUAN3D_DIRECT")
            
            # Initialize mesh processing workers
            if progress_callback:
                progress_callback("Initializing mesh processing...")
            
            self.floater_remove_worker = FloaterRemover()
            self.degenerate_face_remove_worker = DegenerateFaceRemover()
            self.face_reduce_worker = FaceReducer()
            
            logger.info("Mesh processing workers initialized", component="HUNYUAN3D_DIRECT")
            
            # Initialize texture generation (optional)
            try:
                if progress_callback:
                    progress_callback("Loading texture generation pipeline...")
                
                from hy3dgen.texgen import Hunyuan3DPaintPipeline
                
                self.texgen_worker = Hunyuan3DPaintPipeline.from_pretrained(self.texgen_model_path)
                if self.low_vram_mode:
                    self.texgen_worker.enable_model_cpu_offload()
                
                self.has_texturegen = True
                logger.info("Texture generation pipeline initialized", component="HUNYUAN3D_DIRECT")
                
            except Exception as e:
                logger.warning(f"Failed to load texture generation pipeline: {e}", component="HUNYUAN3D_DIRECT")
                self.has_texturegen = False
            
            # Initialize text-to-image (optional)
            try:
                if progress_callback:
                    progress_callback("Loading text-to-image pipeline...")
                
                from hy3dgen.text2image import HunyuanDiTPipeline
                self.t2i_worker = HunyuanDiTPipeline('Tencent-Hunyuan/HunyuanDiT-v1.1-Diffusers-Distilled')
                self.has_t2i = True
                logger.info("Text-to-image pipeline initialized", component="HUNYUAN3D_DIRECT")
                
            except Exception as e:
                logger.warning(f"Failed to load text-to-image pipeline: {e}", component="HUNYUAN3D_DIRECT")
                self.has_t2i = False
            
            # Setup memory optimization
            if self.low_vram_mode and torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            self.is_initialized = True
            logger.info("All Hunyuan3D-2 pipelines initialized successfully", component="HUNYUAN3D_DIRECT")
            
            if progress_callback:
                progress_callback("Hunyuan3D-2 initialization complete")
            
            return True
            
        except Exception as e:
            logger.error(f"Error initializing pipelines: {e}", component="HUNYUAN3D_DIRECT")
            return False
    
    def is_available(self) -> bool:
        """Check if the pipeline is available for use."""
        return self.is_initialized and self.i23d_worker is not None
    
    def generate_image_to_3d(self, image_path: str, settings: Dict[str, Any] = None, 
                           progress_callback: Optional[Callable] = None, 
                           session_id: Optional[str] = None) -> Dict[str, Any]:
        """Generate 3D model from image using direct pipeline integration."""
        try:
            if not self.is_available():
                return {
                    'success': False,
                    'error': 'Hunyuan3D-2 pipeline not initialized'
                }
            
            logger.info(f"Starting direct image-to-3D generation: {image_path}", component="HUNYUAN3D_DIRECT")
            
            # Default settings
            default_settings = {
                'seed': 1234,
                'octree_resolution': 128,
                'num_inference_steps': 5,
                'guidance_scale': 5.0,
                'enable_texture': True,
                'face_count': 40000,
                'randomize_seed': False
            }
            
            if settings:
                default_settings.update(settings)
            
            start_time = time.time()
            
            # Load and process image
            if progress_callback:
                progress_callback("Loading and processing image...")
            
            from PIL import Image
            image = Image.open(image_path).convert('RGBA')
            
            # Remove background if needed
            if progress_callback:
                progress_callback("Removing background...")
            
            image = self.rmbg_worker(image.convert('RGB'))
            
            # Generate 3D shape
            if progress_callback:
                progress_callback("Generating 3D shape...")
            
            generator = torch.Generator()
            if default_settings.get('randomize_seed', False):
                import random
                seed = random.randint(0, 10000000)
            else:
                seed = default_settings.get('seed', 1234)
            
            generator = generator.manual_seed(int(seed))
            
            outputs = self.i23d_worker(
                image=image,
                num_inference_steps=default_settings.get('num_inference_steps', 5),
                guidance_scale=default_settings.get('guidance_scale', 5.0),
                generator=generator,
                octree_resolution=default_settings.get('octree_resolution', 128),
                output_type='mesh'
            )
            
            # Export to trimesh
            from hy3dgen.shapegen.pipelines import export_to_trimesh
            mesh = export_to_trimesh(outputs)[0]
            
            # Post-process mesh
            if progress_callback:
                progress_callback("Post-processing mesh...")
            
            mesh = self.face_reduce_worker(mesh, max_facenum=default_settings.get('face_count', 40000))
            
            # Apply texture if enabled
            if default_settings.get('enable_texture', False) and self.has_texturegen:
                if progress_callback:
                    progress_callback("Generating texture...")
                
                mesh = self.texgen_worker(mesh, image)
            
            # Save mesh
            output_dir = Path("outputs") / f"hunyuan3d_{session_id or uuid.uuid4().hex[:8]}"
            output_dir.mkdir(parents=True, exist_ok=True)
            
            output_path = output_dir / "model.glb"
            mesh.export(str(output_path))
            
            generation_time = time.time() - start_time
            
            logger.info(f"Direct generation completed in {generation_time:.2f}s", component="HUNYUAN3D_DIRECT")
            
            return {
                'success': True,
                'output_path': str(output_path),
                'generation_time': generation_time,
                'mesh_stats': {
                    'vertices': len(mesh.vertices),
                    'faces': len(mesh.faces),
                    'has_texture': default_settings.get('enable_texture', False) and self.has_texturegen
                }
            }
            
        except Exception as e:
            logger.error(f"Error in direct image-to-3D generation: {e}", component="HUNYUAN3D_DIRECT")
            return {
                'success': False,
                'error': f'Generation failed: {str(e)}'
            }

# Global pipeline instance
hunyuan3d_direct_pipeline = None

def get_hunyuan3d_direct_pipeline() -> Hunyuan3DDirectPipeline:
    """Get the global Hunyuan3D direct pipeline instance."""
    global hunyuan3d_direct_pipeline
    if hunyuan3d_direct_pipeline is None:
        hunyuan3d_direct_pipeline = Hunyuan3DDirectPipeline()
    return hunyuan3d_direct_pipeline
