"""
Trellis 2D to 3D Pipeline Implementation

This module implements the core functionality for converting 2D images to 3D models
using Microsoft Trellis. It provides a modular interface that can be tested independently
of the main application.
"""

import os
from typing import Dict
from pathlib import Path
import sys
import imageio
import uuid
import subprocess
import json
import tempfile
import time

# Import logging system
try:
    from utils.logging_system import get_logger
    logger = get_logger()
except ImportError:
    # Fallback if logging system is not available
    class DummyLogger:
        def info(self, *args, **kwargs): pass
        def debug(self, *args, **kwargs): pass
        def warning(self, *args, **kwargs): pass
        def error(self, *args, **kwargs): pass
        def log_pipeline_start(self, *args, **kwargs): pass
        def log_pipeline_progress(self, *args, **kwargs): pass
        def log_pipeline_complete(self, *args, **kwargs): pass
        def log_performance(self, *args, **kwargs): pass
        def set_session_id(self, *args, **kwargs): pass
        def log_exception(self, *args, **kwargs): pass
    logger = DummyLogger()

# Set environment variables for optimal performance
os.environ['SPCONV_ALGO'] = 'native'  # Recommended for single runs

# Add TRELLIS to Python path - use the app directory with full environment
trellis_app_path = str(Path(__file__).parent.parent.parent / "Resources" / "TRELLIS" / "app")
trellis_official_path = str(Path(__file__).parent.parent.parent / "Resources" / "TRELLIS" / "trellis_official" / "TRELLIS")

# Add both paths to ensure we can import from the working environment
for path in [trellis_app_path, trellis_official_path]:
    if path not in sys.path:
        sys.path.insert(0, path)

# Try to use the existing Trellis environment
trellis_env_python = str(Path(__file__).parent.parent.parent / "Resources" / "TRELLIS" / "app" / "env" / "Scripts" / "python.exe")

try:
    from PIL import Image
    from trellis.pipelines import TrellisImageTo3DPipeline
    from trellis.utils import render_utils, postprocessing_utils
    TRELLIS_AVAILABLE = True
    print("Successfully imported Trellis modules")
except ImportError as e:
    print(f"Warning: Could not import Trellis modules: {e}")
    print(f"Trying to use Trellis environment at: {trellis_env_python}")
    TRELLIS_AVAILABLE = False


class TrellisPipeline:
    """
    A modular pipeline for converting 2D images to 3D models using Microsoft Trellis.

    This class provides a clean interface for:
    1. Initializing the Trellis environment
    2. Processing 2D images
    3. Generating 3D models
    4. Retrieving output files
    """

    def __init__(self, output_dir: str):
        """
        Initialize the Trellis pipeline.

        Args:
            output_dir: Directory where output files will be saved
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Initialize the Trellis pipeline
        self.pipeline = None
        self.render_utils = None
        self.postprocessing_utils = None

        if TRELLIS_AVAILABLE:
            try:
                print("Loading Trellis model (this may take a while)...")
                self.pipeline = TrellisImageTo3DPipeline.from_pretrained("JeffreyXiang/TRELLIS-image-large")

                # Check if CUDA is available and move to GPU
                try:
                    import torch
                    if torch.cuda.is_available():
                        self.pipeline.cuda()
                        print(f"Trellis pipeline loaded on GPU: {torch.cuda.get_device_name(0)}")
                    else:
                        print("CUDA not available, using CPU")
                except ImportError:
                    print("PyTorch not available, using CPU")

                self.render_utils = render_utils
                self.postprocessing_utils = postprocessing_utils
                print("Trellis pipeline initialized successfully!")

            except Exception as e:
                print(f"Error initializing Trellis pipeline: {e}")
                import traceback
                traceback.print_exc()
                self.pipeline = None

    def is_available(self) -> bool:
        """
        Check if Trellis is available and properly configured.

        Returns:
            True if Trellis is available, False otherwise
        """
        # Check if we have the pipeline loaded in the current environment
        if self.pipeline is not None and TRELLIS_AVAILABLE:
            return True

        # Check if we can use the Trellis environment via subprocess
        trellis_python = Path(__file__).parent.parent.parent / "Resources" / "TRELLIS" / "app" / "env" / "Scripts" / "python.exe"
        processor_script = Path(__file__).parent / "trellis_processor.py"

        return trellis_python.exists() and processor_script.exists()

    def process_image(self, image_path: str, settings: Dict = None, session_id: str = None) -> Dict[str, str]:
        """
        Process a 2D image and generate a 3D model using the official Trellis pipeline.

        Args:
            image_path: Path to the input 2D image
            settings: Optional settings dictionary with parameters like:
                - seed: Random seed for generation
                - ss_steps: Sparse structure sampling steps
                - ss_cfg_strength: Sparse structure guidance strength
                - slat_steps: SLAT sampling steps
                - slat_cfg_strength: SLAT guidance strength
                - simplify: Mesh simplification ratio
                - texture_size: Texture size for GLB export

        Returns:
            Dictionary containing paths to generated files:
            {
                'gaussian': path_to_gaussian_ply_file,
                'glb': path_to_glb_model,
                'video': path_to_animation_video
            }
        """
        # If pipeline is not loaded in current environment, use subprocess approach
        if self.pipeline is None or not TRELLIS_AVAILABLE:
            return self._process_with_trellis_env(image_path, settings, session_id)

        # Default settings
        if settings is None:
            settings = {}

        # Set up logging
        if session_id:
            logger.set_session_id(session_id)

        logger.log_pipeline_start("trellis_image_to_3d", {
            'image_path': image_path,
            'settings': settings,
            'session_id': session_id
        })

        try:
            logger.info(f"Processing image: {image_path}", component="TRELLIS")
            logger.debug(f"Settings: {settings}", component="TRELLIS")

            # Load the image
            from PIL import Image
            image = Image.open(image_path)
            print(f"Image loaded: {image.size}, mode: {image.mode}")

            # Run the Trellis pipeline exactly like the official example
            print("Running Trellis pipeline...")
            outputs = self.pipeline.run(
                image,
                seed=settings.get('seed', 1),
                sparse_structure_sampler_params={
                    "steps": settings.get('ss_steps', 12),
                    "cfg_strength": settings.get('ss_cfg_strength', 7.5),
                },
                slat_sampler_params={
                    "steps": settings.get('slat_steps', 12),
                    "cfg_strength": settings.get('slat_cfg_strength', 3.0),
                },
            )
            print("Pipeline execution completed successfully")

            # Generate unique output filenames
            output_id = str(uuid.uuid4())[:8]
            base_name = f"trellis_output_{output_id}"

            # Render videos
            print("Rendering videos...")
            gaussian_video = self.render_utils.render_video(outputs['gaussian'][0])['color']
            gaussian_video_path = self.output_dir / f"{base_name}_gaussian.mp4"
            imageio.mimsave(str(gaussian_video_path), gaussian_video, fps=30)

            # Save Gaussian as PLY
            print("Saving Gaussian PLY...")
            gaussian_ply_path = self.output_dir / f"{base_name}_gaussian.ply"
            outputs['gaussian'][0].save_ply(str(gaussian_ply_path))

            # Check if lighting optimizer is enabled
            enable_lighting_optimizer = settings.get('enable_lighting_optimizer', True)

            if enable_lighting_optimizer:
                print("Generating GLB file with lighting enhancements...")

                # Try to use enhanced post-processing
                try:
                    from enhanced_postprocessing import enhanced_to_glb

                    # Enhanced settings for better lighting
                    enhancement_settings = {
                        'enable_lighting_enhancement': True,
                        'ambient_boost': settings.get('ambient_boost', 0.25),
                        'shadow_softening': settings.get('shadow_softening', 0.3),
                        'gamma_correction': settings.get('gamma_correction', 1.15),
                        'material_brightness': settings.get('material_brightness', 0.15),
                        'contrast_enhancement': settings.get('contrast_enhancement', 1.05),
                        'saturation_boost': settings.get('saturation_boost', 1.02),
                    }

                    glb = enhanced_to_glb(
                        outputs['gaussian'][0],
                        outputs['mesh'][0],
                        simplify=settings.get('simplify', 0.95),
                        texture_size=settings.get('texture_size', 1024),
                        enhancement_settings=enhancement_settings,
                        verbose=True
                    )
                    print("✓ Enhanced lighting applied to 3D model")

                except ImportError as e:
                    print(f"Enhanced post-processing not available ({e}), using standard processing...")
                    glb = self.postprocessing_utils.to_glb(
                        outputs['gaussian'][0],
                        outputs['mesh'][0],
                        simplify=settings.get('simplify', 0.95),
                        texture_size=settings.get('texture_size', 1024),
                    )
            else:
                # Lighting optimizer disabled - use standard processing
                print("Generating GLB file with standard processing (lighting optimizer disabled)...")
                glb = self.postprocessing_utils.to_glb(
                    outputs['gaussian'][0],
                    outputs['mesh'][0],
                    simplify=settings.get('simplify', 0.95),
                    texture_size=settings.get('texture_size', 1024),
                )

            glb_path = self.output_dir / f"{base_name}.glb"
            glb.export(str(glb_path))

            print(f"Successfully generated all output files")

            return {
                'gaussian': str(gaussian_ply_path),
                'glb': str(glb_path),
                'video': str(gaussian_video_path)
            }

        except Exception as e:
            print(f"Error processing image: {str(e)}")
            import traceback
            traceback.print_exc()
            raise RuntimeError(f"Error processing image: {str(e)}")

    def _process_with_trellis_env(self, image_path: str, settings: Dict = None, session_id: str = None) -> Dict[str, str]:
        """
        Process image using the Trellis environment via subprocess.
        This is used when the main environment doesn't have all Trellis dependencies.
        """
        if settings is None:
            settings = {}

        try:
            # Path to the Trellis environment Python executable
            trellis_python = str(Path(__file__).parent.parent.parent / "Resources" / "TRELLIS" / "app" / "env" / "Scripts" / "python.exe")
            processor_script = str(Path(__file__).parent / "trellis_processor.py")

            # Create temporary file for results
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                result_file = f.name

            # Prepare command
            cmd = [
                trellis_python,
                processor_script,
                '--image_path', str(image_path),
                '--output_dir', str(self.output_dir),
                '--settings', json.dumps(settings),
                '--result_file', result_file
            ]

            print(f"Running Trellis processor: {' '.join(cmd)}")



            # Run the processor with real-time output monitoring
            print(f"Starting Trellis subprocess...")

            # Import update_progress function once at the beginning
            update_progress_func = None
            if session_id:
                try:
                    from progress_utils import update_progress
                    update_progress_func = update_progress
                    # Initialize preprocessing stage - don't mark as complete yet
                    update_progress(session_id, 'preprocessing', 0, 'Starting image preprocessing...')
                except ImportError as e:
                    print(f"Could not import update_progress: {e}")
                    update_progress_func = None

            # Start the subprocess with real-time output capture
            # Set environment variables for unbuffered output
            env = os.environ.copy()
            env['PYTHONUNBUFFERED'] = '1'
            env['PYTHONIOENCODING'] = 'utf-8'

            process = subprocess.Popen(cmd,
                                     stdout=subprocess.PIPE,
                                     stderr=subprocess.PIPE,
                                     text=True,
                                     bufsize=0,  # Unbuffered
                                     universal_newlines=True,
                                     encoding='utf-8',
                                     errors='replace',  # Replace invalid characters
                                     env=env)

            # Monitor output in real-time and update progress
            import threading

            output_lines = []
            error_lines = []
            sampling_stage_count = 0  # Track which sampling stage we're in (0=sparse, 1=slat)

            def read_stdout():
                while True:
                    try:
                        line = process.stdout.readline()
                        if not line:
                            break
                        output_lines.append(line.strip())
                        try:
                            print(f"Trellis STDOUT: {line.strip()}")
                        except UnicodeEncodeError:
                            print(f"Trellis STDOUT: [Unicode output - {len(line.strip())} chars]")
                        process_line(line.strip())
                    except UnicodeDecodeError as e:
                        print(f"Unicode decode error in stdout: {e}")
                        continue

            def read_stderr():
                while True:
                    try:
                        line = process.stderr.readline()
                        if not line:
                            break
                        error_lines.append(line.strip())
                        try:
                            print(f"Trellis STDERR: {line.strip()}")
                        except UnicodeEncodeError:
                            print(f"Trellis STDERR: [Unicode output - {len(line.strip())} chars]")
                        process_line(line.strip())
                    except UnicodeDecodeError as e:
                        print(f"Unicode decode error in stderr: {e}")
                        continue

            def process_line(line):
                nonlocal sampling_stage_count
                # Update progress based on output keywords
                try:
                    print(f"Processing line for progress: {line[:100]}...")  # Debug log
                except UnicodeEncodeError:
                    print(f"Processing line for progress: [Unicode output - {len(line)} chars]")  # Debug log
                if session_id and update_progress_func:
                    try:
                        import re

                        # Preprocessing stage
                        if "Loading image:" in line:
                            update_progress_func(session_id, 'preprocessing', 25, 'Loading input image...')
                        elif "Image loaded:" in line:
                            update_progress_func(session_id, 'preprocessing', 50, 'Image loaded successfully')
                        elif "Loading Trellis pipeline" in line:
                            update_progress_func(session_id, 'preprocessing', 75, 'Loading Trellis pipeline...')
                        elif "Pipeline loaded on" in line or "CUDA not available" in line or "PyTorch not available" in line:
                            update_progress_func(session_id, 'preprocessing', 100, 'Pipeline loaded and ready')
                            update_progress_func(session_id, 'sparse_structure', 0, 'Starting sparse structure generation...')
                        elif "Running Trellis pipeline" in line:
                            update_progress_func(session_id, 'sparse_structure', 5, 'Initializing 3D structure generation...')

                        # Sparse Structure Sampling - Parse actual percentage and iteration count
                        elif "Sampling:" in line:
                            # Extract percentage from lines like "Sampling:  25%|##5       | 3/12 [00:02<00:05,  1.54it/s]"
                            percentage_match = re.search(r'Sampling:\s*(\d+)%', line)
                            iteration_match = re.search(r'Sampling:.*?\|\s*(\d+)/(\d+)\s*\[', line)

                            if percentage_match:
                                percentage = int(percentage_match.group(1))
                            elif iteration_match:
                                # Calculate percentage from iteration count if percentage not found
                                current = int(iteration_match.group(1))
                                total = int(iteration_match.group(2))
                                percentage = int((current / total) * 100) if total > 0 else 0
                            else:
                                # Look for just iteration pattern without percentage
                                iter_only_match = re.search(r'(\d+)/(\d+)', line)
                                if iter_only_match:
                                    current = int(iter_only_match.group(1))
                                    total = int(iter_only_match.group(2))
                                    percentage = int((current / total) * 100) if total > 0 else 0
                                else:
                                    percentage = 0

                            # Track which sampling stage we're in
                            if sampling_stage_count == 0:
                                # First sampling stage is sparse structure
                                update_progress_func(session_id, 'sparse_structure', percentage, f'Sparse structure sampling: {percentage}%')
                                if percentage >= 100:
                                    sampling_stage_count = 1
                                    update_progress_func(session_id, 'slat_generation', 0, 'Starting SLAT generation...')
                            elif sampling_stage_count == 1:
                                # Second sampling stage is SLAT generation
                                update_progress_func(session_id, 'slat_generation', percentage, f'SLAT generation sampling: {percentage}%')

                        # Pipeline execution completed
                        elif "Pipeline execution completed" in line:
                            print(f"PROGRESS: Pipeline execution completed detected!")
                            update_progress_func(session_id, 'slat_generation', 100, 'SLAT generation completed')
                            update_progress_func(session_id, 'mesh_creation', 0, 'Starting mesh processing...')
                        elif "Sparse structure completed" in line:
                            print(f"PROGRESS: Sparse structure completed detected!")
                            update_progress_func(session_id, 'sparse_structure', 100, 'Sparse structure completed')
                        elif "SLAT generation completed" in line:
                            print(f"PROGRESS: SLAT generation completed detected!")
                            update_progress_func(session_id, 'slat_generation', 100, 'SLAT generation completed')
                            update_progress_func(session_id, 'mesh_creation', 5, 'Starting mesh processing...')
                        elif "Saving Gaussian PLY" in line:
                            update_progress_func(session_id, 'mesh_creation', 10, 'Saving Gaussian PLY...')
                        elif "Generating GLB file" in line:
                            update_progress_func(session_id, 'mesh_creation', 20, 'Generating GLB file...')

                        # Mesh Decimation - Parse actual percentage or detect start
                        elif "Decimating Mesh:" in line:
                            percentage_match = re.search(r'Decimating Mesh:\s*(\d+)%', line)
                            if percentage_match:
                                percentage = int(percentage_match.group(1))
                                # Map decimation progress to 20-60% of mesh creation stage
                                mesh_progress = 20 + int(percentage * 0.4)
                                update_progress_func(session_id, 'mesh_creation', mesh_progress, f'Decimating mesh: {percentage}%')
                            else:
                                # Just started decimation
                                update_progress_func(session_id, 'mesh_creation', 20, 'Starting mesh decimation...')
                        elif "After decimate:" in line:
                            # Decimation completed
                            update_progress_func(session_id, 'mesh_creation', 60, 'Mesh decimation completed')

                        # Rendering - Parse iteration count
                        elif "Rendering:" in line and "it [" in line:
                            # Extract iteration from lines like "Rendering: 54it [00:01, 28.38it/s]"
                            iteration_match = re.search(r'Rendering:\s*(\d+)it', line)
                            if iteration_match:
                                iterations = int(iteration_match.group(1))
                                # Use 100 as baseline for rendering progress
                                percentage = min(int((iterations / 100) * 100), 100)
                                # Map rendering progress to 60-80% of mesh creation stage
                                mesh_progress = 60 + int(percentage * 0.2)
                                update_progress_func(session_id, 'mesh_creation', mesh_progress, f'Rendering: {iterations} iterations')
                        elif "Rendering:" in line and "0it [" in line:
                            # Rendering started
                            update_progress_func(session_id, 'mesh_creation', 60, 'Starting rendering...')

                        # Texture Baking - Parse actual percentage and iteration count
                        elif "Texture baking (opt): optimizing:" in line:
                            # Extract percentage from lines like "Texture baking (opt): optimizing: 1%|          | 31/2500 [00:00<00:39, 61.78it/s, loss=0.437]"
                            percentage_match = re.search(r'optimizing:\s*(\d+)%', line)
                            iteration_match = re.search(r'optimizing:.*?\|\s*(\d+)/(\d+)\s*\[', line)

                            if percentage_match:
                                percentage = int(percentage_match.group(1))
                            elif iteration_match:
                                # Calculate percentage from iteration count if percentage not found
                                current = int(iteration_match.group(1))
                                total = int(iteration_match.group(2))
                                percentage = int((current / total) * 100) if total > 0 else 0
                            else:
                                percentage = 0

                            # Map texture baking to 80-100% of mesh creation stage
                            mesh_progress = 80 + int(percentage * 0.2)
                            update_progress_func(session_id, 'mesh_creation', mesh_progress, f'Texture baking: {percentage}%')
                        elif "Texture baking (opt):" in line and "optimizing:" not in line:
                            # Texture baking started
                            update_progress_func(session_id, 'mesh_creation', 80, 'Starting texture baking...')

                        elif "GLB file generated successfully" in line:
                            print(f"PROGRESS: GLB file generated successfully detected!")
                            update_progress_func(session_id, 'mesh_creation', 100, 'Mesh creation completed')
                            update_progress_func(session_id, 'glb_export', 25, 'GLB file generated successfully')
                        elif "Rendering videos" in line:
                            print(f"PROGRESS: Rendering videos detected!")
                            update_progress_func(session_id, 'glb_export', 50, 'Rendering videos...')
                        elif "Successfully generated output files" in line:
                            print(f"PROGRESS: Successfully generated output files detected!")
                            update_progress_func(session_id, 'glb_export', 100, 'GLB export completed')
                        elif "Videos rendered successfully" in line:
                            print(f"PROGRESS: Videos rendered successfully detected!")
                            update_progress_func(session_id, 'glb_export', 100, 'All processing completed')
                    except ImportError as e:
                        print(f"Failed to import update_progress: {e}")
                    except Exception as e:
                        print(f"Error in progress tracking: {e}")

            # Start reading threads
            import threading
            stdout_thread = threading.Thread(target=read_stdout)
            stderr_thread = threading.Thread(target=read_stderr)

            stdout_thread.daemon = True
            stderr_thread.daemon = True

            stdout_thread.start()
            stderr_thread.start()

            # Wait for process to complete with timeout
            TIMEOUT_SECONDS = 900  # 15 minutes total timeout
            start_time = time.time()

            try:
                # Poll the process with timeout
                while process.poll() is None:
                    elapsed_time = time.time() - start_time
                    if elapsed_time > TIMEOUT_SECONDS:
                        print(f"Process timed out after {TIMEOUT_SECONDS} seconds. Terminating...")
                        if session_id and update_progress_func:
                            update_progress_func(session_id, 'mesh_creation', 100, 'Process timed out - attempting to recover partial results...')

                        # Try to terminate gracefully first
                        process.terminate()
                        try:
                            process.wait(timeout=10)
                        except subprocess.TimeoutExpired:
                            # Force kill if graceful termination fails
                            process.kill()
                            process.wait()

                        raise subprocess.TimeoutExpired(cmd, TIMEOUT_SECONDS)

                    time.sleep(1)  # Check every second

                return_code = process.returncode
            except subprocess.TimeoutExpired:
                # Handle timeout - try to find partial results
                print("Process timed out, checking for partial results...")
                return_code = -1  # Indicate timeout

            # Wait for threads to finish reading
            stdout_thread.join(timeout=5)
            stderr_thread.join(timeout=5)

            # Create result object
            result = type('Result', (), {
                'returncode': return_code,
                'stdout': '\n'.join(output_lines),
                'stderr': '\n'.join(error_lines)
            })()

            if result.returncode != 0:
                print(f"Trellis processor failed with return code {result.returncode}")
                print(f"STDOUT: {result.stdout}")
                print(f"STDERR: {result.stderr}")
                raise RuntimeError(f"Trellis processor failed: {result.stderr}")

            # Read the result
            with open(result_file, 'r') as f:
                output = json.load(f)

            # Clean up
            os.unlink(result_file)

            if 'error' in output:
                raise RuntimeError(output['error'])

            return output

        except subprocess.TimeoutExpired:
            raise RuntimeError("Trellis processing timed out")
        except Exception as e:
            print(f"Error in subprocess processing: {e}")
            raise RuntimeError(f"Failed to process with Trellis environment: {str(e)}")

    def get_output_paths(self, image_id: str) -> Dict[str, str]:
        """
        Get paths to the generated output files for a given image ID.

        Args:
            image_id: Unique identifier for the processed image

        Returns:
            Dictionary of output file paths
        """
        output_prefix = self.output_dir / image_id
        return {
            'gaussian': f"{output_prefix}.ply",
            'glb': f"{output_prefix}.glb",
            'video': f"{output_prefix}.mp4"
        }

    def cleanup(self, pattern: str = None) -> None:
        """
        Clean up temporary files in the output directory.

        Args:
            pattern: Optional pattern to match files for cleanup. If None, cleans all files.
        """
        try:
            if pattern:
                # Clean up files matching the pattern
                for file_path in self.output_dir.glob(f"*{pattern}*"):
                    if file_path.is_file():
                        file_path.unlink()
                        print(f"Cleaned up: {file_path}")
            else:
                # Clean up all files in output directory
                for file_path in self.output_dir.glob("*"):
                    if file_path.is_file():
                        file_path.unlink()
                        print(f"Cleaned up: {file_path}")
        except Exception as e:
            print(f"Error during cleanup: {e}")

    def generate(self, image_path: str, settings: Dict = None, progress_callback = None) -> str:
        """Generate 3D model and return the output path (for modular pipeline compatibility)."""
        result = self.process_image(image_path, settings)
        if 'glb' in result:
            return result['glb']
        else:
            raise RuntimeError("Trellis generation failed - no GLB output")

    def apply_texture_to_mesh(self, mesh_path: str, image_path: str, settings: Dict, progress_callback = None) -> str:
        """Apply texture to an existing mesh using Trellis texture generation."""
        try:
            if progress_callback:
                progress_callback("Loading mesh for Trellis texture application...")

            logger.info(f"Applying Trellis texture to mesh: {mesh_path} with image: {image_path}")

            # Load the mesh
            import trimesh
            mesh = trimesh.load(mesh_path)
            logger.info(f"Mesh loaded successfully: {len(mesh.vertices)} vertices, {len(mesh.faces)} faces")

            # Load the image
            from PIL import Image
            image = Image.open(image_path)
            logger.info(f"Image loaded successfully: {image.size}")

            if progress_callback:
                progress_callback("Applying Trellis texture generation...")

            # Use Trellis texture baking capabilities
            # This is a simplified implementation - in practice, you'd use Trellis's
            # advanced texture baking from postprocessing_utils

            # For now, we'll generate a new model with texture and return it
            # This leverages Trellis's full pipeline for texture generation
            logger.info("Starting Trellis texture generation...")
            result = self.process_image(image_path, settings)

            if 'glb' in result:
                logger.info(f"Trellis texture applied successfully: {result['glb']}")
                return result['glb']
            else:
                logger.error("Trellis texture application failed: no GLB output")
                return mesh_path  # Return original mesh on failure

        except Exception as e:
            logger.error(f"Trellis texture application failed: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return mesh_path  # Return original mesh on failure


def create_trellis_pipeline(output_dir: str = "output") -> TrellisPipeline:
    """
    Factory function to create a TrellisPipeline instance.

    Args:
        output_dir: Directory where output files will be saved

    Returns:
        TrellisPipeline instance
    """
    return TrellisPipeline(output_dir=output_dir)
