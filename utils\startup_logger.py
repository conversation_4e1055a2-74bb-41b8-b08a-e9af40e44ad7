"""
Enhanced Startup Logging System
Creates detailed logs for debugging startup issues
"""

import os
import sys
import logging
import traceback
from datetime import datetime
from pathlib import Path
import json

class StartupLogger:
    """Enhanced logger for startup debugging."""
    
    def __init__(self):
        self.logs_dir = Path("logs")
        self.logs_dir.mkdir(exist_ok=True)
        
        # Create timestamped log file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.startup_log_file = self.logs_dir / f"startup_detailed_{timestamp}.log"
        self.error_log_file = self.logs_dir / f"startup_errors_{timestamp}.log"
        
        # Setup detailed logger
        self.logger = logging.getLogger('startup_detailed')
        self.logger.setLevel(logging.DEBUG)
        
        # Clear any existing handlers
        self.logger.handlers.clear()
        
        # File handler for detailed logs
        file_handler = logging.FileHandler(self.startup_log_file, mode='w', encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        # Error file handler
        error_handler = logging.FileHandler(self.error_log_file, mode='w', encoding='utf-8')
        error_handler.setLevel(logging.ERROR)
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        # Detailed formatter
        detailed_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)8s | %(name)s | %(funcName)s:%(lineno)d | %(message)s'
        )
        
        # Simple console formatter
        console_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)s | %(message)s'
        )
        
        file_handler.setFormatter(detailed_formatter)
        error_handler.setFormatter(detailed_formatter)
        console_handler.setFormatter(console_formatter)
        
        self.logger.addHandler(file_handler)
        self.logger.addHandler(error_handler)
        self.logger.addHandler(console_handler)
        
        # Log startup info
        self.log_system_info()
    
    def log_system_info(self):
        """Log detailed system information."""
        self.logger.info("="*80)
        self.logger.info("3D AI STUDIO STARTUP - DETAILED LOGGING")
        self.logger.info("="*80)
        
        try:
            import platform
            import psutil
            import torch
            
            system_info = {
                "timestamp": datetime.now().isoformat(),
                "platform": platform.platform(),
                "python_version": sys.version,
                "python_executable": sys.executable,
                "working_directory": os.getcwd(),
                "cpu_count": os.cpu_count(),
                "memory_total": f"{psutil.virtual_memory().total / (1024**3):.1f} GB",
                "disk_free": f"{psutil.disk_usage('.').free / (1024**3):.1f} GB",
                "torch_version": torch.__version__ if torch else "Not available",
                "cuda_available": torch.cuda.is_available() if torch else False,
            }
            
            if torch and torch.cuda.is_available():
                system_info["cuda_device"] = torch.cuda.get_device_name(0)
                system_info["cuda_memory"] = f"{torch.cuda.get_device_properties(0).total_memory / (1024**3):.1f} GB"
            
            self.logger.info("SYSTEM INFORMATION:")
            for key, value in system_info.items():
                self.logger.info(f"  {key}: {value}")
                
        except Exception as e:
            self.logger.error(f"Error collecting system info: {e}")
            self.logger.error(traceback.format_exc())
    
    def log_stage(self, stage_name: str, details: str = ""):
        """Log a startup stage."""
        self.logger.info("="*60)
        self.logger.info(f"STAGE: {stage_name}")
        if details:
            self.logger.info(f"DETAILS: {details}")
        self.logger.info("="*60)
    
    def log_step(self, step_name: str, status: str = "STARTING"):
        """Log a startup step."""
        self.logger.info(f"STEP: {step_name} - {status}")
    
    def log_progress(self, message: str, progress: int = None):
        """Log progress with optional percentage."""
        if progress is not None:
            self.logger.info(f"PROGRESS [{progress:3d}%]: {message}")
        else:
            self.logger.info(f"PROGRESS: {message}")
    
    def log_success(self, message: str):
        """Log success message."""
        self.logger.info(f"SUCCESS: {message}")
    
    def log_warning(self, message: str, exception: Exception = None):
        """Log warning message."""
        self.logger.warning(f"WARNING: {message}")
        if exception:
            self.logger.warning(f"Exception: {exception}")
            self.logger.debug(traceback.format_exc())
    
    def log_error(self, message: str, exception: Exception = None):
        """Log error message."""
        self.logger.error(f"ERROR: {message}")
        if exception:
            self.logger.error(f"Exception: {exception}")
            self.logger.error(traceback.format_exc())
    
    def log_import_attempt(self, module_name: str, success: bool, error: Exception = None):
        """Log module import attempts."""
        if success:
            self.logger.debug(f"IMPORT SUCCESS: {module_name}")
        else:
            self.logger.error(f"IMPORT FAILED: {module_name}")
            if error:
                self.logger.error(f"Import error: {error}")
    
    def log_dependency_check(self, dependency: str, available: bool, version: str = None):
        """Log dependency availability."""
        status = "AVAILABLE" if available else "MISSING"
        version_info = f" (v{version})" if version else ""
        self.logger.info(f"DEPENDENCY: {dependency} - {status}{version_info}")
    
    def log_pipeline_loading(self, pipeline_name: str, stage: str, duration: float = None):
        """Log pipeline loading stages."""
        duration_info = f" ({duration:.1f}s)" if duration is not None else ""
        self.logger.info(f"PIPELINE: {pipeline_name} - {stage}{duration_info}")
    
    def log_model_loading(self, model_name: str, path: str, success: bool, duration: float = None):
        """Log model loading attempts."""
        status = "LOADED" if success else "FAILED"
        duration_info = f" ({duration:.1f}s)" if duration is not None else ""
        self.logger.info(f"MODEL: {model_name} - {status}{duration_info}")
        self.logger.debug(f"Model path: {path}")
    
    def log_environment_setup(self, env_name: str, variables: dict):
        """Log environment variable setup."""
        self.logger.info(f"ENVIRONMENT: Setting up {env_name}")
        for key, value in variables.items():
            self.logger.debug(f"  {key} = {value}")
    
    def log_compilation(self, component: str, success: bool, output: str = None):
        """Log compilation attempts."""
        status = "SUCCESS" if success else "FAILED"
        self.logger.info(f"COMPILATION: {component} - {status}")
        if output:
            self.logger.debug(f"Compilation output:\n{output}")
    
    def log_startup_complete(self, total_time: float, success: bool):
        """Log startup completion."""
        self.logger.info("="*80)
        status = "SUCCESSFUL" if success else "FAILED"
        self.logger.info(f"STARTUP COMPLETE: {status} (Total time: {total_time:.1f}s)")
        self.logger.info("="*80)
        
        if success:
            self.logger.info(f"Detailed logs saved to: {self.startup_log_file}")
        else:
            self.logger.error(f"Error logs saved to: {self.error_log_file}")
            self.logger.error(f"Detailed logs saved to: {self.startup_log_file}")
    
    def create_summary_report(self, stages_completed: list, errors: list):
        """Create a startup summary report."""
        summary_file = self.logs_dir / f"startup_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        summary = {
            "timestamp": datetime.now().isoformat(),
            "stages_completed": stages_completed,
            "errors": errors,
            "total_stages": len(stages_completed),
            "error_count": len(errors),
            "success_rate": (len(stages_completed) - len(errors)) / len(stages_completed) if stages_completed else 0,
            "log_files": {
                "detailed": str(self.startup_log_file),
                "errors": str(self.error_log_file)
            }
        }
        
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2)
        
        self.logger.info(f"Summary report saved to: {summary_file}")

# Global startup logger instance
_startup_logger = None

def get_startup_logger() -> StartupLogger:
    """Get the global startup logger instance."""
    global _startup_logger
    if _startup_logger is None:
        _startup_logger = StartupLogger()
    return _startup_logger

def log_startup_stage(stage_name: str, details: str = ""):
    """Convenience function to log startup stage."""
    get_startup_logger().log_stage(stage_name, details)

def log_startup_step(step_name: str, status: str = "STARTING"):
    """Convenience function to log startup step."""
    get_startup_logger().log_step(step_name, status)

def log_startup_progress(message: str, progress: int = None):
    """Convenience function to log startup progress."""
    get_startup_logger().log_progress(message, progress)

def log_startup_error(message: str, exception: Exception = None):
    """Convenience function to log startup error."""
    get_startup_logger().log_error(message, exception)

def log_startup_success(message: str):
    """Convenience function to log startup success."""
    get_startup_logger().log_success(message)
