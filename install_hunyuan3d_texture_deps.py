"""
Hunyuan3D-2 Texture Dependencies Installer
Installs the compiled texture generation components from WinPortable into main environment
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def get_python_executable():
    """Get the Python executable from the virtual environment."""
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        # We're in a virtual environment
        return sys.executable
    else:
        # Try to find the venv python
        venv_path = Path(".venv")
        if os.name == 'nt':  # Windows
            python_exe = venv_path / "Scripts" / "python.exe"
        else:  # Linux/Mac
            python_exe = venv_path / "bin" / "python"
        
        if python_exe.exists():
            return str(python_exe)
        else:
            return sys.executable

def run_command(command, description="", timeout=300):
    """Run a command and return success status."""
    print(f"\n{'='*60}")
    print(f"RUNNING: {description}")
    print(f"COMMAND: {command}")
    print(f"{'='*60}")
    
    try:
        if isinstance(command, str):
            result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True, timeout=timeout)
        else:
            result = subprocess.run(command, check=True, capture_output=True, text=True, timeout=timeout)
        
        print("SUCCESS!")
        if result.stdout:
            print("STDOUT:", result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"ERROR: {e}")
        if e.stdout:
            print("STDOUT:", e.stdout)
        if e.stderr:
            print("STDERR:", e.stderr)
        return False
    except subprocess.TimeoutExpired:
        print(f"ERROR: Command timed out after {timeout} seconds")
        return False

def install_hunyuan3d_texture_dependencies():
    """Install Hunyuan3D-2 texture generation dependencies."""
    print("\n" + "="*80)
    print("INSTALLING HUNYUAN3D-2 TEXTURE DEPENDENCIES")
    print("="*80)
    
    # Get paths
    python_exe = get_python_executable()
    winportable_path = Path("Resources/Hunyuan3D2_WinPortable")
    hunyuan3d_path = winportable_path / "Hunyuan3D-2"
    
    # Check if WinPortable exists
    if not winportable_path.exists():
        print("ERROR: WinPortable directory not found")
        return False
    
    if not hunyuan3d_path.exists():
        print("ERROR: Hunyuan3D-2 directory not found in WinPortable")
        return False
    
    print(f"Using Python: {python_exe}")
    print(f"WinPortable path: {winportable_path}")
    
    # Step 1: Install DISO
    print("\nStep 1: Installing DISO...")
    if not run_command([python_exe, "-m", "pip", "install", "diso"], "Installing DISO"):
        print("WARNING: DISO installation failed, continuing...")
    
    # Step 2: Install custom_rasterizer
    print("\nStep 2: Installing custom_rasterizer...")
    custom_rasterizer_path = hunyuan3d_path / "hy3dgen" / "texgen" / "custom_rasterizer"
    
    if custom_rasterizer_path.exists():
        success = run_command(
            [python_exe, "-m", "pip", "install", str(custom_rasterizer_path)],
            "Installing custom_rasterizer",
            timeout=600  # 10 minutes for compilation
        )
        if not success:
            print("ERROR: custom_rasterizer installation failed")
            return False
    else:
        print(f"ERROR: custom_rasterizer path not found: {custom_rasterizer_path}")
        return False
    
    # Step 3: Install differentiable_renderer
    print("\nStep 3: Installing differentiable_renderer...")
    diff_renderer_path = hunyuan3d_path / "hy3dgen" / "texgen" / "differentiable_renderer"
    
    if diff_renderer_path.exists():
        success = run_command(
            [python_exe, "-m", "pip", "install", str(diff_renderer_path)],
            "Installing differentiable_renderer",
            timeout=600  # 10 minutes for compilation
        )
        if not success:
            print("ERROR: differentiable_renderer installation failed")
            return False
    else:
        print(f"ERROR: differentiable_renderer path not found: {diff_renderer_path}")
        return False
    
    # Step 4: Copy compiled PYD file if it exists
    print("\nStep 4: Copying compiled mesh processor...")
    source_pyd = diff_renderer_path / "build" / "lib.win-amd64-cpython-312" / "mesh_processor.cp312-win_amd64.pyd"
    target_pyd = diff_renderer_path / "mesh_processor.cp312-win_amd64.pyd"
    
    if source_pyd.exists():
        try:
            shutil.copy2(str(source_pyd), str(target_pyd))
            print(f"SUCCESS: Copied {source_pyd} to {target_pyd}")
        except Exception as e:
            print(f"WARNING: Failed to copy PYD file: {e}")
    else:
        print("INFO: Compiled PYD file not found, may be compiled during installation")
    
    # Step 5: Verify installations
    print("\nStep 5: Verifying texture dependencies...")
    
    # Test custom_rasterizer
    verify_script = '''
try:
    import custom_rasterizer
    print("custom_rasterizer: Available")
except ImportError as e:
    print(f"custom_rasterizer: Not available ({e})")

try:
    import mesh_processor
    print("mesh_processor: Available")
except ImportError as e:
    print(f"mesh_processor: Not available ({e})")

try:
    import diso
    print("diso: Available")
except ImportError as e:
    print(f"diso: Not available ({e})")
'''
    
    if not run_command([python_exe, "-c", verify_script], "Verifying texture dependencies"):
        print("WARNING: Some texture dependencies may not be available")
    
    print("\n" + "="*80)
    print("HUNYUAN3D-2 TEXTURE DEPENDENCIES INSTALLATION COMPLETED!")
    print("="*80)
    print("Installed components:")
    print("✓ DISO")
    print("✓ custom_rasterizer (CUDA compilation)")
    print("✓ differentiable_renderer (mesh processing)")
    print("✓ mesh_processor (compiled extension)")
    print("\nTexture generation should now be available!")
    
    return True

def check_texture_dependencies():
    """Quick check if texture dependencies are available."""
    try:
        import custom_rasterizer
        import mesh_processor
        return True
    except ImportError:
        return False

if __name__ == "__main__":
    print("Hunyuan3D-2 Texture Dependencies Installer")
    print("This will install the compiled texture generation components")
    print("\nPress Enter to continue or Ctrl+C to cancel...")
    
    try:
        input()
        success = install_hunyuan3d_texture_dependencies()
        if success:
            print("\nTexture dependencies installed successfully!")
        else:
            print("\nTexture dependencies installation completed with some errors.")
    except KeyboardInterrupt:
        print("\nInstallation cancelled by user.")
        sys.exit(1)
