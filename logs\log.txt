========================================
         3D AI Studio Launcher
========================================

[1/5] Checking Python installation...
[OK] Python found
[2/4] Checking virtual environment
🚀 3D AI Studio - Comprehensive Dependency Installer
======================================================================
This will install ALL dependencies for Trellis and Hunyuan3D-2 systems

Checking critical dependencies...
  ✓ Flask available
  ✓ Flask-CORS available
  ✓ requests available
  ✓ NumPy available
  ✓ Pillow available
  ✓ PyTorch available
  ✓ Transformers available
  ✓ Trimesh available
  ✓ ImageIO available

✅ All critical dependencies are available!
✅ Critical dependencies are available.
Run with --force to reinstall all dependencies.
[OK] Virtual environment ready
[3/4] Quick environment check
  [INFO] All dependencies installed automatically at startup
  [INFO] Trellis and Hunyuan3D-2 systems integrated in single environment
Quick environment check...
  [INFO] Full validation available through Dependency Manager
  ✓ Main Application: Basic setup OK
  ✓ Trellis: Basic setup OK
  ✓ Hunyuan3D-2: Basic setup OK
  [OK] Basic environments ready
[4/4] Activating Python environment
Installing missing dependencies
Warning: Some dependencies may not have installed correctly
The application will attempt to start with available functionality
  [OK] Python environment activated
Checking Node.js for Electron app
[OK] Node.js found
Preparing desktop application
[OK] Desktop application ready
Starting 3D AI Studio Desktop App

========================================
    3D AI Studio Desktop Application
    Starting in desktop window

    STREAMLINED SETUP
    All dependencies installed automatically at startup!

    FEATURES:
    [OK] Trellis pipeline for high-quality 3D generation
    [OK] Hunyuan3D-2 native server with text-to-3D support
    [OK] Automatic background service management
    [OK] Comprehensive progress tracking
    [OK] Single virtual environment for all systems

    Ready to use - no manual setup required!
========================================


> 3d-ai-studio@1.0.0 electron
> electron .


Starting 3D AI Studio...
Starting new server...
Starting server...
App path: E:\3D AI Studio
Python path: E:\3D AI Studio\.venv\Scripts\python.exe
App script: E:\3D AI Studio\app.py
Server: 08:18:14 | INFO | [SYSTEM](NO_SESSI) | System startup | Data: {"platform": "Windows-11-10.0.26100-SP0", "python_version": "3.12.10", "cpu_count": 88, "memory_total": "79.9 GB", "disk_free": "530.8 GB", "torch_version": "2.5.1+cu124", "cuda_available": true, "cuda_device": "NVIDIA GeForce RTX 3060", "cuda_memory": "12.0 GB"}
08:18:14 | INFO | [STARTUP](NO_SESSI) | 3D AI Studio starting up

Server: Starting 3D AI Studio core application...
All AI models and dependencies managed through Dependency Manager
Core application ready - use Dependency Manager for AI features
Starting 3D AI Studio server...
Running startup dependency checks...
============================================================
STARTUP DEPENDENCY CHECKS
============================================================
[STARTUP] Checking critical dependencies...

Server: [STARTUP] imageio: Already available

Server: [STARTUP] Installing Pillow...

Server: [STARTUP] Pillow: Installed successfully

Server: [STARTUP] numpy: Already available

Server: [STARTUP] requests: Already available

Server: [STARTUP] flask: Already available

Server: [STARTUP] flask-cors: Already available
[STARTUP] Critical dependencies check complete: 6/6 available

Server: [STARTUP] PIL imports working correctly

Server: [STARTUP] ImageIO already available
[STARTUP] Checking Hunyuan3D-2 setup...

Server: 08:18:27 | INFO | [HUNYUAN3D_DIRECT](NO_SESSI) | Initial setup status - Compilation: True, Models: True
[STARTUP] Hunyuan3D-2 initial setup already completed
============================================================
STARTUP CHECKS COMPLETE: 4/4 successful
============================================================

Server: Using integrated Hunyuan3D-2 pipeline (no background services needed)
============================================================
LOADING HUNYUAN3D-2 PIPELINE
============================================================
[HUNYUAN3D] Starting Hunyuan3D-2 pipeline initialization...
[HUNYUAN3D] Using CUDA acceleration with RTX 3060
08:18:27 | INFO | [HUNYUAN3D_INTEGRATED](NO_SESSI) | Loading Hunyuan3D-2 pipeline components...
[HUNYUAN3D] Stage 1/6: Setting up Hunyuan3D-2 environment...
08:18:27 | INFO | [HUNYUAN3D_INTEGRATED](NO_SESSI) | Hunyuan3D-2 environment setup completed

Server: [HUNYUAN3D] Stage 2/6: Loading background removal model...
08:18:36 | INFO | [HUNYUAN3D_INTEGRATED](NO_SESSI) | Background removal worker loaded

Server stderr: A matching Triton is not available, some optimizations will not be enabled
Traceback (most recent call last):
  File "E:\3D AI Studio\.venv\Lib\site-packages\xformers\__init__.py", line 57, in _is_triton_available
    import triton  # noqa
    ^^^^^^^^^^^^^
ModuleNotFoundError: No module named 'triton'

Server stderr: 2025-06-06 08:19:16,987 - hy3dgen.shapgen - INFO - Try to load model from local path: Resources\Hunyuan3D2_WinPortable\HuggingFaceHub\tencent/Hunyuan3D-2mini\hunyuan3d-dit-v2-mini-turbo
2025-06-06 08:19:16,987 - hy3dgen.shapgen - INFO - Model path not exists, try to download from huggingface

Server: [HUNYUAN3D] Stage 3/6: Loading shape generation pipeline...
Warning:
Unable to load the following plugins:

        filter_embree.dll: filter_embree.dll does not seem to be a Qt Plugin.

Cannot load library E:\3D AI Studio\.venv\Lib\site-packages\pymeshlab\lib\plugins\filter_embree.dll: The specified module could not be found.
        filter_func.dll: filter_func.dll does not seem to be a Qt Plugin.

Cannot load library E:\3D AI Studio\.venv\Lib\site-packages\pymeshlab\lib\plugins\filter_func.dll: The specified module could not be found.
        filter_mesh_alpha_wrap.dll: filter_mesh_alpha_wrap.dll does not seem to be a Qt Plugin.

Cannot load library E:\3D AI Studio\.venv\Lib\site-packages\pymeshlab\lib\plugins\filter_mesh_alpha_wrap.dll: The specified module could not be found.
        filter_mesh_booleans.dll: filter_mesh_booleans.dll does not seem to be a Qt Plugin.

Cannot load library E:\3D AI Studio\.venv\Lib\site-packages\pymeshlab\lib\plugins\filter_mesh_booleans.dll: The specified module could not be found.
        filter_sketchfab.dll: filter_sketchfab.dll does not seem to be a Qt Plugin.

Cannot load library E:\3D AI Studio\.venv\Lib\site-packages\pymeshlab\lib\plugins\filter_sketchfab.dll: The specified module could not be found.
        io_3ds.dll: io_3ds.dll does not seem to be a Qt Plugin.

Cannot load library E:\3D AI Studio\.venv\Lib\site-packages\pymeshlab\lib\plugins\io_3ds.dll: The specified module could not be found.
        io_e57.dll: io_e57.dll does not seem to be a Qt Plugin.

Cannot load library E:\3D AI Studio\.venv\Lib\site-packages\pymeshlab\lib\plugins\io_e57.dll: The specified module could not be found.


Fetching 3 files: 100%|##########| 3/3 [00:00<?, ?it/s]

Server stderr: 2025-06-06 08:19:17,420 - hy3dgen.shapgen - INFO - Loading model from Resources\Hunyuan3D2_WinPortable\HuggingFaceHub\models--tencent--Hunyuan3D-2mini\snapshots\d3318f050d91fe232038820bbbabeab95c0de14e\hunyuan3d-dit-v2-mini-turbo\model.fp16.safetensors

Server stderr: 2025-06-06 08:20:21,615 - hy3dgen.shapgen - INFO - Try to load model from local path: Resources\Hunyuan3D2_WinPortable\HuggingFaceHub\tencent/Hunyuan3D-2mini\hunyuan3d-vae-v2-mini-turbo
2025-06-06 08:20:21,615 - hy3dgen.shapgen - INFO - Model path not exists, try to download from huggingface

Fetching 3 files:   0%|          | 0/3 [00:00<?, ?it/s]
Server stderr: E:\3D AI Studio\.venv\Lib\site-packages\huggingface_hub\file_download.py:143: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\HuggingFaceHub\models--tencent--Hunyuan3D-2mini. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https://huggingface.co/docs/huggingface_hub/how-to-cache#limitations.
To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https://docs.microsoft.com/en-us/windows/apps/get-started/enable-your-device-for-development
  warnings.warn(message)

Fetching 3 files: 100%|##########| 3/3 [00:20<00:00,  6.80s/it]

Server stderr: 2025-06-06 08:20:42,117 - hy3dgen.shapgen - INFO - Loading model from Resources\Hunyuan3D2_WinPortable\HuggingFaceHub\models--tencent--Hunyuan3D-2mini\snapshots\d3318f050d91fe232038820bbbabeab95c0de14e\hunyuan3d-vae-v2-mini-turbo\model.fp16.safetensors

Server: 08:20:58 | INFO | [HUNYUAN3D_INTEGRATED](NO_SESSI) | Shape generation pipeline loaded
[HUNYUAN3D] Stage 4/6: Loading mesh processing workers...
08:20:58 | INFO | [HUNYUAN3D_INTEGRATED](NO_SESSI) | Mesh processing workers loaded

Server: [HUNYUAN3D] Stage 5/6: Loading texture generation pipeline...

Fetching 13 files:   0%|          | 0/13 [00:00<?, ?it/s]
Fetching 13 files: 100%|##########| 13/13 [00:00<00:00, 4262.17it/s]

Fetching 17 files:   0%|          | 0/17 [00:00<?, ?it/s]
Fetching 17 files: 100%|##########| 17/17 [00:00<00:00, 8468.31it/s]

Server stderr: You need to install HuggingFace Hub to load models from the hub.

Server: 08:21:01 | WARNING | [HUNYUAN3D_INTEGRATED](NO_SESSI) | Failed to load texture generation pipeline: Model path Resources\Hunyuan3D2_WinPortable\HuggingFaceHub\models--tencent--Hunyuan3D-2\snapshots\34e28261f71c32975727be8db0eace439a280f82 not found

Server: [HUNYUAN3D] Stage 6/6: Loading text-to-image pipeline...

Loading pipeline components...:   0%|          | 0/7 [00:00<?, ?it/s]
Server stderr:
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading pipeline components...:   0%|          | 0/7 [00:00<?, ?it/s]

Server: 08:21:06 | WARNING | [HUNYUAN3D_INTEGRATED](NO_SESSI) | Failed to load text-to-image pipeline: 'SafeTensorFile' object has no attribute 'get_slice'

Server: [HUNYUAN3D] Finalizing pipeline setup...
08:21:06 | INFO | [HUNYUAN3D_INTEGRATED](NO_SESSI) | All Hunyuan3D-2 pipeline components loaded successfully

Server: [HUNYUAN3D] Hunyuan3D-2 pipeline ready!
[HUNYUAN3D] SUCCESS: Hunyuan3D-2 pipeline loaded successfully!
[HUNYUAN3D] SUCCESS: Texture generation: Not available
[HUNYUAN3D] SUCCESS: Text-to-image: Not available
[HUNYUAN3D] SUCCESS: CUDA acceleration enabled
============================================================
HUNYUAN3D-2 INITIALIZATION COMPLETED
============================================================
 * Serving Flask app 'app'
 * Debug mode: off

Server stderr: WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
Press CTRL+C to quit

Server detected as ready via stderr!
Server stderr: 127.0.0.1 - - [06/Jun/2025 08:21:09] "GET / HTTP/1.1" 200 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 08:21:10] "GET /assets/index-DrFs_VGd.js HTTP/1.1" 200 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 08:21:10] "GET /assets/index-ehFM-iJR.css HTTP/1.1" 200 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 08:21:10] "GET /api/config/huggingface-token HTTP/1.1" 200 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 08:21:10] "GET /api/sample-images HTTP/1.1" 200 -

Server: 08:21:10 | INFO | [API](NO_SESSI) | API: GET delighter_status | Status: 200 | Duration: 0.038s | IP: 127.0.0.1

Server stderr: 127.0.0.1 - - [06/Jun/2025 08:21:10] "GET /api/delighter/status HTTP/1.1" 200 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 08:21:12] "GET /api/sample-images/∩┐╜Pngtree∩┐╜3d%20rendering%20of%20a%20greek_15549830-sharpen.png HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 08:21:12] "GET /api/sample-images/b0da10f6-3d28-4a74-95b8-3fa373c2d7a2.png HTTP/1.1
Server stderr: " 304 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 08:21:12] "GET /api/sample-images/d5aeb72c-82af-45f1-b518-ea72280119b3-sharpen-face-upscale-3.4x.jpeg HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 08:21:12] "GET /api/sample-images/a793b9fc-3485-4c04-b57d-95cdb356c57c-upscale-3.4x.png HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 08:21:12] "
Server stderr: GET /api/sample-images/d9ba77aa-ff91-4e43-ad55-fabe51244dc8.png HTTP/1.1
Server stderr: " 304 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 08:21:12] "GET /api/sample-images/5ac3a645-10a0-48c7-93e4-c53a5a1c81a7-upscale-3.4x.png HTTP/1.1
Server stderr: " 304 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 08:21:12] "GET /api/sample-images/10745cfe-80a0-48f0-a56e-0308e9f870f2-upscale-3.4x.png HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 08:21:12] "
Server stderr: GET /api/sample-images/02f234e7-140d-4775-9563-067b4e5862c4.png HTTP/1.1" 304 -
127.0.0.1 - - [06/Jun/2025 08:21:12] "
Server stderr: GET /api/sample-images/18722263-upscale-4x.jpeg HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 08:21:12] "GET /api/sample-images/50abdc8c-5c16-4bda-9ffa-e5ba71adb3eb-sharpen-upscale-3.4x.jpeg HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 08:21:13] "GET /api/sample-images/da6939f2-25df-4133-94af-8b242e042314.png HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 08:21:13] "GET /api/sample-images/DP-23915-004-sharpen.jpeg HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 08:21:13] "
Server stderr: GET /api/sample-images/ef257036-61ce-40d0-b627-83761afc45ed-upscale-3.4x.png HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 08:21:13] "GET /api/sample-images/fede52c1-c968-4daa-88e7-c753af5887ad.png HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 08:21:13] "GET /api/sample-images/istockphoto-173559916-612x612.jpg HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 08:21:13] "GET /api/sample-images/mayan-pot-in-the-form-of-a-kneeling-figure-800-1200_u-l-q1nhita0.jpg HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 08:21:20] "GET /api/pipelines/available HTTP/1.1" 200 -