"""
Hunyuan3D-2 Integrated Pipeline
Loads Hunyuan3D-2 pipeline directly in our application using dedicated environment
"""

import os
import sys
import time
import subprocess
import torch
from pathlib import Path
from typing import Dict, Any, Optional, Callable

# Set up logging
try:
    from utils.logging_system import get_logger
    logger = get_logger()
except ImportError:
    import logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

class Hunyuan3DIntegratedPipeline:
    """Integrated Hunyuan3D-2 pipeline that loads directly in our application."""
    
    def __init__(self):
        self.winportable_path = Path("Resources/Hunyuan3D2_WinPortable")
        self.hunyuan3d_path = self.winportable_path / "Hunyuan3D-2"
        self.python_path = self.winportable_path / "python_standalone" / "python.exe"
        
        # Pipeline components (will be loaded)
        self.i23d_worker = None
        self.texgen_worker = None
        self.t2i_worker = None
        self.rmbg_worker = None
        self.face_reduce_worker = None
        self.floater_remove_worker = None
        self.degenerate_face_remove_worker = None
        
        # Pipeline status
        self.is_initialized = False
        self.has_texturegen = False
        self.has_t2i = False
        
        # Configuration (matching WinPortable defaults)
        self.model_path = "tencent/Hunyuan3D-2mini"
        self.subfolder = "hunyuan3d-dit-v2-mini-turbo"
        self.texgen_model_path = "tencent/Hunyuan3D-2"
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.enable_flashvdm = True
        self.low_vram_mode = True
        self.enable_t23d = True
        
    def setup_hunyuan3d_environment(self):
        """Setup the Hunyuan3D-2 environment exactly like WinPortable."""
        try:
            # Add Hunyuan3D-2 path to Python path
            hunyuan3d_str = str(self.hunyuan3d_path)
            if hunyuan3d_str not in sys.path:
                sys.path.insert(0, hunyuan3d_str)

            # Set environment variables (matching WinPortable)
            os.environ['HF_HUB_CACHE'] = str(self.winportable_path / "HuggingFaceHub")
            os.environ['HY3DGEN_MODELS'] = str(self.winportable_path / "HuggingFaceHub")
            os.environ['PYTHONPYCACHEPREFIX'] = str(self.winportable_path / "pycache")
            os.environ['HUNYUAN3D_ENABLE_T23D'] = '1'
            os.environ['PYTHONUNBUFFERED'] = '1'

            # Portable application settings
            os.environ['HF_HUB_DISABLE_SYMLINKS_WARNING'] = '1'  # Disable symlink warnings
            os.environ['HF_HUB_DISABLE_SYMLINKS'] = '1'  # Force no symlinks for portability
            os.environ['XFORMERS_MORE_DETAILS'] = '0'  # Disable xFormers warnings
            os.environ['TRITON_DISABLE_WARNINGS'] = '1'  # Disable Triton warnings

            logger.info("Hunyuan3D-2 environment setup completed", component="HUNYUAN3D_INTEGRATED")
            return True

        except Exception as e:
            logger.error(f"Error setting up Hunyuan3D-2 environment: {e}", component="HUNYUAN3D_INTEGRATED")
            return False
    
    def load_pipeline_components(self, progress_callback: Optional[Callable] = None) -> bool:
        """Load all Hunyuan3D-2 pipeline components with progress tracking."""
        try:
            logger.info("Loading Hunyuan3D-2 pipeline components...", component="HUNYUAN3D_INTEGRATED")
            
            # Stage 1: Setup environment
            if progress_callback:
                progress_callback("Stage 1/6: Setting up Hunyuan3D-2 environment...")
            
            if not self.setup_hunyuan3d_environment():
                return False
            
            # Stage 2: Load background removal
            if progress_callback:
                progress_callback("Stage 2/6: Loading background removal model...")
            
            from hy3dgen.rembg import BackgroundRemover
            self.rmbg_worker = BackgroundRemover()
            logger.info("Background removal worker loaded", component="HUNYUAN3D_INTEGRATED")
            
            # Stage 3: Load shape generation pipeline
            if progress_callback:
                progress_callback("Stage 3/6: Loading shape generation pipeline...")
            
            from hy3dgen.shapegen import (
                FaceReducer, FloaterRemover, DegenerateFaceRemover, 
                Hunyuan3DDiTFlowMatchingPipeline
            )
            
            # Load main image-to-3D pipeline
            torch.set_default_device("cpu")
            self.i23d_worker = Hunyuan3DDiTFlowMatchingPipeline.from_pretrained(
                self.model_path,
                subfolder=self.subfolder,
                use_safetensors=True,
                device=self.device,
            )
            
            if self.enable_flashvdm:
                mc_algo = 'mc' if self.device in ['cpu', 'mps'] else 'dmc'
                self.i23d_worker.enable_flashvdm(mc_algo=mc_algo)
            
            logger.info("Shape generation pipeline loaded", component="HUNYUAN3D_INTEGRATED")
            
            # Stage 4: Load mesh processing workers
            if progress_callback:
                progress_callback("Stage 4/6: Loading mesh processing workers...")
            
            self.floater_remove_worker = FloaterRemover()
            self.degenerate_face_remove_worker = DegenerateFaceRemover()
            self.face_reduce_worker = FaceReducer()
            
            logger.info("Mesh processing workers loaded", component="HUNYUAN3D_INTEGRATED")
            
            # Stage 5: Load texture generation (optional - skip for faster startup)
            if progress_callback:
                progress_callback("Stage 5/6: Checking texture generation availability...")

            # For portable application startup speed, skip texture generation loading
            # Texture generation can be loaded on-demand when needed
            logger.info("Skipping texture generation pipeline loading for faster startup", component="HUNYUAN3D_INTEGRATED")
            logger.info("Texture generation will be loaded on-demand when requested", component="HUNYUAN3D_INTEGRATED")
            self.has_texturegen = False

            # # Original texture loading code (commented out for faster startup)
            # try:
            #     from hy3dgen.texgen import Hunyuan3DPaintPipeline
            #
            #     # Check if texture model snapshots exist
            #     texture_model_cache = self.winportable_path / "HuggingFaceHub" / "models--tencent--Hunyuan3D-2" / "snapshots"
            #     if texture_model_cache.exists() and any(texture_model_cache.iterdir()):
            #         # Find the snapshot directory
            #         snapshot_dirs = list(texture_model_cache.iterdir())
            #         if snapshot_dirs:
            #             snapshot_path = snapshot_dirs[0]  # Use the first (and likely only) snapshot
            #
            #             # Check if the required model subdirectories exist
            #             delight_path = snapshot_path / "hunyuan3d-delight-v2-0"
            #             paint_path = snapshot_path / "hunyuan3d-paint-v2-0"
            #
            #             if delight_path.exists() and paint_path.exists():
            #                 self.texgen_worker = Hunyuan3DPaintPipeline.from_pretrained(self.texgen_model_path)
            #                 if self.low_vram_mode:
            #                     self.texgen_worker.enable_model_cpu_offload()
            #
            #                 self.has_texturegen = True
            #                 logger.info("Texture generation pipeline loaded", component="HUNYUAN3D_INTEGRATED")
            #             else:
            #                 logger.info("Texture model subdirectories not found, skipping texture generation", component="HUNYUAN3D_INTEGRATED")
            #                 self.has_texturegen = False
            #         else:
            #             logger.info("No texture model snapshots found, skipping texture generation", component="HUNYUAN3D_INTEGRATED")
            #             self.has_texturegen = False
            #     else:
            #         logger.info("Texture models not downloaded yet, skipping texture generation", component="HUNYUAN3D_INTEGRATED")
            #         self.has_texturegen = False
            #
            # except Exception as e:
            #     logger.warning(f"Failed to load texture generation pipeline: {e}", component="HUNYUAN3D_INTEGRATED")
            #     logger.info("Texture generation will be unavailable (shape generation still works)", component="HUNYUAN3D_INTEGRATED")
            #     self.has_texturegen = False
            
            # Stage 6: Load text-to-image (optional)
            if progress_callback:
                progress_callback("Stage 6/6: Loading text-to-image pipeline...")

            if self.enable_t23d:
                try:
                    from hy3dgen.text2image import HunyuanDiTPipeline
                    self.t2i_worker = HunyuanDiTPipeline('Tencent-Hunyuan/HunyuanDiT-v1.1-Diffusers-Distilled')
                    self.has_t2i = True
                    logger.info("Text-to-image pipeline loaded", component="HUNYUAN3D_INTEGRATED")

                except Exception as e:
                    logger.warning(f"Failed to load text-to-image pipeline: {e}", component="HUNYUAN3D_INTEGRATED")
                    logger.info("Text-to-image will be unavailable (image-to-3D still works)", component="HUNYUAN3D_INTEGRATED")
                    self.has_t2i = False
            else:
                logger.info("Text-to-image pipeline disabled", component="HUNYUAN3D_INTEGRATED")
                self.has_t2i = False
            
            # Finalization
            if progress_callback:
                progress_callback("Finalizing pipeline setup...")
            
            # Setup memory optimization
            if self.low_vram_mode and torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            self.is_initialized = True
            logger.info("All Hunyuan3D-2 pipeline components loaded successfully", component="HUNYUAN3D_INTEGRATED")
            
            if progress_callback:
                progress_callback("Hunyuan3D-2 pipeline ready!")
            
            return True
            
        except Exception as e:
            logger.error(f"Error loading Hunyuan3D-2 pipeline components: {e}", component="HUNYUAN3D_INTEGRATED")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}", component="HUNYUAN3D_INTEGRATED")
            return False
    
    def is_available(self) -> bool:
        """Check if the pipeline is available for use."""
        return self.is_initialized and self.i23d_worker is not None

    def load_texture_generation_on_demand(self) -> bool:
        """Load texture generation pipeline on-demand when needed."""
        if self.has_texturegen:
            return True  # Already loaded

        try:
            logger.info("Loading texture generation pipeline on-demand...", component="HUNYUAN3D_INTEGRATED")

            from hy3dgen.texgen import Hunyuan3DPaintPipeline

            # Check if texture model snapshots exist
            texture_model_cache = self.winportable_path / "HuggingFaceHub" / "models--tencent--Hunyuan3D-2" / "snapshots"
            if texture_model_cache.exists() and any(texture_model_cache.iterdir()):
                # Find the snapshot directory
                snapshot_dirs = list(texture_model_cache.iterdir())
                if snapshot_dirs:
                    snapshot_path = snapshot_dirs[0]  # Use the first (and likely only) snapshot

                    # Check if the required model subdirectories exist
                    delight_path = snapshot_path / "hunyuan3d-delight-v2-0"
                    paint_path = snapshot_path / "hunyuan3d-paint-v2-0"

                    if delight_path.exists() and paint_path.exists():
                        self.texgen_worker = Hunyuan3DPaintPipeline.from_pretrained(self.texgen_model_path)
                        if self.low_vram_mode:
                            self.texgen_worker.enable_model_cpu_offload()

                        self.has_texturegen = True
                        logger.info("Texture generation pipeline loaded successfully", component="HUNYUAN3D_INTEGRATED")
                        return True
                    else:
                        logger.warning("Texture model subdirectories not found", component="HUNYUAN3D_INTEGRATED")
                        return False
                else:
                    logger.warning("No texture model snapshots found", component="HUNYUAN3D_INTEGRATED")
                    return False
            else:
                logger.warning("Texture models not downloaded", component="HUNYUAN3D_INTEGRATED")
                return False

        except Exception as e:
            logger.error(f"Failed to load texture generation pipeline on-demand: {e}", component="HUNYUAN3D_INTEGRATED")
            return False
    
    def generate_image_to_3d(self, image_path: str, settings: Dict[str, Any] = None, 
                           progress_callback: Optional[Callable] = None, 
                           session_id: Optional[str] = None) -> Dict[str, Any]:
        """Generate 3D model from image using integrated pipeline."""
        try:
            if not self.is_available():
                return {
                    'success': False,
                    'error': 'Hunyuan3D-2 pipeline not initialized'
                }
            
            logger.info(f"Starting integrated image-to-3D generation: {image_path}", component="HUNYUAN3D_INTEGRATED")
            
            # Default settings
            default_settings = {
                'seed': 1234,
                'octree_resolution': 128,
                'num_inference_steps': 5,
                'guidance_scale': 5.0,
                'enable_texture': True,
                'face_count': 40000,
                'randomize_seed': False
            }
            
            if settings:
                default_settings.update(settings)
            
            start_time = time.time()
            
            # Load and process image
            if progress_callback:
                progress_callback("Loading and processing image...")
            
            from PIL import Image
            image = Image.open(image_path).convert('RGBA')
            
            # Remove background
            if progress_callback:
                progress_callback("Removing background...")
            
            image = self.rmbg_worker(image.convert('RGB'))
            
            # Generate 3D shape
            if progress_callback:
                progress_callback("Generating 3D shape...")
            
            generator = torch.Generator()
            if default_settings.get('randomize_seed', False):
                import random
                seed = random.randint(0, 10000000)
            else:
                seed = default_settings.get('seed', 1234)
            
            generator = generator.manual_seed(int(seed))
            
            outputs = self.i23d_worker(
                image=image,
                num_inference_steps=default_settings.get('num_inference_steps', 5),
                guidance_scale=default_settings.get('guidance_scale', 5.0),
                generator=generator,
                octree_resolution=default_settings.get('octree_resolution', 128),
                output_type='mesh'
            )
            
            # Export to trimesh
            from hy3dgen.shapegen.pipelines import export_to_trimesh
            mesh = export_to_trimesh(outputs)[0]
            
            # Post-process mesh
            if progress_callback:
                progress_callback("Post-processing mesh...")
            
            mesh = self.face_reduce_worker(mesh, max_facenum=default_settings.get('face_count', 40000))
            
            # Apply texture if enabled
            if default_settings.get('enable_texture', False) and self.has_texturegen:
                if progress_callback:
                    progress_callback("Generating texture...")
                
                mesh = self.texgen_worker(mesh, image)
            
            # Save mesh
            import uuid
            output_dir = Path("outputs") / f"hunyuan3d_{session_id or uuid.uuid4().hex[:8]}"
            output_dir.mkdir(parents=True, exist_ok=True)
            
            output_path = output_dir / "model.glb"
            mesh.export(str(output_path))
            
            generation_time = time.time() - start_time
            
            logger.info(f"Integrated generation completed in {generation_time:.2f}s", component="HUNYUAN3D_INTEGRATED")
            
            return {
                'success': True,
                'primary_output': str(output_path),
                'glb': str(output_path),
                'generation_time': generation_time,
                'mesh_stats': {
                    'vertices': len(mesh.vertices),
                    'faces': len(mesh.faces),
                    'has_texture': default_settings.get('enable_texture', False) and self.has_texturegen
                }
            }
            
        except Exception as e:
            logger.error(f"Error in integrated image-to-3D generation: {e}", component="HUNYUAN3D_INTEGRATED")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}", component="HUNYUAN3D_INTEGRATED")
            return {
                'success': False,
                'error': f'Generation failed: {str(e)}'
            }

# Global pipeline instance
_hunyuan3d_integrated_pipeline = None

def get_hunyuan3d_integrated_pipeline() -> Hunyuan3DIntegratedPipeline:
    """Get the global Hunyuan3D integrated pipeline instance."""
    global _hunyuan3d_integrated_pipeline
    if _hunyuan3d_integrated_pipeline is None:
        _hunyuan3d_integrated_pipeline = Hunyuan3DIntegratedPipeline()
    return _hunyuan3d_integrated_pipeline
