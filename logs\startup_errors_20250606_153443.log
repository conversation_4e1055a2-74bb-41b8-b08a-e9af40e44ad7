2025-06-06 15:35:24,435 |    ERROR | startup_detailed | log_error:134 | ERROR: Texture generation pipeline failed after 0.2s
2025-06-06 15:35:24,435 |    ERROR | startup_detailed | log_error:136 | Exception: DLL load failed while importing custom_rasterizer_kernel: The specified procedure could not be found.
2025-06-06 15:35:24,440 |    ERROR | startup_detailed | log_error:137 | Traceback (most recent call last):
  File "E:\3D AI Studio\hunyuan3d_integrated_pipeline.py", line 252, in load_pipeline_components
    self.texgen_worker = Hunyuan3DPaintPipeline.from_pretrained(local_texgen_path)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\texgen\pipelines.py", line 86, in from_pretrained
    return cls(Hunyuan3DTexGenConfig(delight_model_path, multiview_model_path))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\texgen\pipelines.py", line 91, in __init__
    self.render = MeshRender(
                  ^^^^^^^^^^^
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\texgen\differentiable_renderer\mesh_render.py", line 145, in __init__
    import custom_rasterizer as cr
  File "E:\3D AI Studio\.venv\Lib\site-packages\custom_rasterizer\__init__.py", line 22, in <module>
    from .render import *
  File "E:\3D AI Studio\.venv\Lib\site-packages\custom_rasterizer\render.py", line 15, in <module>
    import custom_rasterizer_kernel
ImportError: DLL load failed while importing custom_rasterizer_kernel: The specified procedure could not be found.

