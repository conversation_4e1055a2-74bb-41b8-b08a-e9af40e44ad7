import os
import uuid
import shutil
import sys
import io
import base64
import requests
import json
import time
import threading
import atexit
import signal
from flask import Flask, request, jsonify, send_from_directory, Response
from flask_cors import CORS
from werkzeug.utils import secure_filename
from config_manager import get_config_manager

# Initialize logging system
from utils.logging_system import init_logging, get_logger, log_api_endpoint

# Initialize logging
logger = init_logging()
logger.log_system_info()
logger.info("3D AI Studio starting up", component="STARTUP")

# Core application setup - minimal dependencies for startup
print("Starting 3D AI Studio core application...")
print("All AI models and dependencies managed through Dependency Manager")

# Initialize pipelines as None - they will be loaded on-demand
trellis_pipeline = None
hunyuan3d_pipeline = None
hunyuan3d_direct_pipeline = None
text_to_image_pipeline = None
rembg_available = False

# Set up output directory
output_dir = os.path.join(os.path.dirname(__file__), 'output')
os.makedirs(output_dir, exist_ok=True)

print("Core application ready - use Dependency Manager for AI features")

# Lazy loading functions for pipelines
def get_trellis_pipeline():
    """Lazy load Trellis pipeline when needed."""
    global trellis_pipeline
    if trellis_pipeline is None:
        try:
            sys.path.append(os.path.join(os.path.dirname(__file__), 'pipelines', 'trellis_pipeline'))
            from trellis_pipeline import create_trellis_pipeline
            trellis_pipeline = create_trellis_pipeline(output_dir=output_dir)
            print("Trellis pipeline loaded successfully")
        except Exception as e:
            print(f"Failed to load Trellis pipeline: {e}")
            trellis_pipeline = False  # Mark as failed to avoid retrying
    return trellis_pipeline if trellis_pipeline is not False else None

def get_hunyuan3d_pipeline():
    """Get the Hunyuan3D-2 integrated pipeline (loaded at startup)."""
    global hunyuan3d_pipeline
    if hunyuan3d_pipeline is None:
        try:
            # Try to get the integrated pipeline (should be loaded at startup)
            from hunyuan3d_integrated_pipeline import get_hunyuan3d_integrated_pipeline
            integrated_pipeline = get_hunyuan3d_integrated_pipeline()

            if integrated_pipeline.is_available():
                print("Using Hunyuan3D-2 integrated pipeline")
                hunyuan3d_pipeline = integrated_pipeline
                return hunyuan3d_pipeline
            else:
                print("WARNING: Hunyuan3D-2 integrated pipeline not available - should have been loaded at startup")
                hunyuan3d_pipeline = False
        except Exception as e:
            print(f"Failed to load Hunyuan3D-2 integrated pipeline: {e}")
            hunyuan3d_pipeline = False  # Mark as failed to avoid retrying
    return hunyuan3d_pipeline if hunyuan3d_pipeline is not False else None

def get_hunyuan3d_direct_pipeline():
    """Load and return the Hunyuan3D direct pipeline."""
    global hunyuan3d_direct_pipeline
    if hunyuan3d_direct_pipeline is None:
        try:
            from hunyuan3d_direct_pipeline import get_hunyuan3d_direct_pipeline as get_direct_pipeline
            hunyuan3d_direct_pipeline = get_direct_pipeline()

            # Initialize the pipeline if not already done
            if not hunyuan3d_direct_pipeline.is_initialized:
                print("Initializing Hunyuan3D-2 direct pipeline...")

                def progress_callback(message):
                    print(f"[HUNYUAN3D] {message}")

                if hunyuan3d_direct_pipeline.initialize_pipelines(progress_callback):
                    print("Hunyuan3D-2 direct pipeline loaded successfully")
                else:
                    print("Warning: Hunyuan3D-2 direct pipeline initialization failed")
                    hunyuan3d_direct_pipeline = None

        except Exception as e:
            print(f"Warning: Could not load Hunyuan3D-2 direct pipeline: {e}")
            hunyuan3d_direct_pipeline = None
    return hunyuan3d_direct_pipeline

def get_text_to_image_pipeline():
    """Lazy load text-to-image pipeline when needed."""
    global text_to_image_pipeline
    if text_to_image_pipeline is None:
        try:
            from text_to_image_pipeline import create_text_to_image_pipeline
            text_to_image_pipeline = create_text_to_image_pipeline(
                output_dir=output_dir,
                model_preference="sdxl_turbo"
            )
            print("Text-to-image pipeline loaded successfully")
        except Exception as e:
            print(f"Failed to load text-to-image pipeline: {e}")
            text_to_image_pipeline = False  # Mark as failed to avoid retrying
    return text_to_image_pipeline if text_to_image_pipeline is not False else None

def get_rembg():
    """Lazy load REMBG when needed."""
    global rembg_available
    if not rembg_available:
        try:
            import rembg
            from PIL import Image
            rembg_available = True
            print("REMBG loaded successfully")
            return rembg, Image
        except ImportError as e:
            print(f"REMBG not available: {e}")
            return None, None
    else:
        import rembg
        from PIL import Image
        return rembg, Image

app = Flask(__name__, static_folder='Resources/UI/dist')
CORS(app)  # Enable CORS for all routes

# Track application start time for uptime calculation
app_start_time = time.time()

# Configure upload folder
UPLOAD_FOLDER = os.path.join(os.path.dirname(__file__), 'uploads')
OUTPUT_FOLDER = os.path.join(os.path.dirname(__file__), 'output')
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)

# Import progress functions from separate module to avoid circular imports
from progress_utils import update_progress, get_progress, clear_progress

# Import project manager
from project_manager import project_manager

# Note: Dependency Manager removed - all dependencies now handled at startup

# Background services API endpoints
@app.route('/api/background-services/status', methods=['GET'])
@log_api_endpoint("BACKGROUND_SERVICES")
def get_background_services_status():
    """Get the status of all background services."""
    try:
        from background_services import get_service_status
        status = get_service_status()
        return jsonify({
            'success': True,
            'data': status
        })
    except Exception as e:
        logger.error(f"Error getting background services status: {e}", component="BACKGROUND_SERVICES")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/background-services/<service_name>/status', methods=['GET'])
@log_api_endpoint("BACKGROUND_SERVICES")
def get_service_status_endpoint(service_name):
    """Get the status of a specific background service."""
    try:
        from background_services import get_service_status
        status = get_service_status(service_name)
        return jsonify({
            'success': True,
            'data': status
        })
    except Exception as e:
        logger.error(f"Error getting service {service_name} status: {e}", component="BACKGROUND_SERVICES")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/background-services/<service_name>/restart', methods=['POST'])
@log_api_endpoint("BACKGROUND_SERVICES")
def restart_service_endpoint(service_name):
    """Restart a specific background service."""
    try:
        from background_services import service_manager

        # Stop the service
        stop_success = service_manager.stop_service(service_name)
        if not stop_success:
            return jsonify({
                'success': False,
                'error': f'Failed to stop service {service_name}'
            }), 500

        # Wait a moment
        time.sleep(2)

        # Start the service
        start_success = service_manager.start_service(service_name)
        if start_success:
            return jsonify({
                'success': True,
                'message': f'Service {service_name} restarted successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': f'Failed to start service {service_name}'
            }), 500

    except Exception as e:
        logger.error(f"Error restarting service {service_name}: {e}", component="BACKGROUND_SERVICES")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500



@app.route('/')
def serve_ui():
    """Serve the UI from the static folder"""
    return send_from_directory(app.static_folder, 'index.html')

@app.route('/<path:path>')
def serve_static(path):
    """Serve static files from the UI build directory"""
    return send_from_directory(app.static_folder, path)

@app.route('/api/sample-images', methods=['GET'])
def get_sample_images():
    """
    Get all sample images from the sample_images folder.

    Returns:
        JSON list of sample images with their metadata
    """
    try:
        sample_images_dir = os.path.join(os.path.dirname(__file__), 'sample_images')

        # Create the directory if it doesn't exist
        if not os.path.exists(sample_images_dir):
            os.makedirs(sample_images_dir)
            return jsonify({'images': []})

        # Supported image extensions
        supported_extensions = {'.jpg', '.jpeg', '.png', '.webp', '.bmp', '.gif'}

        images = []
        for filename in os.listdir(sample_images_dir):
            if os.path.isfile(os.path.join(sample_images_dir, filename)):
                name, ext = os.path.splitext(filename)
                if ext.lower() in supported_extensions:
                    # Generate a clean display name from filename
                    display_name = name.replace('_', ' ').replace('-', ' ').title()

                    # Try to determine category from filename or use default
                    category = 'Objects'  # Default category
                    filename_lower = filename.lower()
                    if any(word in filename_lower for word in ['chair', 'table', 'lamp', 'furniture']):
                        category = 'Furniture'
                    elif any(word in filename_lower for word in ['shoe', 'watch', 'glasses', 'bag', 'clothing']):
                        category = 'Fashion'
                    elif any(word in filename_lower for word in ['phone', 'camera', 'headphones', 'electronics']):
                        category = 'Electronics'
                    elif any(word in filename_lower for word in ['plant', 'flower', 'tree', 'nature', 'organic']):
                        category = 'Nature'

                    images.append({
                        'id': len(images) + 1,
                        'name': display_name,
                        'filename': filename,
                        'url': f'/api/sample-images/{filename}',
                        'category': category,
                        'description': f'{category.lower()} sample'
                    })

        # Sort by category then by name
        images.sort(key=lambda x: (x['category'], x['name']))

        return jsonify({'images': images})

    except Exception as e:
        print(f"Error getting sample images: {str(e)}")
        return jsonify({'error': 'Failed to load sample images', 'images': []}), 500

@app.route('/api/sample-images/<filename>')
def serve_sample_image(filename):
    """
    Serve a sample image file.
    """
    try:
        sample_images_dir = os.path.join(os.path.dirname(__file__), 'sample_images')
        return send_from_directory(sample_images_dir, filename)
    except Exception as e:
        print(f"Error serving sample image {filename}: {str(e)}")
        return jsonify({'error': 'Image not found'}), 404

@app.route('/api/proxy-image', methods=['POST'])
def proxy_image():
    """
    Proxy endpoint to fetch external images and avoid CORS issues.

    Expected JSON payload:
    {
        "imageUrl": "https://example.com/image.jpg"
    }

    Returns:
        The image file as a blob
    """
    try:
        data = request.json
        if not data or 'imageUrl' not in data:
            return jsonify({'error': 'No image URL provided'}), 400

        image_url = data['imageUrl']

        # Fetch the image
        response = requests.get(image_url, timeout=10)
        response.raise_for_status()

        # Return the image with appropriate headers
        return response.content, 200, {
            'Content-Type': response.headers.get('Content-Type', 'image/jpeg'),
            'Content-Length': str(len(response.content))
        }

    except Exception as e:
        print(f"Error proxying image: {str(e)}")
        return jsonify({'error': 'Failed to fetch image'}), 500

@app.route('/api/progress/<session_id>')
def progress_stream(session_id):
    """
    Server-Sent Events endpoint for real-time progress updates.
    """
    def generate():
        try:
            print(f"SSE connection established for session: {session_id}")

            # Send initial connection confirmation
            yield f"data: {json.dumps({'status': 'connected', 'session_id': session_id})}\n\n"

            max_iterations = 120  # Maximum 60 seconds (120 * 0.5s)
            iterations = 0

            while iterations < max_iterations:
                try:
                    progress_data = get_progress(session_id)

                    # Always send data, even if empty
                    data_to_send = progress_data if progress_data else {}

                    # Debug: Log what we're sending to frontend
                    if progress_data:
                        print(f"SSE DEBUG: Sending {len(progress_data)} stages to frontend: {list(progress_data.keys())}")
                        if len(progress_data) > 1:
                            print(f"SSE DEBUG: Full progress data: {json.dumps(data_to_send)}")

                    yield f"data: {json.dumps(data_to_send)}\n\n"

                    # Stop streaming if all stages are complete
                    if progress_data and len(progress_data) >= 5:  # 5 stages total
                        all_complete = all(stage.get('progress', 0) >= 100 for stage in progress_data.values())
                        if all_complete:
                            print(f"All stages complete for session: {session_id}")
                            yield f"data: {json.dumps({'status': 'completed'})}\n\n"
                            break

                    time.sleep(0.5)  # Update every 500ms
                    iterations += 1

                except Exception as e:
                    print(f"Error in SSE loop: {e}")
                    yield f"data: {json.dumps({'error': str(e)})}\n\n"
                    break

            print(f"SSE connection closed for session: {session_id}")

        except Exception as e:
            print(f"Error in SSE generator: {e}")
            yield f"data: {json.dumps({'error': str(e)})}\n\n"

    try:
        response = Response(generate(), mimetype='text/event-stream')
        response.headers['Cache-Control'] = 'no-cache'
        response.headers['Connection'] = 'keep-alive'
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Headers'] = 'Cache-Control'
        response.headers['Access-Control-Allow-Methods'] = 'GET'
        response.headers['X-Accel-Buffering'] = 'no'  # Disable nginx buffering
        return response
    except Exception as e:
        print(f"Error creating SSE response: {e}")
        return jsonify({'error': 'Failed to create progress stream'}), 500

@app.route('/api/test-sse')
def test_sse():
    """Test SSE endpoint to verify connection works"""
    def generate():
        for i in range(5):
            yield f"data: {json.dumps({'test': i, 'message': f'Test message {i}'})}\n\n"
            time.sleep(1)
        yield f"data: {json.dumps({'status': 'test_complete'})}\n\n"

    response = Response(generate(), mimetype='text/event-stream')
    response.headers['Cache-Control'] = 'no-cache'
    response.headers['Connection'] = 'keep-alive'
    response.headers['Access-Control-Allow-Origin'] = '*'
    return response

@app.route('/api/progress-poll/<session_id>')
def progress_poll(session_id):
    """
    Polling endpoint for progress updates (fallback for SSE).
    """
    try:
        progress_data = get_progress(session_id)
        return jsonify(progress_data)
    except Exception as e:
        print(f"Error in progress polling: {e}")
        return jsonify({'error': str(e)}), 500



@app.route('/api/delighter/status', methods=['GET'])
@log_api_endpoint("DELIGHTER_STATUS")
def delighter_status():
    """
    Check the status of Agisoft De-Lighter shadow removal system.

    Returns:
        JSON with availability status and message
    """
    try:
        from pipelines.agisoft_delighter import AgisoftDelighter

        delighter = AgisoftDelighter()

        if delighter.is_available():
            return jsonify({
                'available': True,
                'message': 'Agisoft De-Lighter is ready for shadow removal',
                'delighter_path': delighter.delighter_path
            })
        else:
            return jsonify({
                'available': False,
                'message': 'Agisoft De-Lighter not found. Please install from https://www.agisoft.com/downloads/installer/',
                'installation_url': 'https://www.agisoft.com/downloads/installer/'
            })

    except ImportError as e:
        return jsonify({
            'available': False,
            'message': f"Agisoft De-Lighter module not found: {str(e)}"
        })
    except Exception as e:
        return jsonify({
            'available': False,
            'message': f'Error checking Agisoft De-Lighter status: {str(e)}'
        }), 500

@app.route('/api/delighter/preview', methods=['POST'])
@log_api_endpoint("DELIGHTER_PREVIEW")
def delighter_preview():
    """
    Preview Agisoft De-Lighter shadow removal effects on an uploaded image.

    Expected JSON payload:
    {
        "image_id": "uuid",
        "quality_mode": "high",
        "settings": {
            "quality": "high",
            "preserve_details": true,
            "auto_mask": true
        }
    }

    Returns:
        JSON with preview URL and processing info
    """
    try:
        data = request.get_json()
        image_id = data.get('image_id')
        quality_mode = data.get('quality_mode', 'high')
        settings = data.get('settings', {})

        if not image_id:
            return jsonify({'error': 'Missing image_id'}), 400

        # Find the original image file
        upload_dir = os.path.join(UPLOAD_FOLDER, image_id)
        if not os.path.exists(upload_dir):
            return jsonify({'error': 'Image not found'}), 404

        # Find the original image file (not the processed one)
        original_file = None
        for file in os.listdir(upload_dir):
            if not file.startswith('processed_') and file.lower().endswith(('.png', '.jpg', '.jpeg')):
                original_file = os.path.join(upload_dir, file)
                break

        if not original_file or not os.path.exists(original_file):
            return jsonify({'error': 'Original image file not found'}), 404

        logger.info(f"Previewing Agisoft De-Lighter shadow removal with quality: {quality_mode}", component="DELIGHTER_PREVIEW")

        try:
            from pipelines.agisoft_delighter import AgisoftDelighter

            delighter = AgisoftDelighter()

            if delighter.is_available():
                # Open the original image
                input_image = Image.open(original_file)

                # Configure settings
                delighter_settings = {
                    'quality': quality_mode,
                    'preserve_details': settings.get('preserve_details', True),
                    'auto_mask': settings.get('auto_mask', True)
                }

                # Apply shadow removal with the specified settings
                start_time = time.time()
                output_image = delighter.remove_shadows(input_image, delighter_settings)
                processing_time = time.time() - start_time

                logger.log_performance("delighter_shadow_removal_preview", processing_time,
                                      {'image_size': input_image.size, 'quality_mode': quality_mode})

                # Save the processed image
                processed_filename = f"delighter_preview_{int(time.time())}.png"
                processed_path = os.path.join(upload_dir, processed_filename)
                output_image.save(processed_path, format="PNG")

                # Convert to base64 for preview
                buffered = io.BytesIO()
                output_image.save(buffered, format="PNG")
                processed_image_data = base64.b64encode(buffered.getvalue()).decode('utf-8')

                return jsonify({
                    'success': True,
                    'preview_url': f'data:image/png;base64,{processed_image_data}',
                    'quality_mode': quality_mode,
                    'processing_time': round(processing_time, 2),
                    'settings_used': delighter_settings,
                    'preview_filename': processed_filename
                })

            else:
                return jsonify({'error': 'Agisoft De-Lighter not available. Please install from https://www.agisoft.com/downloads/installer/'}), 500

        except Exception as e:
            logger.error(f"Agisoft De-Lighter shadow removal preview failed: {e}", component="DELIGHTER_PREVIEW")
            return jsonify({'error': f'Shadow removal failed: {str(e)}'}), 500

    except Exception as e:
        logger.error(f"Preview Agisoft De-Lighter shadow removal error: {e}", component="DELIGHTER_PREVIEW")
        return jsonify({'error': str(e)}), 500





@app.route('/api/upload', methods=['POST'])
@log_api_endpoint("UPLOAD")
def upload_image():
    """
    Upload an image for processing.
    Removes the background using REMBG if available.

    Returns:
        JSON with the image ID, path, and processed image data.
    """
    if 'image' not in request.files:
        return jsonify({'error': 'No image provided'}), 400

    file = request.files['image']
    if file.filename == '':
        return jsonify({'error': 'No image selected'}), 400

    # Generate a unique ID for this upload
    image_id = str(uuid.uuid4())
    logger.set_session_id(image_id)
    logger.info(f"Image upload started: {file.filename}", component="UPLOAD",
                extra_data={'filename': file.filename, 'image_id': image_id})

    # Create a directory for this upload
    upload_dir = os.path.join(UPLOAD_FOLDER, image_id)
    os.makedirs(upload_dir, exist_ok=True)

    # Save the original uploaded file
    original_filename = secure_filename(file.filename)
    original_path = os.path.join(upload_dir, original_filename)
    file.save(original_path)

    # Process the image to remove background if REMBG is available
    # Always save as PNG to support transparency
    processed_filename = 'processed_' + os.path.splitext(original_filename)[0] + '.png'
    processed_path = os.path.join(upload_dir, processed_filename)
    processed_image_data = None

    logger.info(f"Starting background removal for {original_filename}", component="BACKGROUND_REMOVAL")
    logger.debug(f"Original path: {original_path}", component="BACKGROUND_REMOVAL")
    logger.debug(f"Processed path: {processed_path}", component="BACKGROUND_REMOVAL")

    # Try to load REMBG for background removal
    rembg, Image = get_rembg()
    if rembg and Image:
        try:
            # Open the image
            logger.debug("Opening image...", component="BACKGROUND_REMOVAL")
            input_image = Image.open(original_path)
            logger.info(f"Image opened successfully. Format: {input_image.format}, Size: {input_image.size}",
                       component="BACKGROUND_REMOVAL")

            # Remove the background using REMBG
            logger.info("Removing background...", component="BACKGROUND_REMOVAL")
            start_time = time.time()
            output_image = rembg.remove(input_image)
            bg_removal_time = time.time() - start_time
            logger.log_performance("background_removal", bg_removal_time,
                                  {'image_size': input_image.size, 'format': input_image.format})
            logger.info(f"Background removal completed in {bg_removal_time:.2f}s", component="BACKGROUND_REMOVAL")

            # Save the processed image as PNG to support transparency
            print("Saving processed image...")
            output_image.save(processed_path, 'PNG')
            print(f"Processed image saved to {processed_path}")

            # Convert the processed image to base64 for preview
            print("Converting to base64 for preview...")
            buffered = io.BytesIO()
            output_image.save(buffered, format="PNG")
            processed_image_data = base64.b64encode(buffered.getvalue()).decode('utf-8')
            print("Base64 conversion completed")

            print(f"Background removal completed successfully for {original_filename}")

        except Exception as e:
            print(f"Error in image processing: {str(e)}")
            print(f"Error type: {type(e).__name__}")
            import traceback
            print("Full traceback:")
            print(traceback.format_exc())
            # If there's an error, use the original image
            shutil.copy(original_path, processed_path)
    else:
        print("REMBG not available, using original image")
        # If REMBG is not available, just use the original image
        shutil.copy(original_path, processed_path)

    return jsonify({
        'image_id': image_id,
        'original_filename': original_filename,
        'filename': processed_filename,  # Use the processed image for generation
        'path': processed_path,
        'processed_image_data': processed_image_data  # Base64 encoded image data for preview
    })

@app.route('/api/generate-text-to-3d', methods=['POST'])
@log_api_endpoint("TEXT_TO_3D")
def generate_text_to_3d():
    """
    Generate a 3D model from a text prompt using text-to-image + Trellis pipeline.

    Expected JSON payload:
    {
        "prompt": "A red sports car",
        "settings": {
            "seed": 42,
            "ss_steps": 12,
            "ss_cfg_strength": 7.5,
            "slat_steps": 12,
            "slat_cfg_strength": 3.0,
            "simplify": 0.95,
            "texture_size": 1024,
            "img_guidance_scale": 7.5,
            "img_steps": 20
        }
    }

    Returns:
        JSON with paths to the generated files.
    """
    try:
        data = request.get_json()
        prompt = data.get('prompt')
        settings = data.get('settings', {})
        image_model = data.get('image_model', 'sdxl_turbo')  # Get the selected image model
        pipeline_type = data.get('pipeline', 'trellis')  # Get the selected 3D pipeline

        if not prompt or not prompt.strip():
            return jsonify({'error': 'Missing or empty prompt'}), 400

        # Select the appropriate 3D pipeline using lazy loading
        selected_pipeline = None
        if pipeline_type == 'hunyuan3d':
            selected_pipeline = get_hunyuan3d_pipeline()
            if selected_pipeline and selected_pipeline.is_available():
                print("Selected Hunyuan3D-2 pipeline for text-to-3D generation")
            else:
                return jsonify({'error': 'Hunyuan3D-2 pipeline is not available. Install through Dependency Manager.'}), 500
        else:  # Default to Trellis
            selected_pipeline = get_trellis_pipeline()
            if selected_pipeline and selected_pipeline.is_available():
                print("Selected Trellis pipeline for text-to-3D generation")
            else:
                return jsonify({'error': 'Trellis pipeline is not available. Install through Dependency Manager.'}), 500

        # For Hunyuan3D-2, we can use its built-in text-to-image, otherwise use the separate pipeline
        if pipeline_type == 'hunyuan3d':
            # Hunyuan3D-2 has its own text-to-image pipeline
            pass
        else:
            # Check if text-to-image pipeline is available for Trellis using lazy loading
            text_to_image_pipeline = get_text_to_image_pipeline()
            if not text_to_image_pipeline:
                return jsonify({'error': 'Text-to-image pipeline is not available. Install through Dependency Manager.'}), 500

            # Try to check availability without triggering reinitialization
            try:
                # Don't call is_available() as it might trigger flash attention issues
                # Just check if the pipeline object exists
                if not hasattr(text_to_image_pipeline, 'model_preference'):
                    return jsonify({'error': 'Text-to-image pipeline is not properly initialized'}), 500
            except Exception as e:
                print(f"Warning: Text-to-image pipeline check failed: {e}")
                return jsonify({'error': f'Text-to-image pipeline is not available: {str(e)}'}), 500

        try:
            # Get session ID from request data or generate one
            session_id = data.get('session_id')
            if not session_id:
                session_id = str(uuid.uuid4())
            logger.set_session_id(session_id)

            logger.log_pipeline_start("text_to_3d", {
                'prompt': prompt,
                'settings': settings,
                'image_model': image_model,
                'pipeline': pipeline_type,
                'session_id': session_id
            })

            logger.info(f"Starting text-to-3D generation with {pipeline_type} pipeline", component="TEXT_TO_3D",
                       extra_data={'prompt': prompt[:100], 'image_model': image_model, 'pipeline': pipeline_type})

            # Create project
            project_name = prompt[:50] + "..." if len(prompt) > 50 else prompt
            project_id = project_manager.create_project(
                name=project_name,
                project_type='text-to-3d',
                original_prompt=prompt
            )

            # Clear any existing progress data for this session and initialize progress tracking
            clear_progress(session_id)
            from progress_utils import update_progress

            # Generate 3D model using the selected pipeline
            if pipeline_type == 'hunyuan3d':
                # Use Hunyuan3D-2's native server for text-to-3D generation
                print(f"Using Hunyuan3D-2 native server for text-to-3D generation")
                update_progress(session_id, 'text_to_3d', 0, 'Starting Hunyuan3D-2 text-to-3D generation...')

                # Convert settings to Hunyuan3D-2 format
                hunyuan_settings = {
                    'seed': settings.get('seed', 1234),
                    'octree_resolution': settings.get('octree_resolution', 128),
                    'num_inference_steps': settings.get('num_inference_steps', 5),
                    'guidance_scale': settings.get('guidance_scale', 5.0),
                    'enable_texture': settings.get('enable_texture', True),  # Default to True for Turbo Paint
                    'enable_texture_enhancement': settings.get('enable_texture_enhancement', False),  # New 4x upscaling option
                    'face_count': settings.get('face_count', 40000)
                }

                # Store generation settings in project
                generation_settings = {
                    'pipeline': 'hunyuan3d',
                    'hunyuan_settings': hunyuan_settings,
                    'prompt': prompt
                }
                project_manager.update_project_settings(project_id, generation_settings)

                # Progress callback for native client
                def progress_callback(message):
                    if "Starting" in message:
                        update_progress(session_id, 'text_to_3d', 10, message)
                    elif "Sending request" in message or "uploaded" in message:
                        update_progress(session_id, 'text_to_3d', 25, message)
                    elif "Processing" in message:
                        update_progress(session_id, 'text_to_3d', 80, message)
                    else:
                        update_progress(session_id, 'text_to_3d', 50, message)

                # Generate using Hunyuan3D-2 native client
                if hasattr(selected_pipeline, 'generate_text_to_3d'):
                    # Using native client
                    result = selected_pipeline.generate_text_to_3d(
                        prompt,
                        hunyuan_settings,
                        session_id,
                        progress_callback
                    )
                else:
                    # Fallback to original pipeline
                    result = selected_pipeline.generate_text_to_3d(prompt, hunyuan_settings, session_id)

                # Update progress based on result
                if result.get('success'):
                    update_progress(session_id, 'text_to_3d', 95, 'Text-to-3D generation completed successfully')
                    # For native client, the result structure is different
                    image_path = result.get('generated_image') or result.get('primary_output')

                    # Update result to match expected format
                    if result.get('primary_output') and not result.get('glb'):
                        result['glb'] = result['primary_output']
                else:
                    update_progress(session_id, 'text_to_3d', 0, f'Generation failed: {result.get("error", "Unknown error")}')
                    return jsonify({'error': result.get('error', 'Text-to-3D generation failed')}), 500

            else:
                # Use Trellis pipeline with separate text-to-image
                print(f"Using Trellis pipeline for text-to-3D generation")
                update_progress(session_id, 'text_to_image', 0, 'Starting text-to-image generation...')

                # Step 1: Generate image from text
                print("Step 1: Generating image from text...")
                print(f"Using image model: {image_model}")
                update_progress(session_id, 'text_to_image', 25, 'Loading text-to-image model...')

                # Update the text-to-image pipeline model preference if needed
                try:
                    if text_to_image_pipeline.model_preference != image_model:
                        print(f"Switching text-to-image model from {text_to_image_pipeline.model_preference} to {image_model}")
                        text_to_image_pipeline.model_preference = image_model
                        text_to_image_pipeline._initialized = False  # Force reinitialization
                except Exception as e:
                    print(f"Warning: Could not update text-to-image model preference: {e}")
                    # Continue with current model

                image_result = text_to_image_pipeline.generate_image(prompt, settings, session_id=session_id)
                image_path = image_result['image_path']

                print(f"Image generated successfully: {image_path}")
                update_progress(session_id, 'text_to_image', 100, 'Text-to-image generation completed')

                # Step 2: Generate 3D model from the generated image
                print("Step 2: Generating 3D model from image...")
                update_progress(session_id, 'preprocessing', 0, 'Starting 3D model generation...')

                # Convert settings to Trellis format
                trellis_settings = {
                    'seed': settings.get('seed', 1),
                    'ss_steps': settings.get('ss_steps', 12),
                    'ss_cfg_strength': settings.get('ss_cfg_strength', 7.5),
                    'slat_steps': settings.get('slat_steps', 12),
                    'slat_cfg_strength': settings.get('slat_cfg_strength', 3.0),
                    'simplify': settings.get('simplify', 0.95),
                    'texture_size': settings.get('texture_size', 1024)
                }

                # Store generation settings in project
                generation_settings = {
                    'pipeline': 'trellis',
                    'text_to_image_model': image_model,
                    'image_size': settings.get('image_size', 512),
                    'guidance_scale': settings.get('guidance_scale', 7.5),
                    'num_inference_steps': settings.get('num_inference_steps', 4),
                    'seed': settings.get('seed'),
                    'trellis_settings': trellis_settings
                }
                project_manager.update_project_settings(project_id, generation_settings)

                # Generate the 3D model with progress tracking
                result = selected_pipeline.process_image(
                    image_path,
                    settings=trellis_settings,
                    session_id=session_id
                )

            print(f"3D model generated successfully. Output files: {result}")

            # Apply Agisoft De-Lighter post-processing if enabled
            model_file = result.get('glb') or result.get('gaussian')
            video_file = result.get('video')

            # Check if Agisoft De-Lighter should be applied to the generated 3D model
            enable_delighter = settings.get('enable_delighter', False)
            delighter_quality = settings.get('delighter_quality', 'high')

            if enable_delighter and model_file and os.path.exists(model_file):
                try:
                    from pipelines.agisoft_delighter import AgisoftDelighter
                    from progress_utils import update_progress

                    delighter = AgisoftDelighter()

                    if delighter.is_available():
                        logger.info(f"Applying Agisoft De-Lighter post-processing to text-to-3D model with quality: {delighter_quality}", component="DELIGHTER_POST")

                        # Update progress - start texture enhancement
                        update_progress(session_id, 'texture_enhancement', 0, 'Starting texture enhancement with Agisoft De-Lighter...')

                        # Create enhanced model path
                        model_dir = os.path.dirname(model_file)
                        model_name = os.path.splitext(os.path.basename(model_file))[0]
                        enhanced_model_path = os.path.join(model_dir, f"{model_name}_delighted.glb")

                        # Configure Delighter settings for 3D model processing
                        delighter_settings = {
                            'quality': delighter_quality,
                            'preserve_details': True,
                            'auto_mask': True
                        }

                        start_time = time.time()

                        # Update progress - processing
                        update_progress(session_id, 'texture_enhancement', 25, 'Processing 3D model textures...')

                        # Process the 3D model textures
                        enhanced_model = delighter.process_3d_model_textures(
                            model_file,
                            enhanced_model_path,
                            delighter_settings
                        )

                        delighter_time = time.time() - start_time

                        # Update progress - finalizing
                        update_progress(session_id, 'texture_enhancement', 90, 'Finalizing enhanced textures...')

                        # Use the enhanced model as the final output
                        if os.path.exists(enhanced_model):
                            model_file = enhanced_model
                            result['glb'] = enhanced_model  # Update result dict
                            logger.log_performance("delighter_post_text_to_3d", delighter_time,
                                                  {'quality': delighter_quality, 'model_size': os.path.getsize(model_file)})
                            logger.info(f"Agisoft De-Lighter post-processing completed in {delighter_time:.2f}s", component="DELIGHTER_POST")

                            # Update progress - completed
                            update_progress(session_id, 'texture_enhancement', 100, 'Texture enhancement completed successfully')
                        else:
                            logger.warning("Enhanced model not created, using original", component="DELIGHTER_POST")
                            update_progress(session_id, 'texture_enhancement', 100, 'Texture enhancement skipped - using original model')
                    else:
                        logger.warning("Agisoft De-Lighter not available, skipping post-processing", component="DELIGHTER_POST")
                        update_progress(session_id, 'texture_enhancement', 100, 'Texture enhancement skipped - Delighter not available')

                except Exception as e:
                    logger.error(f"Agisoft De-Lighter post-processing failed: {e}", component="DELIGHTER_POST")
                    print(f"Delighter post-processing failed, using original model: {e}")
                    update_progress(session_id, 'texture_enhancement', 100, 'Texture enhancement failed - using original model')
                    # Continue with the original model

            # Update project with enhanced prompt
            enhanced_prompt = image_result.get('prompt', prompt)
            if enhanced_prompt != prompt:
                metadata = project_manager.get_project_metadata(project_id)
                if metadata:
                    metadata['enhanced_prompt'] = enhanced_prompt
                    metadata_path = project_manager.metadata_dir / f"{project_id}.json"
                    with open(metadata_path, 'w') as f:
                        import json
                        json.dump(metadata, f, indent=2)

            print(f"DEBUG: Attempting to save project files:")
            print(f"  Project ID: {project_id}")
            print(f"  Model file: {model_file}")
            print(f"  Video file: {video_file}")
            print(f"  Generated image: {image_path}")
            print(f"  Model file exists: {os.path.exists(model_file) if model_file else False}")
            print(f"  Video file exists: {os.path.exists(video_file) if video_file else False}")
            print(f"  Generated image exists: {os.path.exists(image_path) if image_path else False}")

            success = project_manager.update_project_files(
                project_id=project_id,
                model_file=model_file,
                video_file=video_file,
                generated_image=image_path,
                original_image=image_path  # Use generated image as fallback for thumbnail
            )
            print(f"DEBUG: Project file update success: {success}")

            # Extract mesh statistics for the generated model
            mesh_stats = {}
            if model_file and os.path.exists(model_file):
                try:
                    from mesh_stats_utils import get_mesh_statistics, format_file_size
                    mesh_stats = get_mesh_statistics(model_file)
                    logger.info(f"Extracted mesh statistics: {mesh_stats.get('vertices', 0)} vertices, {mesh_stats.get('faces', 0)} faces",
                               component="TEXT_TO_3D")
                except Exception as e:
                    logger.warning(f"Could not extract mesh statistics: {e}", component="TEXT_TO_3D")
                    mesh_stats = {
                        'vertices': 0,
                        'faces': 0,
                        'file_size': os.path.getsize(model_file) if os.path.exists(model_file) else 0,
                        'file_format': 'glb'
                    }

            # Prepare response with filenames and statistics
            response = {
                'success': True,
                'session_id': session_id,
                'project_id': project_id,
                'generated_image': os.path.basename(image_path),
                'original_prompt': prompt,
                'enhanced_prompt': image_result.get('prompt', prompt) if 'image_result' in locals() else prompt,
                'files': {},
                'mesh_stats': mesh_stats
            }

            # Add files that were generated
            if 'gaussian' in result and result['gaussian']:
                response['files']['gaussian'] = os.path.basename(result['gaussian'])
            if 'video' in result and result['video']:
                response['files']['video'] = os.path.basename(result['video'])
            if 'glb' in result and result['glb']:
                response['files']['glb'] = os.path.basename(result['glb'])

            print(f"Returning response: {response}")
            return jsonify(response)

        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            print(f"Error in text-to-3D generation: {str(e)}")
            print(f"Detailed error: {error_details}")

            # Mark project as failed if it was created
            if 'project_id' in locals():
                project_manager.mark_project_failed(project_id, str(e))

            return jsonify({
                'error': f'Error in text-to-3D generation: {str(e)}',
                'details': error_details
            }), 500

    except Exception as e:
        print(f"Error in generate_text_to_3d: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/generate', methods=['POST'])
@log_api_endpoint("IMAGE_TO_3D")
def generate_model():
    """
    Generate a 3D model from an uploaded image using Trellis.

    Expected JSON payload:
    {
        "image_id": "uuid",
        "filename": "image.jpg",
        "settings": {
            "ss_steps": 12,
            "ss_cfg_strength": 7.5,
            "slat_steps": 12,
            "slat_cfg_strength": 3.0,
            "seed": 42,
            "simplify": 0.95,
            "texture_size": 1024
        }
    }

    Returns:
        JSON with paths to the generated files.
    """
    try:
        print("DEBUG: ===== GENERATE MODEL ENDPOINT CALLED =====")
        print(f"DEBUG: Request method: {request.method}")
        print(f"DEBUG: Request content type: {request.content_type}")

        data = request.get_json()
        print(f"DEBUG: Raw request data: {data}")

        if not data:
            print("ERROR: No JSON data received")
            return jsonify({'error': 'No JSON data received'}), 400

        image_id = data.get('image_id')
        filename = data.get('filename')
        settings = data.get('settings', {})
        pipeline_type = data.get('pipeline', 'trellis')  # Get the selected 3D pipeline

        # Debug: Log received data from frontend
        print(f"DEBUG: ===== IMAGE-TO-3D GENERATION REQUEST =====")
        print(f"DEBUG: Received settings from frontend: {settings}")
        print(f"DEBUG: Pipeline type from frontend: {pipeline_type}")
        print(f"DEBUG: Image ID: {image_id}")
        print(f"DEBUG: Filename: {filename}")
        print(f"DEBUG: Full request data keys: {list(data.keys())}")
        print(f"DEBUG: enable_lighting_optimizer in settings: {'enable_lighting_optimizer' in settings}")
        if 'enable_lighting_optimizer' in settings:
            print(f"DEBUG: enable_lighting_optimizer value: {settings['enable_lighting_optimizer']}")
        print(f"DEBUG: =============================================")

        if not image_id or not filename:
            print(f"ERROR: Missing required fields - image_id: {image_id}, filename: {filename}")
            return jsonify({'error': 'Missing image_id or filename'}), 400

        # Get the processed image path - fix the path construction
        image_path = os.path.join(UPLOAD_FOLDER, image_id, filename)
        if not os.path.exists(image_path):
            return jsonify({'error': f'Processed image not found at {image_path}'}), 404

        # Select the appropriate 3D pipeline using lazy loading
        selected_pipeline = None
        print(f"DEBUG: Selecting pipeline for type: {pipeline_type}")

        if pipeline_type == 'hunyuan3d':
            print("DEBUG: Attempting to load Hunyuan3D-2 pipeline...")
            selected_pipeline = get_hunyuan3d_pipeline()

            if selected_pipeline:
                print(f"DEBUG: Hunyuan3D-2 pipeline object created successfully")
                is_available = selected_pipeline.is_available()
                print(f"DEBUG: Hunyuan3D-2 availability check result: {is_available}")

                if is_available:
                    print("SUCCESS: Selected Hunyuan3D-2 pipeline for image-to-3D generation")
                    print(f"DEBUG: Hunyuan3D-2 model path: {getattr(selected_pipeline, 'model_path', 'Unknown')}")
                    print(f"DEBUG: Hunyuan3D-2 subfolder: {getattr(selected_pipeline, 'subfolder', 'Unknown')}")
                else:
                    print("ERROR: Hunyuan3D-2 pipeline is not available")
                    return jsonify({'error': 'Hunyuan3D-2 pipeline is not available. Install through Dependency Manager.'}), 500
            else:
                print("ERROR: Failed to create Hunyuan3D-2 pipeline object")
                return jsonify({'error': 'Failed to load Hunyuan3D-2 pipeline. Check installation.'}), 500
        else:  # Default to Trellis
            print("DEBUG: Using Trellis pipeline (default)")
            selected_pipeline = get_trellis_pipeline()
            if selected_pipeline and selected_pipeline.is_available():
                print("SUCCESS: Selected Trellis pipeline for image-to-3D generation")
            else:
                print("ERROR: Trellis pipeline is not available")
                return jsonify({'error': 'Trellis pipeline is not available. Install through Dependency Manager.'}), 500

        # Note: Agisoft De-Lighter will be applied as post-processing after 3D generation

        # Import progress utilities
        from progress_utils import update_progress, clear_progress

        # Clear any existing progress data for this session
        clear_progress(image_id)

        # Create project
        project_name = f"Image Model {image_id[:8]}"
        project_id = project_manager.create_project(
            name=project_name,
            project_type='image-to-3d',
            original_image_path=image_path
        )

        # Generate 3D model using the selected pipeline
        if selected_pipeline:
            try:
                if pipeline_type == 'hunyuan3d':
                    # Convert settings to Hunyuan3D-2 format
                    hunyuan_settings = {
                        'seed': settings.get('seed', 1234),
                        'hunyuan_model': settings.get('hunyuan_model', 'turbo'),  # Model selection
                        'octree_resolution': settings.get('octree_resolution', 128),
                        'num_inference_steps': settings.get('num_inference_steps', 5),
                        'guidance_scale': settings.get('guidance_scale', 5.0),
                        'enable_texture': settings.get('enable_texture', True),  # Default to True for Turbo Paint
                        'enable_texture_enhancement': settings.get('enable_texture_enhancement', False),  # New 4x upscaling option
                        'face_count': settings.get('face_count', 40000)
                    }

                    # Store generation settings in project
                    generation_settings = {
                        'pipeline': 'hunyuan3d',
                        'hunyuan_settings': hunyuan_settings
                    }
                    project_manager.update_project_settings(project_id, generation_settings)

                    # Add detailed logging
                    print(f"HUNYUAN3D: Starting Hunyuan3D-2 model generation with settings: {hunyuan_settings}")
                    print(f"HUNYUAN3D: Input image path: {image_path}")
                    print(f"HUNYUAN3D: Pipeline type: {type(selected_pipeline).__name__}")

                    # Progress callback for native client
                    def progress_callback(message):
                        if "Starting" in message:
                            update_progress(image_id, 'image_to_3d', 10, message)
                        elif "uploaded" in message or "Sending request" in message:
                            update_progress(image_id, 'image_to_3d', 25, message)
                        elif "Processing" in message:
                            update_progress(image_id, 'image_to_3d', 80, message)
                        else:
                            update_progress(image_id, 'image_to_3d', 50, message)

                    # Generate the model with real progress tracking
                    print("HUNYUAN3D: Calling selected_pipeline for Hunyuan3D-2...")
                    print(f"PROGRESS DEBUG: Using session_id for progress tracking: {image_id}")

                    if hasattr(selected_pipeline, 'generate_image_to_3d'):
                        # Using native client
                        result = selected_pipeline.generate_image_to_3d(
                            image_path,
                            hunyuan_settings,
                            image_id,
                            progress_callback
                        )
                        # Convert native client result to expected format
                        if result.get('success') and result.get('primary_output'):
                            result['glb'] = result['primary_output']
                    else:
                        # Fallback to original pipeline
                        result = selected_pipeline.process_image(
                            image_path,
                            settings=hunyuan_settings,
                            session_id=image_id  # Pass session_id for progress tracking
                        )

                    print(f"HUNYUAN3D: Hunyuan3D-2 generation completed with result: {result}")

                    # Update progress based on result
                    if result.get('success'):
                        update_progress(image_id, 'image_to_3d', 95, 'Image-to-3D generation completed successfully')
                    else:
                        update_progress(image_id, 'image_to_3d', 0, f'Generation failed: {result.get("error", "Unknown error")}')
                        return jsonify({'error': result.get('error', 'Image-to-3D generation failed')}), 500

                else:
                    # Convert settings to Trellis format
                    trellis_settings = {
                        'seed': settings.get('seed', 1),
                        'ss_steps': settings.get('ss_steps', 12),
                        'ss_cfg_strength': settings.get('ss_cfg_strength', 7.5),
                        'slat_steps': settings.get('slat_steps', 12),
                        'slat_cfg_strength': settings.get('slat_cfg_strength', 3.0),
                        'simplify': settings.get('simplify', 0.95),
                        'texture_size': settings.get('texture_size', 1024),
                        # Include lighting optimizer setting
                        'enable_lighting_optimizer': settings.get('enable_lighting_optimizer', True)
                    }

                    # Store generation settings in project
                    generation_settings = {
                        'pipeline': 'trellis',
                        'trellis_settings': trellis_settings
                    }
                    project_manager.update_project_settings(project_id, generation_settings)

                    # Add detailed logging
                    print(f"Starting Trellis model generation with settings: {trellis_settings}")
                    print(f"Input image path: {image_path}")

                    # Generate the model with real progress tracking
                    result = selected_pipeline.process_image(
                        image_path,
                        settings=trellis_settings,
                        session_id=image_id  # Pass session_id for progress tracking
                    )
                print(f"Successfully generated model. Output files: {result}")

                # Apply Agisoft De-Lighter post-processing if enabled
                model_file = result.get('glb') or result.get('gaussian')
                video_file = result.get('video')

                # Check if Agisoft De-Lighter should be applied to the generated 3D model
                enable_delighter = settings.get('enable_delighter', False)
                delighter_quality = settings.get('delighter_quality', 'high')

                if enable_delighter and model_file and os.path.exists(model_file):
                    try:
                        from pipelines.agisoft_delighter import AgisoftDelighter
                        from progress_utils import update_progress

                        delighter = AgisoftDelighter()

                        if delighter.is_available():
                            logger.info(f"Applying Agisoft De-Lighter post-processing to 3D model with quality: {delighter_quality}", component="DELIGHTER_POST")

                            # Update progress - start texture enhancement
                            update_progress(image_id, 'texture_enhancement', 0, 'Starting texture enhancement with Agisoft De-Lighter...')

                            # Create enhanced model path
                            model_dir = os.path.dirname(model_file)
                            model_name = os.path.splitext(os.path.basename(model_file))[0]
                            enhanced_model_path = os.path.join(model_dir, f"{model_name}_delighted.glb")

                            # Configure Delighter settings for 3D model processing
                            delighter_settings = {
                                'quality': delighter_quality,
                                'preserve_details': True,
                                'auto_mask': True
                            }

                            start_time = time.time()

                            # Update progress - processing
                            update_progress(image_id, 'texture_enhancement', 25, 'Processing 3D model textures...')

                            # Process the 3D model textures
                            enhanced_model = delighter.process_3d_model_textures(
                                model_file,
                                enhanced_model_path,
                                delighter_settings
                            )

                            delighter_time = time.time() - start_time

                            # Update progress - finalizing
                            update_progress(image_id, 'texture_enhancement', 90, 'Finalizing enhanced textures...')

                            # Use the enhanced model as the final output
                            if os.path.exists(enhanced_model):
                                model_file = enhanced_model
                                result['glb'] = enhanced_model  # Update result dict
                                logger.log_performance("delighter_post_3d", delighter_time,
                                                      {'quality': delighter_quality, 'model_size': os.path.getsize(model_file)})
                                logger.info(f"Agisoft De-Lighter post-processing completed in {delighter_time:.2f}s", component="DELIGHTER_POST")

                                # Update progress - completed
                                update_progress(image_id, 'texture_enhancement', 100, 'Texture enhancement completed successfully')
                            else:
                                logger.warning("Enhanced model not created, using original", component="DELIGHTER_POST")
                                update_progress(image_id, 'texture_enhancement', 100, 'Texture enhancement skipped - using original model')
                        else:
                            logger.warning("Agisoft De-Lighter not available, skipping post-processing", component="DELIGHTER_POST")
                            update_progress(image_id, 'texture_enhancement', 100, 'Texture enhancement skipped - Delighter not available')

                    except Exception as e:
                        logger.error(f"Agisoft De-Lighter post-processing failed: {e}", component="DELIGHTER_POST")
                        print(f"Delighter post-processing failed, using original model: {e}")
                        update_progress(image_id, 'texture_enhancement', 100, 'Texture enhancement failed - using original model')
                        # Continue with the original model

                print(f"DEBUG: Attempting to save project files:")
                print(f"  Project ID: {project_id}")
                print(f"  Model file: {model_file}")
                print(f"  Video file: {video_file}")
                print(f"  Model file exists: {os.path.exists(model_file) if model_file else False}")
                print(f"  Video file exists: {os.path.exists(video_file) if video_file else False}")

                success = project_manager.update_project_files(
                    project_id=project_id,
                    model_file=model_file,
                    video_file=video_file,
                    original_image=image_path  # Pass original image for fallback thumbnail
                )
                print(f"DEBUG: Project file update success: {success}")

                # Extract mesh statistics for the generated model
                mesh_stats = {}
                if model_file and os.path.exists(model_file):
                    try:
                        from mesh_stats_utils import get_mesh_statistics, format_file_size
                        mesh_stats = get_mesh_statistics(model_file)
                        logger.info(f"Extracted mesh statistics: {mesh_stats.get('vertices', 0)} vertices, {mesh_stats.get('faces', 0)} faces",
                                   component="IMAGE_TO_3D")
                    except Exception as e:
                        logger.warning(f"Could not extract mesh statistics: {e}", component="IMAGE_TO_3D")
                        mesh_stats = {
                            'vertices': 0,
                            'faces': 0,
                            'file_size': os.path.getsize(model_file) if os.path.exists(model_file) else 0,
                            'file_format': 'glb'
                        }

                # Prepare response with filenames and statistics
                response = {
                    'success': True,
                    'project_id': project_id,
                    'files': {},
                    'mesh_stats': mesh_stats
                }

                # Add files that were generated
                if 'gaussian' in result and result['gaussian']:
                    response['files']['gaussian'] = os.path.basename(result['gaussian'])
                if 'video' in result and result['video']:
                    response['files']['video'] = os.path.basename(result['video'])
                if 'glb' in result and result['glb']:
                    response['files']['glb'] = os.path.basename(result['glb'])

                print(f"Returning response: {response}")
                return jsonify(response)

            except Exception as e:
                import traceback
                error_details = traceback.format_exc()
                print(f"Error generating 3D model: {str(e)}")
                print(f"Detailed error: {error_details}")

                # Mark project as failed
                project_manager.mark_project_failed(project_id, str(e))

                return jsonify({
                    'error': f'Error generating 3D model: {str(e)}',
                    'details': error_details
                }), 500

        # If Trellis is not available, return error
        else:
            print("Trellis pipeline not available")
            return jsonify({
                'error': 'Trellis pipeline is not available. Please ensure it is properly installed and configured.'
            }), 500

    except Exception as e:
        print(f"Error in generate_model: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/files/<filename>')
def get_file(filename):
    """
    Serve a generated file.

    Args:
        filename (str): Name of the file to serve.

    Returns:
        The requested file.
    """
    return send_from_directory(OUTPUT_FOLDER, filename)

@app.route('/api/pipelines/available', methods=['GET'])
def get_available_pipelines():
    """
    Get the list of available 3D generation pipelines.

    Returns:
        JSON with available pipelines and their status.
    """
    try:
        pipelines = []

        # Check Trellis pipeline using lazy loading
        trellis_pipeline = get_trellis_pipeline()
        if trellis_pipeline and trellis_pipeline.is_available():
            pipelines.append({
                'id': 'trellis',
                'name': 'Trellis',
                'description': 'High-quality 3D generation with structured latent diffusion',
                'available': True,
                'features': ['High Quality', 'Structured Latents', 'Multi-format Output']
            })
        else:
            pipelines.append({
                'id': 'trellis',
                'name': 'Trellis',
                'description': 'High-quality 3D generation with structured latent diffusion',
                'available': False,
                'features': ['High Quality', 'Structured Latents', 'Multi-format Output']
            })

        # Check Hunyuan3D-2 pipeline using lazy loading
        hunyuan_pipeline_instance = get_hunyuan3d_pipeline()
        is_available = False

        print(f"DEBUG: ===== PIPELINE AVAILABILITY CHECK =====")
        print(f"DEBUG: Hunyuan3D pipeline object: {hunyuan_pipeline_instance}")
        print(f"DEBUG: Pipeline type: {type(hunyuan_pipeline_instance).__name__ if hunyuan_pipeline_instance else 'None'}")

        if hunyuan_pipeline_instance:
            # Check if it's the native client or original pipeline
            if hasattr(hunyuan_pipeline_instance, 'is_server_available'):
                # Native client
                print(f"DEBUG: Using native client availability check")
                is_available = hunyuan_pipeline_instance.is_server_available()
                print(f"DEBUG: Native client server available: {is_available}")
            elif hasattr(hunyuan_pipeline_instance, 'is_available'):
                # Original pipeline
                print(f"DEBUG: Using original pipeline availability check")
                is_available = hunyuan_pipeline_instance.is_available()
                print(f"DEBUG: Original pipeline available: {is_available}")
            else:
                print(f"DEBUG: Pipeline object has no availability check method")
        else:
            print(f"DEBUG: No Hunyuan3D pipeline object available")

        print(f"DEBUG: Final availability result: {is_available}")
        print(f"DEBUG: =========================================")

        if is_available:
            pipelines.append({
                'id': 'hunyuan3d',
                'name': 'Hunyuan3D-2 Native',
                'description': 'Fast 3D generation with built-in text-to-image (Native Server)',
                'available': True,
                'features': ['Fast Generation', 'Built-in Text-to-Image', 'Native Server', 'Efficient Processing']
            })
        else:
            pipelines.append({
                'id': 'hunyuan3d',
                'name': 'Hunyuan3D-2 Native',
                'description': 'Fast 3D generation with built-in text-to-image (Native Server)',
                'available': False,
                'features': ['Fast Generation', 'Built-in Text-to-Image', 'Native Server', 'Efficient Processing']
            })

        # Set default pipeline based on availability
        default_pipeline = 'trellis'  # Fallback default

        # Prefer Hunyuan3D-2 if available for text-to-3D
        for pipeline in pipelines:
            if pipeline['id'] == 'hunyuan3d' and pipeline['available']:
                default_pipeline = 'hunyuan3d'
                break

        return jsonify({
            'pipelines': pipelines,
            'default': default_pipeline
        })

    except Exception as e:
        print(f"Error getting available pipelines: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/config/huggingface-token', methods=['GET'])
def get_huggingface_token():
    """
    Get the current Hugging Face token status (not the actual token for security).

    Returns:
        JSON with token availability status.
    """
    try:
        config = get_config_manager()
        has_token = config.is_flux_available()
        preferred_model = config.get_preferred_model()

        return jsonify({
            'has_token': has_token,
            'preferred_model': preferred_model,
            'flux_available': has_token
        })
    except Exception as e:
        print(f"Error getting HF token status: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/config/huggingface-token', methods=['POST'])
def set_huggingface_token():
    """
    Set the Hugging Face token for accessing FLUX models.

    Expected JSON payload:
    {
        "token": "hf_xxxxxxxxxxxx",
        "preferred_model": "flux"  // optional: "flux", "sdxl_turbo", "sdxl", or "stable_diffusion"
    }

    Returns:
        JSON with success status.
    """
    try:
        data = request.get_json()
        token = data.get('token', '').strip()
        preferred_model = data.get('preferred_model', 'flux')

        config = get_config_manager()

        # If token is empty, clear it
        if not token:
            if config.set_huggingface_token(''):
                config.set_preferred_model('sdxl_turbo')  # Fallback to SDXL Turbo
                return jsonify({
                    'success': True,
                    'message': 'Token cleared successfully'
                })
            else:
                return jsonify({'error': 'Failed to clear token'}), 500

        # Validate token format
        if not token.startswith('hf_'):
            return jsonify({
                'error': 'Invalid token format. Hugging Face tokens should start with "hf_"'
            }), 400

        # Test the token by trying to authenticate with Hugging Face
        try:
            from huggingface_hub import login, whoami

            # Test authentication
            login(token=token)
            user_info = whoami(token=token)
            print(f"Token validated for user: {user_info.get('name', 'Unknown')}")

        except Exception as e:
            error_msg = str(e).lower()
            if 'invalid' in error_msg or 'unauthorized' in error_msg or '401' in error_msg:
                return jsonify({
                    'error': 'Invalid Hugging Face token. Please check your token and try again.'
                }), 400
            elif 'network' in error_msg or 'connection' in error_msg:
                return jsonify({
                    'error': 'Network error while validating token. Please check your internet connection.'
                }), 400
            else:
                return jsonify({
                    'error': f'Token validation failed: {str(e)}'
                }), 400

        # Save the validated token
        if config.set_huggingface_token(token):
            # Set preferred model if provided
            if preferred_model in ['flux', 'sdxl_turbo', 'sdxl', 'stable_diffusion']:
                config.set_preferred_model(preferred_model)

            # Test the token by trying to reinitialize the text-to-image pipeline
            global text_to_image_pipeline
            try:
                # Reset the pipeline to force reinitialization with new token
                text_to_image_pipeline = None
                text_to_image_pipeline = get_text_to_image_pipeline()

                model_type = text_to_image_pipeline.model_type if text_to_image_pipeline and text_to_image_pipeline.is_available() else None

                return jsonify({
                    'success': True,
                    'message': 'Hugging Face token validated and saved successfully! FLUX Schnell is now available.',
                    'flux_available': config.is_flux_available(),
                    'current_model': model_type
                })

            except Exception as e:
                print(f"Error reinitializing pipeline with new token: {e}")
                return jsonify({
                    'success': True,
                    'message': 'Token validated and saved, but pipeline reinitialization failed. FLUX will be available on next generation.',
                    'warning': str(e)
                })
        else:
            return jsonify({'error': 'Failed to save token'}), 500

    except Exception as e:
        print(f"Error setting HF token: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/config/settings', methods=['GET'])
def get_settings():
    """
    Get all configuration settings.

    Returns:
        JSON with all settings (excluding sensitive data like tokens).
    """
    try:
        config = get_config_manager()
        settings = config.get_all_settings()

        # Remove sensitive data
        safe_settings = settings.copy()
        if 'huggingface_token' in safe_settings:
            # Only show if token exists, not the actual token
            safe_settings['huggingface_token'] = bool(safe_settings['huggingface_token'])

        return jsonify(safe_settings)
    except Exception as e:
        print(f"Error getting settings: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/config/model-preference', methods=['POST'])
def update_model_preference():
    """
    Update the preferred text-to-image model.

    Expected JSON payload:
    {
        "preferred_model": "sdxl_turbo"  // "flux", "sdxl_turbo", "sdxl", or "stable_diffusion"
    }

    Returns:
        JSON with success status.
    """
    try:
        data = request.get_json()
        preferred_model = data.get('preferred_model')

        if not preferred_model or preferred_model not in ['flux', 'sdxl_turbo', 'sdxl', 'stable_diffusion']:
            return jsonify({'error': 'Invalid model preference'}), 400

        config = get_config_manager()

        if config.set_preferred_model(preferred_model):
            # Reinitialize the text-to-image pipeline with the new model preference
            global text_to_image_pipeline
            try:
                # Reset the pipeline to force reinitialization with new model preference
                text_to_image_pipeline = None
                text_to_image_pipeline = get_text_to_image_pipeline()

                return jsonify({
                    'success': True,
                    'message': f'Model preference updated to {preferred_model}',
                    'current_model': preferred_model
                })
            except Exception as e:
                print(f"Error reinitializing pipeline: {e}")
                return jsonify({
                    'success': True,
                    'message': f'Model preference updated to {preferred_model} (pipeline will reinitialize on next use)',
                    'current_model': preferred_model
                })
        else:
            return jsonify({'error': 'Failed to save model preference'}), 500

    except Exception as e:
        print(f"Error updating model preference: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/config/settings', methods=['POST'])
def update_settings():
    """
    Update configuration settings.

    Expected JSON payload:
    {
        "text_to_image_model": "sdxl_turbo",  // "flux", "sdxl_turbo", "sdxl", or "stable_diffusion"
        "auto_use_flux": true,
        "image_quality": "high",
        "default_image_size": 512
    }

    Returns:
        JSON with success status.
    """
    try:
        data = request.get_json()
        config = get_config_manager()

        # Filter out sensitive settings that should use dedicated endpoints
        safe_settings = {k: v for k, v in data.items() if k != 'huggingface_token'}

        if config.update_settings(safe_settings):
            return jsonify({
                'success': True,
                'message': 'Settings updated successfully'
            })
        else:
            return jsonify({'error': 'Failed to save settings'}), 500

    except Exception as e:
        print(f"Error updating settings: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/status', methods=['GET'])
def get_status():
    """
    Check if the server is running and the model is loaded.

    Returns:
        JSON with the server status.
    """
    try:
        config = get_config_manager()
        return jsonify({
            'status': 'ok',
            'trellis_available': trellis_pipeline is not None and trellis_pipeline.is_available() if trellis_pipeline else False,
            'text_to_image_available': text_to_image_pipeline is not None and text_to_image_pipeline.is_available() if text_to_image_pipeline else False,
            'text_to_image_model': text_to_image_pipeline.model_type if text_to_image_pipeline and text_to_image_pipeline.is_available() else None,
            'flux_available': config.is_flux_available(),
            'preferred_model': config.get_preferred_model(),
            'rembg_available': rembg_available
        })
    except Exception as e:
        print(f"Error getting status: {e}")
        return jsonify({
            'status': 'error',
            'error': str(e),
            'trellis_available': False,
            'text_to_image_available': False,
            'flux_available': False,
            'rembg_available': rembg_available
        })

@app.route('/api/health')
def health_check():
    """
    Enhanced health check endpoint for the Application Health System.

    Returns:
        JSON with detailed server status.
    """
    try:
        config = get_config_manager()
        return jsonify({
            'status': 'ok',
            'trellis_available': trellis_pipeline is not None and trellis_pipeline.is_available() if trellis_pipeline else False,
            'text_to_image_available': text_to_image_pipeline is not None and text_to_image_pipeline.is_available() if text_to_image_pipeline else False,
            'text_to_image_model': text_to_image_pipeline.model_type if text_to_image_pipeline and text_to_image_pipeline.is_available() else None,
            'flux_available': config.is_flux_available(),
            'preferred_model': config.get_preferred_model(),
            'rembg_available': rembg_available,
            'timestamp': time.time(),
            'uptime': time.time() - app_start_time if 'app_start_time' in globals() else 0,
            'version': '2.0.0'
        })
    except Exception as e:
        logger.error(f"Health check failed: {e}", component="HEALTH")
        return jsonify({'status': 'error', 'error': str(e)}), 500

@app.route('/api/background-services/status')
def background_services_status():
    """
    Get the status of all background services.

    Returns:
        JSON with background services status.
    """
    try:
        from background_services import background_service_manager

        if background_service_manager:
            services_status = {}

            # Get status for each registered service
            for service_name in background_service_manager.services:
                service_info = background_service_manager.get_service_status(service_name)
                services_status[service_name] = service_info

            return jsonify({
                'status': 'ok',
                'services': services_status,
                'timestamp': time.time()
            })
        else:
            return jsonify({
                'status': 'warning',
                'message': 'Background service manager not available',
                'services': {},
                'timestamp': time.time()
            })

    except Exception as e:
        logger.error(f"Background services status check failed: {e}", component="HEALTH")
        return jsonify({
            'status': 'error',
            'error': str(e),
            'services': {},
            'timestamp': time.time()
        }), 500

@app.route('/api/system/resources')
def system_resources():
    """
    Get system resource usage information.

    Returns:
        JSON with system resource information.
    """
    try:
        import psutil
        import platform

        # Get CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)

        # Get memory usage
        memory = psutil.virtual_memory()
        memory_percent = memory.percent

        # Get disk usage
        disk = psutil.disk_usage('/')
        disk_percent = (disk.used / disk.total) * 100

        # Get system info
        system_info = {
            'platform': platform.platform(),
            'cpu_count': psutil.cpu_count(),
            'memory_total': f"{memory.total / (1024**3):.1f} GB",
            'disk_total': f"{disk.total / (1024**3):.1f} GB"
        }

        return jsonify({
            'status': 'ok',
            'cpu_usage': cpu_percent,
            'memory_usage': memory_percent,
            'disk_usage': disk_percent,
            'system_info': system_info,
            'timestamp': time.time()
        })

    except ImportError:
        # psutil not available
        return jsonify({
            'status': 'warning',
            'message': 'System monitoring not available (psutil not installed)',
            'cpu_usage': 0,
            'memory_usage': 0,
            'disk_usage': 0,
            'system_info': {},
            'timestamp': time.time()
        })
    except Exception as e:
        logger.error(f"System resources check failed: {e}", component="HEALTH")
        return jsonify({
            'status': 'error',
            'error': str(e),
            'cpu_usage': 0,
            'memory_usage': 0,
            'disk_usage': 0,
            'system_info': {},
            'timestamp': time.time()
        }), 500

@app.route('/api/projects', methods=['GET'])
def get_projects():
    """
    Get all projects.

    Returns:
        JSON with list of projects.
    """
    try:
        projects = project_manager.get_all_projects()

        # Add thumbnail URLs for projects that have them
        for project in projects:
            if 'files' in project and 'thumbnail' in project['files']:
                project['thumbnail_url'] = f"/api/projects/{project['id']}/thumbnail"

        return jsonify({'projects': projects})
    except Exception as e:
        print(f"Error getting projects: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/projects/<project_id>', methods=['GET'])
def get_project(project_id):
    """
    Get a specific project by ID.

    Args:
        project_id: Project ID

    Returns:
        JSON with project details.
    """
    try:
        project = project_manager.get_project_metadata(project_id)
        if not project:
            return jsonify({'error': 'Project not found'}), 404

        # Add file URLs
        if 'files' in project:
            if 'model' in project['files']:
                project['model_url'] = f"/api/projects/{project_id}/model"
            if 'video' in project['files']:
                project['video_url'] = f"/api/projects/{project_id}/video"
            if 'thumbnail' in project['files']:
                project['thumbnail_url'] = f"/api/projects/{project_id}/thumbnail"

        return jsonify(project)
    except Exception as e:
        print(f"Error getting project: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/projects/<project_id>/model')
def get_project_model(project_id):
    """
    Serve a project's 3D model file.

    Args:
        project_id: Project ID

    Returns:
        The 3D model file.
    """
    try:
        print(f"DEBUG: Serving model for project {project_id}")
        model_path = project_manager.get_project_file_path(project_id, 'model')
        print(f"DEBUG: Model path: {model_path}")

        if not model_path:
            print(f"DEBUG: Model file not found for project {project_id}")
            return jsonify({'error': 'Model file not found'}), 404

        if not os.path.exists(model_path):
            print(f"DEBUG: Model file does not exist at path: {model_path}")
            return jsonify({'error': 'Model file does not exist'}), 404

        print(f"DEBUG: Serving model file: {model_path}")
        print(f"DEBUG: File size: {os.path.getsize(model_path)} bytes")

        # Determine MIME type based on file extension
        file_ext = os.path.splitext(model_path)[1].lower()
        if file_ext == '.glb':
            mimetype = 'model/gltf-binary'
        elif file_ext == '.gltf':
            mimetype = 'model/gltf+json'
        elif file_ext == '.ply':
            mimetype = 'application/octet-stream'
        else:
            mimetype = 'application/octet-stream'

        # Add CORS headers for 3D model files
        response = send_from_directory(
            os.path.dirname(model_path),
            os.path.basename(model_path),
            as_attachment=False,
            mimetype=mimetype
        )
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type'

        # Add cache-busting headers to ensure fresh model loads after Delighter processing
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
        response.headers['ETag'] = f'"{os.path.getmtime(model_path)}"'  # Use file modification time as ETag

        return response
    except Exception as e:
        print(f"Error serving project model: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/projects/<project_id>/video')
def get_project_video(project_id):
    """
    Serve a project's video file.

    Args:
        project_id: Project ID

    Returns:
        The video file.
    """
    try:
        video_path = project_manager.get_project_file_path(project_id, 'video')
        if not video_path:
            return jsonify({'error': 'Video file not found'}), 404

        return send_from_directory(os.path.dirname(video_path), os.path.basename(video_path))
    except Exception as e:
        print(f"Error serving project video: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/projects/<project_id>/thumbnail')
def get_project_thumbnail(project_id):
    """
    Serve a project's thumbnail image.

    Args:
        project_id: Project ID

    Returns:
        The thumbnail image.
    """
    try:
        thumbnail_path = project_manager.get_project_file_path(project_id, 'thumbnail')
        if not thumbnail_path:
            return jsonify({'error': 'Thumbnail not found'}), 404

        return send_from_directory(os.path.dirname(thumbnail_path), os.path.basename(thumbnail_path))
    except Exception as e:
        print(f"Error serving project thumbnail: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/projects/<project_id>', methods=['DELETE'])
def delete_project(project_id):
    """
    Delete a project and all its files.

    Args:
        project_id: Project ID

    Returns:
        JSON with success status.
    """
    try:
        if project_manager.delete_project(project_id):
            return jsonify({'success': True, 'message': 'Project deleted successfully'})
        else:
            return jsonify({'error': 'Failed to delete project'}), 500
    except Exception as e:
        print(f"Error deleting project: {e}")
        return jsonify({'error': str(e)}), 500



@app.route('/api/cleanup', methods=['POST'])
def cleanup():
    """
    Clean up temporary files for a specific image ID.

    Expected JSON payload:
    {
        "image_id": "uuid"
    }

    Returns:
        JSON with the cleanup status.
    """
    data = request.json
    if not data or 'image_id' not in data:
        return jsonify({'error': 'Invalid request data'}), 400

    image_id = data['image_id']
    upload_dir = os.path.join(UPLOAD_FOLDER, image_id)

    # Clean up upload directory
    if os.path.exists(upload_dir):
        shutil.rmtree(upload_dir)

    # Clean up output files
    for file in os.listdir(OUTPUT_FOLDER):
        if file.startswith(image_id):
            os.remove(os.path.join(OUTPUT_FOLDER, file))

    return jsonify({'success': True})

if __name__ == '__main__':
    # Initialize enhanced startup logging
    import time
    startup_start_time = time.time()

    try:
        from utils.startup_logger import get_startup_logger, log_startup_stage, log_startup_step, log_startup_error, log_startup_success
        startup_logger = get_startup_logger()

        log_startup_stage("APPLICATION STARTUP", "3D AI Studio with Hunyuan3D-2 Integration")

    except Exception as e:
        print(f"Warning: Could not initialize startup logger: {e}")
        # Fallback to basic logging
        def log_startup_stage(stage, details=""): print(f"STAGE: {stage}")
        def log_startup_step(step, status="STARTING"): print(f"STEP: {step} - {status}")
        def log_startup_error(msg, exc=None): print(f"ERROR: {msg}")
        def log_startup_success(msg): print(f"SUCCESS: {msg}")

    print("Starting 3D AI Studio server...")
    log_startup_step("Server Initialization", "STARTING")

    # Run startup dependency checks
    log_startup_stage("DEPENDENCY CHECKS", "Verifying critical dependencies")
    try:
        from startup_dependency_installer import run_startup_checks
        log_startup_step("Startup dependency checks", "RUNNING")
        run_startup_checks()
        log_startup_success("Startup dependency checks completed")
    except Exception as e:
        log_startup_error("Startup dependency checks failed", e)
        print(f"Warning: Startup dependency checks failed: {e}")

    # Background services disabled - using integrated Hunyuan3D-2 pipeline
    print("Using integrated Hunyuan3D-2 pipeline (no background services needed)")

    # # Initialize and start background services
    # try:
    #     from background_services import initialize_background_services, start_background_services
    #     print("Initializing background services...")
    #     initialize_background_services()

    #     def background_progress_callback(message):
    #         print(f"[BACKGROUND] {message}")

    #     print("Starting background services...")
    #     start_background_services(background_progress_callback)
    #     print("Background services initialization completed")
    # except Exception as e:
    #     print(f"Warning: Background services initialization failed: {e}")
    #     print("Application will continue without background services")

    # Initialize Hunyuan3D-2 pipeline directly in our application
    log_startup_stage("HUNYUAN3D-2 PIPELINE LOADING", "Loading integrated pipeline with texture generation")

    pipeline_start_time = time.time()
    print("=" * 60)
    print("LOADING HUNYUAN3D-2 PIPELINE")
    print("=" * 60)
    print("⏰ IMPORTANT: Full pipeline loading takes 3-5 minutes")
    print("⏰ This includes texture generation models (essential for quality)")
    print("⏰ Please wait for complete initialization...")
    print("=" * 60)

    try:
        def startup_progress_callback(message):
            print(f"[HUNYUAN3D] {message}")
            startup_logger.log_progress(f"Hunyuan3D: {message}")

        # Load the integrated Hunyuan3D-2 pipeline with CUDA support
        log_startup_step("Hunyuan3D-2 pipeline initialization", "STARTING")
        print("[HUNYUAN3D] Starting Hunyuan3D-2 pipeline initialization...")
        print("[HUNYUAN3D] Using CUDA acceleration with RTX 3060")

        log_startup_step("Importing Hunyuan3D integrated pipeline", "IMPORTING")
        try:
            from hunyuan3d_integrated_pipeline import get_hunyuan3d_integrated_pipeline
            log_startup_success("Hunyuan3D integrated pipeline module imported")

            integrated_pipeline = get_hunyuan3d_integrated_pipeline()
            log_startup_success("Hunyuan3D integrated pipeline instance created")
        except Exception as e:
            log_startup_error(f"Failed to import Hunyuan3D integrated pipeline", e)
            print(f"[HUNYUAN3D] ERROR: Failed to import integrated pipeline: {e}")
            import traceback
            print(f"[HUNYUAN3D] Traceback: {traceback.format_exc()}")
            raise  # Re-raise to see the full error

        # Load all pipeline components with progress tracking
        log_startup_step("Loading pipeline components", "LOADING")
        component_start_time = time.time()
        success = integrated_pipeline.load_pipeline_components(startup_progress_callback)
        component_load_time = time.time() - component_start_time

        if success:
            log_startup_success(f"Hunyuan3D-2 pipeline loaded in {component_load_time:.1f}s")
            print("[HUNYUAN3D] SUCCESS: Hunyuan3D-2 pipeline loaded successfully!")
            print(f"[HUNYUAN3D] SUCCESS: Texture generation: {'Available' if integrated_pipeline.has_texturegen else 'Not available'}")
            print(f"[HUNYUAN3D] SUCCESS: Text-to-image: {'Available' if integrated_pipeline.has_t2i else 'Not available'}")
            print("[HUNYUAN3D] SUCCESS: CUDA acceleration enabled")

            # Log pipeline capabilities
            startup_logger.log_pipeline_loading("Hunyuan3D-2", "COMPLETE", component_load_time)
            startup_logger.log_dependency_check("Texture Generation", integrated_pipeline.has_texturegen)
            startup_logger.log_dependency_check("Text-to-Image", integrated_pipeline.has_t2i)

            # Set as the global pipeline
            import sys
            current_module = sys.modules[__name__]
            current_module.hunyuan3d_pipeline = integrated_pipeline
            log_startup_success("Hunyuan3D-2 pipeline set as global")

        else:
            log_startup_error("Failed to load integrated pipeline")
            print("[HUNYUAN3D] ERROR: Failed to load integrated pipeline")

    except Exception as e:
        pipeline_error_time = time.time() - pipeline_start_time
        log_startup_error(f"Error during pipeline initialization after {pipeline_error_time:.1f}s", e)
        print(f"[HUNYUAN3D] ERROR: Error during pipeline initialization: {e}")
        import traceback
        print(f"[HUNYUAN3D] Traceback: {traceback.format_exc()}")

    pipeline_total_time = time.time() - pipeline_start_time
    log_startup_stage("HUNYUAN3D-2 COMPLETED", f"Total time: {pipeline_total_time:.1f}s")
    print("=" * 60)
    print("HUNYUAN3D-2 INITIALIZATION COMPLETED")
    print("=" * 60)

    # Register cleanup handlers
    def cleanup_background_services():
        """Clean up background services on application shutdown."""
        try:
            from background_services import stop_background_services
            print("Stopping background services...")
            stop_background_services()
            print("Background services stopped")
        except Exception as e:
            print(f"Error stopping background services: {e}")

    # Register cleanup for normal exit
    atexit.register(cleanup_background_services)

    # Register cleanup for signal termination
    def signal_handler(signum, frame):
        print(f"Received signal {signum}, shutting down...")
        cleanup_background_services()
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Log startup completion
    total_startup_time = time.time() - startup_start_time
    log_startup_stage("FLASK SERVER STARTING", f"Total startup time: {total_startup_time:.1f}s")
    startup_logger.log_startup_complete(total_startup_time, True)

    log_startup_success("3D AI Studio ready to serve requests")
    print(f"🚀 3D AI Studio startup completed in {total_startup_time:.1f}s")
    print(f"📋 Detailed logs saved to: logs/startup_detailed_*.log")

    app.run(host='0.0.0.0', port=5000, debug=False)
