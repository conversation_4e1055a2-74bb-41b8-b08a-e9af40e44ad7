2025-06-06 16:46:55,053 |     INFO | startup_detailed | log_system_info:68 | ================================================================================
2025-06-06 16:46:55,054 |     INFO | startup_detailed | log_system_info:69 | 3D AI STUDIO STARTUP - DETAILED LOGGING
2025-06-06 16:46:55,054 |     INFO | startup_detailed | log_system_info:70 | ================================================================================
2025-06-06 16:46:55,067 |     INFO | startup_detailed | log_system_info:94 | SYSTEM INFORMATION:
2025-06-06 16:46:55,067 |     INFO | startup_detailed | log_system_info:96 |   timestamp: 2025-06-06T16:46:55.054711
2025-06-06 16:46:55,067 |     INFO | startup_detailed | log_system_info:96 |   platform: Windows-11-10.0.26100-SP0
2025-06-06 16:46:55,068 |     INFO | startup_detailed | log_system_info:96 |   python_version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
2025-06-06 16:46:55,068 |     INFO | startup_detailed | log_system_info:96 |   python_executable: E:\3D AI Studio\.venv\Scripts\python.exe
2025-06-06 16:46:55,069 |     INFO | startup_detailed | log_system_info:96 |   working_directory: E:\3D AI Studio
2025-06-06 16:46:55,069 |     INFO | startup_detailed | log_system_info:96 |   cpu_count: 88
2025-06-06 16:46:55,069 |     INFO | startup_detailed | log_system_info:96 |   memory_total: 79.9 GB
2025-06-06 16:46:55,069 |     INFO | startup_detailed | log_system_info:96 |   disk_free: 529.7 GB
2025-06-06 16:46:55,069 |     INFO | startup_detailed | log_system_info:96 |   torch_version: 2.6.0+cu124
2025-06-06 16:46:55,069 |     INFO | startup_detailed | log_system_info:96 |   cuda_available: True
2025-06-06 16:46:55,069 |     INFO | startup_detailed | log_system_info:96 |   cuda_device: NVIDIA GeForce RTX 3060
2025-06-06 16:46:55,069 |     INFO | startup_detailed | log_system_info:96 |   cuda_memory: 12.0 GB
2025-06-06 16:46:55,070 |     INFO | startup_detailed | log_stage:104 | ============================================================
2025-06-06 16:46:55,070 |     INFO | startup_detailed | log_stage:105 | STAGE: APPLICATION STARTUP
2025-06-06 16:46:55,070 |     INFO | startup_detailed | log_stage:107 | DETAILS: 3D AI Studio with Hunyuan3D-2 Integration
2025-06-06 16:46:55,070 |     INFO | startup_detailed | log_stage:108 | ============================================================
2025-06-06 16:46:55,070 |     INFO | startup_detailed | log_step:112 | STEP: Server Initialization - STARTING
2025-06-06 16:46:55,070 |     INFO | startup_detailed | log_stage:104 | ============================================================
2025-06-06 16:46:55,070 |     INFO | startup_detailed | log_stage:105 | STAGE: DEPENDENCY CHECKS
2025-06-06 16:46:55,071 |     INFO | startup_detailed | log_stage:107 | DETAILS: Verifying critical dependencies
2025-06-06 16:46:55,071 |     INFO | startup_detailed | log_stage:108 | ============================================================
2025-06-06 16:46:55,072 |     INFO | startup_detailed | log_step:112 | STEP: Startup dependency checks - RUNNING
2025-06-06 16:47:08,143 |     INFO | startup_detailed | log_success:123 | SUCCESS: Startup dependency checks completed
2025-06-06 16:47:08,143 |     INFO | startup_detailed | log_stage:104 | ============================================================
2025-06-06 16:47:08,143 |     INFO | startup_detailed | log_stage:105 | STAGE: HUNYUAN3D-2 PIPELINE LOADING
2025-06-06 16:47:08,143 |     INFO | startup_detailed | log_stage:107 | DETAILS: Loading integrated pipeline with texture generation
2025-06-06 16:47:08,144 |     INFO | startup_detailed | log_stage:108 | ============================================================
2025-06-06 16:47:08,144 |     INFO | startup_detailed | log_step:112 | STEP: Hunyuan3D-2 pipeline initialization - STARTING
2025-06-06 16:47:08,144 |     INFO | startup_detailed | log_step:112 | STEP: Importing Hunyuan3D integrated pipeline - IMPORTING
2025-06-06 16:47:08,171 |     INFO | startup_detailed | log_success:123 | SUCCESS: Hunyuan3D integrated pipeline module imported
2025-06-06 16:47:08,172 |     INFO | startup_detailed | log_success:123 | SUCCESS: Hunyuan3D integrated pipeline instance created
2025-06-06 16:47:08,172 |     INFO | startup_detailed | log_step:112 | STEP: Loading pipeline components - LOADING
2025-06-06 16:47:08,172 |     INFO | startup_detailed | log_pipeline_loading:157 | PIPELINE: Hunyuan3D-2 - STARTING
2025-06-06 16:47:08,172 |     INFO | startup_detailed | log_progress:119 | PROGRESS: Hunyuan3D: Stage 1/6: Setting up Hunyuan3D-2 environment...
2025-06-06 16:47:08,173 |     INFO | startup_detailed | log_progress:119 | PROGRESS: Hunyuan3D: Stage 2/6: Loading background removal model...
2025-06-06 16:47:08,173 |     INFO | startup_detailed | log_step:112 | STEP: Background removal model - LOADING
2025-06-06 16:47:12,850 |     INFO | startup_detailed | log_model_loading:163 | MODEL: Background Removal - LOADED (4.7s)
2025-06-06 16:47:12,850 |    DEBUG | startup_detailed | log_model_loading:164 | Model path: hy3dgen.rembg
2025-06-06 16:47:12,850 |     INFO | startup_detailed | log_progress:119 | PROGRESS: Hunyuan3D: Stage 3/6: Loading shape generation pipeline...
2025-06-06 16:47:12,850 |     INFO | startup_detailed | log_step:112 | STEP: Shape generation imports - IMPORTING
2025-06-06 16:47:24,183 |    DEBUG | startup_detailed | log_import_attempt:142 | IMPORT SUCCESS: hy3dgen.shapegen
2025-06-06 16:47:24,183 |     INFO | startup_detailed | log_step:112 | STEP: Shape generation imports - COMPLETED (11.3s)
2025-06-06 16:47:24,184 |     INFO | startup_detailed | log_step:112 | STEP: Main shape generation pipeline - LOADING
2025-06-06 16:47:24,184 |     INFO | startup_detailed | log_model_loading:163 | MODEL: Hunyuan3D DiT - FAILED (0.0s)
2025-06-06 16:47:24,184 |    DEBUG | startup_detailed | log_model_loading:164 | Model path: tencent/Hunyuan3D-2mini/hunyuan3d-dit-v2-mini-turbo
2025-06-06 16:47:35,274 |     INFO | startup_detailed | log_model_loading:163 | MODEL: Hunyuan3D DiT - LOADED (11.1s)
2025-06-06 16:47:35,274 |    DEBUG | startup_detailed | log_model_loading:164 | Model path: tencent/Hunyuan3D-2mini/hunyuan3d-dit-v2-mini-turbo
2025-06-06 16:47:35,274 |     INFO | startup_detailed | log_step:112 | STEP: FlashVDM optimization - ENABLING
2025-06-06 16:47:37,752 |     INFO | startup_detailed | log_success:123 | SUCCESS: FlashVDM enabled with dmc algorithm
2025-06-06 16:47:37,753 |     INFO | startup_detailed | log_pipeline_loading:157 | PIPELINE: Shape Generation - LOADED (24.9s)
2025-06-06 16:47:37,753 |     INFO | startup_detailed | log_progress:119 | PROGRESS: Hunyuan3D: Stage 4/6: Loading mesh processing workers...
2025-06-06 16:47:37,753 |     INFO | startup_detailed | log_progress:119 | PROGRESS: Hunyuan3D: Stage 5/6: Loading texture generation pipeline...
2025-06-06 16:47:37,753 |     INFO | startup_detailed | log_step:112 | STEP: Texture generation pipeline - STARTING
2025-06-06 16:47:37,753 |     INFO | startup_detailed | log_step:112 | STEP: Texture generation imports - IMPORTING
2025-06-06 16:47:37,890 |    DEBUG | startup_detailed | log_import_attempt:142 | IMPORT SUCCESS: hy3dgen.texgen
2025-06-06 16:47:37,891 |     INFO | startup_detailed | log_step:112 | STEP: Texture model cache check - Checking Resources\Hunyuan3D2_WinPortable\HuggingFaceHub\models--tencent--Hunyuan3D-2\snapshots
2025-06-06 16:47:37,891 |     INFO | startup_detailed | log_step:112 | STEP: Texture model paths - Delight: True, Paint: True
2025-06-06 16:47:37,892 |     INFO | startup_detailed | log_progress:119 | PROGRESS: Hunyuan3D: Loading texture delight model...
2025-06-06 16:47:37,892 |     INFO | startup_detailed | log_step:112 | STEP: Texture pipeline loading - LOADING MODELS
2025-06-06 16:47:37,892 |     INFO | startup_detailed | log_step:112 | STEP: Using local texture model path - Resources\Hunyuan3D2_WinPortable\HuggingFaceHub\models--tencent--Hunyuan3D-2\snapshots\34e28261f71c32975727be8db0eace439a280f82
2025-06-06 16:47:41,777 |    ERROR | startup_detailed | log_error:134 | ERROR: Texture generation pipeline failed after 4.0s
2025-06-06 16:47:41,777 |    ERROR | startup_detailed | log_error:136 | Exception: 'SafeTensorFile' object has no attribute 'get_slice'
2025-06-06 16:47:41,788 |    ERROR | startup_detailed | log_error:137 | Traceback (most recent call last):
  File "E:\3D AI Studio\hunyuan3d_integrated_pipeline.py", line 252, in load_pipeline_components
    self.texgen_worker = Hunyuan3DPaintPipeline.from_pretrained(local_texgen_path)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\texgen\pipelines.py", line 86, in from_pretrained
    return cls(Hunyuan3DTexGenConfig(delight_model_path, multiview_model_path))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\texgen\pipelines.py", line 95, in __init__
    self.load_models()
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\texgen\pipelines.py", line 101, in load_models
    self.models['delight_model'] = Light_Shadow_Remover(self.config)
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\texgen\utils\dehighlight_utils.py", line 28, in __init__
    pipeline = StableDiffusionInstructPix2PixPipeline.from_pretrained(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\pipelines\pipeline_utils.py", line 961, in from_pretrained
    loaded_sub_model = load_sub_model(
                       ^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\pipelines\pipeline_loading_utils.py", line 777, in load_sub_model
    loaded_sub_model = load_method(os.path.join(cached_folder, name), **loading_kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\transformers\modeling_utils.py", line 309, in _wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\transformers\modeling_utils.py", line 4574, in from_pretrained
    ) = cls._load_pretrained_model(
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\transformers\modeling_utils.py", line 4833, in _load_pretrained_model
    load_state_dict(checkpoint_files[0], map_location="meta", weights_only=weights_only).keys()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\transformers\modeling_utils.py", line 541, in load_state_dict
    _slice = f.get_slice(k)
             ^^^^^^^^^^^
AttributeError: 'SafeTensorFile' object has no attribute 'get_slice'

2025-06-06 16:47:41,800 |     INFO | startup_detailed | log_progress:119 | PROGRESS: Hunyuan3D: Stage 6/6: Loading text-to-image pipeline...
2025-06-06 16:47:41,801 |     INFO | startup_detailed | log_step:112 | STEP: Text-to-image pipeline - LOADING
2025-06-06 16:47:45,359 |  WARNING | startup_detailed | log_warning:127 | WARNING: Text-to-image pipeline failed: 'SafeTensorFile' object has no attribute 'get_slice'
2025-06-06 16:47:45,388 |     INFO | startup_detailed | log_progress:119 | PROGRESS: Hunyuan3D: Finalizing pipeline setup...
2025-06-06 16:47:45,389 |     INFO | startup_detailed | log_progress:119 | PROGRESS: Hunyuan3D: Hunyuan3D-2 pipeline ready!
2025-06-06 16:47:45,389 |     INFO | startup_detailed | log_success:123 | SUCCESS: Hunyuan3D-2 pipeline loaded in 37.2s
2025-06-06 16:47:45,389 |     INFO | startup_detailed | log_pipeline_loading:157 | PIPELINE: Hunyuan3D-2 - COMPLETE (37.2s)
2025-06-06 16:47:45,389 |     INFO | startup_detailed | log_dependency_check:152 | DEPENDENCY: Texture Generation - MISSING
2025-06-06 16:47:45,389 |     INFO | startup_detailed | log_dependency_check:152 | DEPENDENCY: Text-to-Image - MISSING
2025-06-06 16:47:45,389 |     INFO | startup_detailed | log_success:123 | SUCCESS: Hunyuan3D-2 pipeline set as global
2025-06-06 16:47:45,390 |     INFO | startup_detailed | log_step:112 | STEP: Initializing modular pipeline system - STARTING
2025-06-06 16:47:47,903 |     INFO | startup_detailed | log_success:123 | SUCCESS: Trellis pipeline available for modular system
2025-06-06 16:47:47,905 |     INFO | startup_detailed | log_success:123 | SUCCESS: Modular pipeline system initialized
2025-06-06 16:47:47,905 |     INFO | startup_detailed | log_stage:104 | ============================================================
2025-06-06 16:47:47,905 |     INFO | startup_detailed | log_stage:105 | STAGE: HUNYUAN3D-2 COMPLETED
2025-06-06 16:47:47,905 |     INFO | startup_detailed | log_stage:107 | DETAILS: Total time: 39.8s
2025-06-06 16:47:47,905 |     INFO | startup_detailed | log_stage:108 | ============================================================
2025-06-06 16:47:47,905 |     INFO | startup_detailed | log_stage:104 | ============================================================
2025-06-06 16:47:47,905 |     INFO | startup_detailed | log_stage:105 | STAGE: FLASK SERVER STARTING
2025-06-06 16:47:47,905 |     INFO | startup_detailed | log_stage:107 | DETAILS: Total startup time: 52.9s
2025-06-06 16:47:47,905 |     INFO | startup_detailed | log_stage:108 | ============================================================
2025-06-06 16:47:47,905 |     INFO | startup_detailed | log_startup_complete:181 | ================================================================================
2025-06-06 16:47:47,905 |     INFO | startup_detailed | log_startup_complete:183 | STARTUP COMPLETE: SUCCESSFUL (Total time: 52.9s)
2025-06-06 16:47:47,906 |     INFO | startup_detailed | log_startup_complete:184 | ================================================================================
2025-06-06 16:47:47,906 |     INFO | startup_detailed | log_startup_complete:187 | Detailed logs saved to: logs\startup_detailed_20250606_164655.log
2025-06-06 16:47:47,906 |     INFO | startup_detailed | log_success:123 | SUCCESS: 3D AI Studio ready to serve requests
