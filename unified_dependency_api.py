#!/usr/bin/env python3
"""
Unified Dependency Manager API
Clean, simple API using the unified installer
"""

import time
import threading
import json
from pathlib import Path
from flask import jsonify, Response
from unified_installer import installer

# Global progress tracking
install_progress = {}
install_threads = {}

# Cache for status checks to improve performance
_status_cache = {}
_cache_timestamp = 0
CACHE_DURATION = 30  # Cache for 30 seconds

def register_unified_api_routes(app):
    """Register unified dependency manager API routes."""
    
    @app.route('/api/unified/categories', methods=['GET'])
    def get_unified_categories():
        """Get all dependencies and models organized by category."""
        try:
            categories = {
                'Core Dependencies': [
                    {
                        'id': 'pytorch_cuda',
                        'name': 'PyTorch with CUDA',
                        'description': 'Deep learning framework with GPU acceleration',
                        'status': _check_status('pytorch_cuda'),
                        'essential': True,
                        'size_mb': 2500,
                        'type': 'dependency'
                    },
                    {
                        'id': 'pillow',
                        'name': 'Pillow (PIL)',
                        'description': 'Python Imaging Library',
                        'status': _check_status('pillow'),
                        'essential': True,
                        'size_mb': 25,
                        'type': 'dependency'
                    },
                    {
                        'id': 'numpy',
                        'name': 'NumPy',
                        'description': 'Numerical computing library',
                        'status': _check_status('numpy'),
                        'essential': True,
                        'size_mb': 50,
                        'type': 'dependency'
                    },
                    {
                        'id': 'flask',
                        'name': 'Flask',
                        'description': 'Web framework for the API server',
                        'status': _check_status('flask'),
                        'essential': True,
                        'size_mb': 15,
                        'type': 'dependency'
                    },
                    {
                        'id': 'requests',
                        'name': 'Requests',
                        'description': 'HTTP library for API calls',
                        'status': _check_status('requests'),
                        'essential': True,
                        'size_mb': 10,
                        'type': 'dependency'
                    },
                    {
                        'id': 'gitpython',
                        'name': 'GitPython',
                        'description': 'Git repository management',
                        'status': _check_status('gitpython'),
                        'essential': True,
                        'size_mb': 20,
                        'type': 'dependency'
                    },
                    {
                        'id': 'huggingface_hub',
                        'name': 'Hugging Face Hub',
                        'description': 'Model repository access',
                        'status': _check_status('huggingface_hub'),
                        'essential': True,
                        'size_mb': 30,
                        'type': 'dependency'
                    },
                    {
                        'id': 'imageio',
                        'name': 'ImageIO',
                        'description': 'Image I/O operations',
                        'status': _check_status('imageio'),
                        'essential': True,
                        'size_mb': 25,
                        'type': 'dependency'
                    },
                    {
                        'id': 'easydict',
                        'name': 'EasyDict',
                        'description': 'Configuration management',
                        'status': _check_status('easydict'),
                        'essential': True,
                        'size_mb': 5,
                        'type': 'dependency'
                    }
                ],
                'Additional Dependencies': [
                    {
                        'id': 'missing_deps',
                        'name': 'Missing Dependencies',
                        'description': 'Install commonly missing packages',
                        'status': 'available',  # Always available to install
                        'essential': False,
                        'size_mb': 500,
                        'type': 'dependency'
                    },
                    {
                        'id': 'trellis_deps',
                        'name': 'Trellis Dependencies',
                        'description': 'Dependencies required for Trellis models',
                        'status': 'available',  # Always available to install
                        'essential': False,
                        'size_mb': 1000,
                        'type': 'dependency'
                    }
                ],
                'Utilities': [
                    {
                        'id': 'rembg',
                        'name': 'REMBG',
                        'description': 'Background removal tool for images',
                        'status': _check_status('rembg'),
                        'essential': False,
                        'size_mb': 50,
                        'type': 'dependency'
                    },
                    {
                        'id': 'git_base',
                        'name': 'GIT Base',
                        'description': 'Image captioning and understanding',
                        'status': _check_model_status('git_base'),
                        'essential': False,
                        'size_mb': 500,
                        'type': 'model'
                    }
                ],
                'Trellis': [
                    {
                        'id': 'trellis_image_large',
                        'name': 'TRELLIS Image Large',
                        'description': 'Large image-to-3D model',
                        'status': _check_model_status('trellis_image_large'),
                        'essential': False,
                        'size_mb': 8000,
                        'type': 'model'
                    },
                    {
                        'id': 'trellis_text_large',
                        'name': 'TRELLIS Text Large',
                        'description': 'Large text-to-3D model',
                        'status': _check_model_status('trellis_text_large'),
                        'essential': False,
                        'size_mb': 8000,
                        'type': 'model'
                    }
                ],
                'Hunyuan3D-2': [
                    {
                        'id': 'hunyuan3d_codebase',
                        'name': 'Hunyuan3D-2 Codebase',
                        'description': 'Hunyuan3D-2 inference codebase',
                        'status': _check_model_status('hunyuan3d_codebase'),
                        'essential': True,
                        'size_mb': 50,
                        'type': 'model'
                    },
                    {
                        'id': 'hunyuan3d_texturing_deps',
                        'name': 'Hunyuan3D-2 Texturing Dependencies',
                        'description': 'Essential dependencies for full Hunyuan3D-2 texturing pipeline (pymeshlab, xatlas, diso)',
                        'status': _check_status('hunyuan3d_texturing_deps'),
                        'essential': True,
                        'size_mb': 200,
                        'type': 'dependency'
                    },
                    {
                        'id': 'hunyuan3d_mesh_processing',
                        'name': 'Hunyuan3D-2 Mesh Processing Tools',
                        'description': 'Advanced mesh processing tools (FloaterRemover, DegenerateFaceRemover, FaceReducer)',
                        'status': _check_status('hunyuan3d_mesh_processing'),
                        'essential': True,
                        'size_mb': 100,
                        'type': 'dependency'
                    },
                    {
                        'id': 'hunyuan3d_texture_deps',
                        'name': 'Hunyuan3D-2 Texture Compilation',
                        'description': 'Compile custom rasterizer and differentiable renderer for high-quality texture generation',
                        'status': _check_status('hunyuan3d_texture_deps'),
                        'essential': False,
                        'size_mb': 50,
                        'type': 'dependency'
                    },
                    {
                        'id': 'hunyuan3d_paint_models',
                        'name': 'Hunyuan3D-2 Paint Models',
                        'description': 'Download Hunyuan3D Paint models for advanced texture generation (hunyuan3d-paint-v2-0)',
                        'status': _check_status('hunyuan3d_paint_models'),
                        'essential': False,
                        'size_mb': 2500,
                        'type': 'model'
                    },
                    {
                        'id': 'hunyuan3d_paint_turbo',
                        'name': 'Hunyuan3D-2 Turbo Paint Model',
                        'description': 'Download fastest Hunyuan3D Turbo Paint model for high-speed texture generation (hunyuan3d-paint-v2-0-turbo)',
                        'status': _check_status('hunyuan3d_paint_turbo'),
                        'essential': False,
                        'size_mb': 2500,
                        'type': 'model'
                    },
                    {
                        'id': 'hunyuan3d_winportable',
                        'name': 'Hunyuan3D-2 WinPortable Service',
                        'description': 'High-quality texture generation service using pre-compiled Hunyuan3D-2 environment',
                        'status': _check_status('hunyuan3d_winportable'),
                        'essential': False,
                        'size_mb': 0,  # Already installed
                        'type': 'service'
                    },
                    {
                        'id': 'hunyuan3d_2mini',
                        'name': 'Hunyuan3D-2 Mini Model',
                        'description': 'Fast 3D generation model (image-to-3D and text-to-3D)',
                        'status': _check_model_status('hunyuan3d_2mini'),
                        'essential': False,
                        'size_mb': 4000,
                        'type': 'model'
                    },
                    {
                        'id': 'hunyuan3d_2_full',
                        'name': 'Hunyuan3D-2 Full Model',
                        'description': 'High-quality 3D generation and texture model',
                        'status': _check_model_status('hunyuan3d_2_full'),
                        'essential': False,
                        'size_mb': 8000,
                        'type': 'model'
                    }
                ]
            }
            
            return jsonify({
                'success': True,
                'data': categories
            })
            
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/unified/summary', methods=['GET'])
    def get_unified_summary():
        """Get summary statistics."""
        try:
            # Get all items from categories
            categories_response = get_unified_categories()
            categories_data = categories_response.get_json()
            
            if not categories_data['success']:
                return categories_response
            
            total = 0
            available = 0
            essential = 0
            installing = len(install_progress)
            
            for category_items in categories_data['data'].values():
                for item in category_items:
                    total += 1
                    if item['status'] == 'available':
                        available += 1
                    if item['essential']:
                        essential += 1
            
            return jsonify({
                'success': True,
                'data': {
                    'total': total,
                    'available': available,
                    'essential': essential,
                    'missing': total - available,
                    'installing': installing
                }
            })
            
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/unified/<item_id>/install', methods=['POST'])
    def install_unified_item(item_id):
        """Install any item (dependency or model)."""
        try:
            if item_id in install_threads and install_threads[item_id].is_alive():
                return jsonify({
                    'success': False,
                    'error': f'{item_id} is already being installed'
                }), 400
            
            # Initialize progress tracking with immediate persistence
            initial_progress = {
                'progress': 0,
                'status': 'installing',
                'current_step': 'Initializing installation...',
                'timestamp': time.time(),
                'item_id': item_id
            }
            install_progress[item_id] = initial_progress
            print(f"[UNIFIED API] Initialized progress tracking for {item_id}: {initial_progress}", flush=True)
            
            def progress_callback(step, progress):
                # Determine status based on progress and step content
                if progress >= 100:
                    status = 'completed'
                elif progress == 0 and ('error' in step.lower() or 'failed' in step.lower()):
                    status = 'failed'
                else:
                    status = 'installing'

                # Update progress with enhanced data
                progress_data = {
                    'progress': max(0, min(100, progress)),  # Clamp between 0-100
                    'status': status,
                    'current_step': step,
                    'timestamp': time.time(),
                    'item_id': item_id
                }

                install_progress[item_id] = progress_data
                print(f"[UNIFIED API] Progress update for {item_id}: {progress}% - {step} (Status: {status})", flush=True)

                # Force flush to ensure immediate persistence
                import sys
                sys.stdout.flush()
            
            def install_worker():
                try:
                    success = installer.install(item_id, progress_callback)
                    
                    # Final status update
                    if success:
                        install_progress[item_id] = {
                            'progress': 100,
                            'status': 'completed',
                            'current_step': f'{item_id} installation completed successfully',
                            'timestamp': time.time()
                        }
                    else:
                        install_progress[item_id] = {
                            'progress': 0,
                            'status': 'failed',
                            'current_step': f'{item_id} installation failed',
                            'timestamp': time.time()
                        }
                        
                except Exception as e:
                    install_progress[item_id] = {
                        'progress': 0,
                        'status': 'failed',
                        'current_step': f'{item_id} installation error: {str(e)}',
                        'timestamp': time.time()
                    }
            
            # Start installation in background thread
            thread = threading.Thread(target=install_worker, daemon=True)
            thread.start()
            install_threads[item_id] = thread
            
            return jsonify({
                'success': True,
                'message': f'{item_id} installation started'
            })
            
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/unified/<item_id>/progress', methods=['GET'])
    def get_unified_progress(item_id):
        """Get installation progress for any item."""
        try:
            progress = install_progress.get(item_id, {
                'progress': 0,
                'status': 'unknown',
                'current_step': 'No installation in progress'
            })
            
            print(f"[UNIFIED API] Progress request for {item_id}, returning: {progress}", flush=True)
            
            return jsonify({
                'success': True,
                'data': progress
            })
            
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/unified/debug/progress', methods=['GET'])
    def debug_progress():
        """Debug endpoint to see all progress data."""
        try:
            return jsonify({
                'success': True,
                'data': {
                    'install_progress': dict(install_progress),
                    'install_threads': {k: v.is_alive() if v else None for k, v in install_threads.items()},
                    'active_installs': list(install_progress.keys())
                }
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/unified/progress/stream', methods=['GET'])
    def stream_progress():
        """Server-sent events stream for real-time progress updates."""
        def generate():
            last_sent = {}
            while True:
                try:
                    # Check for progress updates
                    current_progress = dict(install_progress)

                    for item_id, progress_data in current_progress.items():
                        # Only send if data has changed
                        if item_id not in last_sent or last_sent[item_id] != progress_data:
                            yield f"data: {json.dumps({'item_id': item_id, 'progress': progress_data})}\n\n"
                            last_sent[item_id] = progress_data.copy()

                    # Clean up completed items from last_sent
                    for item_id in list(last_sent.keys()):
                        if item_id not in current_progress:
                            del last_sent[item_id]

                    time.sleep(0.5)  # Update every 500ms

                except Exception as e:
                    yield f"data: {json.dumps({'error': str(e)})}\n\n"
                    break

        return Response(generate(), mimetype='text/plain')

    @app.route('/api/unified/cache/clear', methods=['POST'])
    def clear_unified_cache():
        """Clear the status cache for fresh checks."""
        global _status_cache, _cache_timestamp
        _status_cache.clear()
        _cache_timestamp = 0
        return jsonify({
            'success': True,
            'message': 'Status cache cleared'
        })

def _check_status(item_id: str) -> str:
    """Check if a dependency is available with caching."""
    global _status_cache, _cache_timestamp

    current_time = time.time()

    # Check cache first
    if (current_time - _cache_timestamp < CACHE_DURATION and
        item_id in _status_cache):
        return _status_cache[item_id]

    # Clear cache if expired
    if current_time - _cache_timestamp >= CACHE_DURATION:
        _status_cache.clear()
        _cache_timestamp = current_time

    try:
        status = _check_status_uncached(item_id)
        _status_cache[item_id] = status
        return status
    except Exception:
        return 'error'

def _check_status_uncached(item_id: str) -> str:
    """Internal function to check status without caching."""
    try:
        if item_id == 'pytorch_cuda':
            import torch  # noqa: F401
            return 'available' if torch.cuda.is_available() else 'missing'
        elif item_id == 'pillow':
            import PIL  # noqa: F401
            return 'available'
        elif item_id == 'numpy':
            import numpy  # noqa: F401
            return 'available'
        elif item_id == 'flask':
            import flask  # noqa: F401
            return 'available'
        elif item_id == 'requests':
            import requests  # noqa: F401
            return 'available'
        elif item_id == 'gitpython':
            import git  # noqa: F401
            return 'available'
        elif item_id == 'huggingface_hub':
            import huggingface_hub  # noqa: F401
            return 'available'
        elif item_id == 'imageio':
            import imageio  # noqa: F401
            return 'available'
        elif item_id == 'easydict':
            import easydict  # noqa: F401
            return 'available'
        elif item_id == 'rembg':
            import rembg  # noqa: F401
            return 'available'
        elif item_id == 'hunyuan3d_texturing_deps':
            # Check for essential texturing dependencies
            try:
                import pymeshlab
                import xatlas
                # Note: diso is optional, we use 'mc' algorithm as fallback
                return 'available'
            except ImportError:
                return 'missing'
        elif item_id == 'hunyuan3d_mesh_processing':
            # Check if Hunyuan3D mesh processing tools are available
            try:
                # Try to import from the Hunyuan3D codebase
                from hy3dgen.shapegen import FloaterRemover, DegenerateFaceRemover, FaceReducer
                return 'available'
            except ImportError:
                return 'missing'
        elif item_id == 'hunyuan3d_texture_deps':
            # Check if custom rasterizer and differentiable renderer are compiled
            # Consider it available if at least one component works or if basic texture support exists
            rasterizer_available = False
            renderer_available = False

            try:
                import custom_rasterizer_kernel
                rasterizer_available = True
            except ImportError:
                pass

            try:
                import mesh_processor
                renderer_available = True
            except ImportError:
                pass

            # Check if Hunyuan3D codebase is available for fallback texture methods
            hunyuan3d_available = False
            try:
                # Check for Hunyuan3D codebase
                app_root = Path(__file__).parent
                hunyuan3d_models_path = app_root / "models" / "hunyuan3d"
                hunyuan3d_codebase_path = hunyuan3d_models_path / "hunyuan3d_codebase"

                if hunyuan3d_codebase_path.exists() and any(hunyuan3d_codebase_path.iterdir()):
                    hunyuan3d_available = True
                else:
                    # Fallback to old path structure
                    hunyuan3d_path = app_root / "Resources" / "Hunyuan3D-2-main"
                    if hunyuan3d_path.exists():
                        hunyuan3d_available = True
            except Exception:
                pass

            # Return available if we have any texture capability
            if rasterizer_available or renderer_available or hunyuan3d_available:
                return 'available'
            else:
                return 'missing'
        elif item_id == 'hunyuan3d_paint_models':
            # Check if Hunyuan3D Paint models are available
            try:
                app_root = Path(__file__).parent

                # Check for paint models in the full model directory
                paint_model_paths = [
                    app_root / "models" / "hunyuan3d" / "hunyuan3d_2_full" / "hunyuan3d-paint-v2-0",
                    app_root / "models" / "hunyuan3d_2_full" / "hunyuan3d-paint-v2-0",
                    app_root / "models" / "hunyuan3d_paint_models" / "hunyuan3d-paint-v2-0"
                ]

                delight_model_paths = [
                    app_root / "models" / "hunyuan3d" / "hunyuan3d_2_full" / "hunyuan3d-delight-v2-0",
                    app_root / "models" / "hunyuan3d_2_full" / "hunyuan3d-delight-v2-0",
                    app_root / "models" / "hunyuan3d_paint_models" / "hunyuan3d-delight-v2-0"
                ]

                # Check for both paint and delight models
                paint_found = False
                delight_found = False

                for paint_path in paint_model_paths:
                    if paint_path.exists() and (paint_path / "model_index.json").exists():
                        paint_found = True
                        break

                for delight_path in delight_model_paths:
                    if delight_path.exists() and (delight_path / "model_index.json").exists():
                        delight_found = True
                        break

                if paint_found and delight_found:
                    return 'available'
                else:
                    return 'missing'
            except Exception:
                return 'missing'
        elif item_id == 'hunyuan3d_paint_turbo':
            # Check if Hunyuan3D Turbo Paint models are available
            try:
                app_root = Path(__file__).parent

                # Check for Turbo paint models in the full model directory
                turbo_paint_paths = [
                    app_root / "models" / "hunyuan3d" / "hunyuan3d_2_full" / "hunyuan3d-paint-v2-0-turbo",
                    app_root / "models" / "hunyuan3d_2_full" / "hunyuan3d-paint-v2-0-turbo",
                    app_root / "models" / "hunyuan3d_paint_models" / "hunyuan3d-paint-v2-0-turbo"
                ]

                for turbo_path in turbo_paint_paths:
                    if turbo_path.exists() and (turbo_path / "model_index.json").exists():
                        return 'available'

                # Check if available via HuggingFace cache
                try:
                    from hy3dgen.texgen import Hunyuan3DPaintPipeline
                    # Try to load Turbo model without downloading
                    Hunyuan3DPaintPipeline.from_pretrained(
                        'tencent/Hunyuan3D-2',
                        subfolder='hunyuan3d-paint-v2-0-turbo',
                        local_files_only=True
                    )
                    return 'available'
                except Exception:
                    return 'missing'
            except Exception:
                return 'missing'
        elif item_id == 'hunyuan3d_winportable':
            # Check if Hunyuan3D WinPortable service is available
            try:
                from hunyuan3d_winportable_service import get_winportable_service
                service = get_winportable_service()
                return 'available' if service.is_available else 'missing'
            except Exception:
                return 'missing'
        else:
            return 'missing'
    except ImportError:
        return 'missing'
    except Exception:
        return 'error'

def _check_model_status(item_id: str) -> str:
    """Check if a model is available."""
    from pathlib import Path
    
    app_root = Path(__file__).parent
    
    model_paths = {
        'git_base': app_root / 'models' / 'utilities' / 'microsoft--git-base',
        'trellis_image_large': app_root / 'models' / 'trellis' / 'trellis_image_large',
        'trellis_text_large': app_root / 'models' / 'trellis' / 'trellis_text_large',
        'hunyuan3d_codebase': app_root / 'models' / 'hunyuan3d' / 'hunyuan3d_codebase',
        'hunyuan3d_2mini': app_root / 'models' / 'hunyuan3d' / 'hunyuan3d_2mini',
        'hunyuan3d_2_full': app_root / 'models' / 'hunyuan3d' / 'hunyuan3d_2_full'
    }
    
    model_path = model_paths.get(item_id)
    if model_path and model_path.exists() and any(model_path.iterdir()):
        return 'available'
    else:
        return 'missing'
