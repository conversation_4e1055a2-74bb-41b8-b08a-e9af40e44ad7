========================================
         3D AI Studio Launcher
========================================

[1/5] Checking Python installation...
[OK] Python found
[2/4] Checking virtual environment
🚀 3D AI Studio - Comprehensive Dependency Installer
======================================================================
This will install ALL dependencies for Trellis and Hunyuan3D-2 systems

Checking critical dependencies...
  ✓ Flask available
  ✓ Flask-CORS available
  ✓ requests available
  ✓ NumPy available
  ✓ Pillow available
  ✓ PyTorch available
  ✓ Transformers available
  ✓ Trimesh available
  ✓ ImageIO available

✅ All critical dependencies are available!
✅ Critical dependencies are available.
Run with --force to reinstall all dependencies.
[OK] Virtual environment ready
[3/4] Quick environment check
  [INFO] All dependencies installed automatically at startup
  [INFO] Trellis and Hunyuan3D-2 systems integrated in single environment
Quick environment check...
  [INFO] Full validation available through Dependency Manager
  ✓ Main Application: Basic setup OK
  ✓ Trellis: Basic setup OK
  ✓ Hunyuan3D-2: Basic setup OK
  [OK] Basic environments ready
[4/4] Activating Python environment
Installing missing dependencies
Warning: Some dependencies may not have installed correctly
The application will attempt to start with available functionality
  [OK] Python environment activated
Checking Node.js for Electron app
[OK] Node.js found
Preparing desktop application
[OK] Desktop application ready
Starting 3D AI Studio Desktop App

========================================
    3D AI Studio Desktop Application
    Starting in desktop window

    STREAMLINED SETUP
    All dependencies installed automatically at startup!

    FEATURES:
    [OK] Trellis pipeline for high-quality 3D generation
    [OK] Hunyuan3D-2 native server with text-to-3D support
    [OK] Automatic background service management
    [OK] Comprehensive progress tracking
    [OK] Single virtual environment for all systems

    Ready to use - no manual setup required!
========================================


> 3d-ai-studio@1.0.0 electron
> electron .


Starting 3D AI Studio...
Starting new server...
Starting server...
App path: E:\3D AI Studio
Python path: E:\3D AI Studio\.venv\Scripts\python.exe
App script: E:\3D AI Studio\app.py
Server: 06:53:38 | INFO | [SYSTEM](NO_SESSI) | System startup | Data: {"platform": "Windows-11-10.0.26100-SP0", "python_version": "3.12.10", "cpu_count": 88, "memory_total": "79.9 GB", "disk_free": "539.2 GB", "torch_version": "2.7.1+cpu", "cuda_available": false}
06:53:38 | INFO | [STARTUP](NO_SESSI) | 3D AI Studio starting up

Server: Starting 3D AI Studio core application...
All AI models and dependencies managed through Dependency Manager
Core application ready - use Dependency Manager for AI features
Starting 3D AI Studio server...
Running startup dependency checks...
============================================================
STARTUP DEPENDENCY CHECKS
============================================================
[STARTUP] Checking critical dependencies...

Server: [STARTUP] imageio: Already available

Server: [STARTUP] Installing Pillow...

Server: [STARTUP] Pillow: Installed successfully

Server: [STARTUP] numpy: Already available

Server: [STARTUP] requests: Already available

Server: [STARTUP] flask: Already available

Server: [STARTUP] flask-cors: Already available
[STARTUP] Critical dependencies check complete: 6/6 available

Server: [STARTUP] PIL imports working correctly

Server: [STARTUP] ImageIO already available
[STARTUP] Checking Hunyuan3D-2 setup...

Server: 06:53:55 | INFO | [HUNYUAN3D_DIRECT](NO_SESSI) | Initial setup status - Compilation: True, Models: True
[STARTUP] Hunyuan3D-2 initial setup already completed
============================================================
STARTUP CHECKS COMPLETE: 4/4 successful
============================================================

Server: 06:53:55 | INFO | [BACKGROUND_SERVICES](NO_SESSI) | Background services using main app logging system
Initializing background services...
06:53:55 | INFO | [BACKGROUND_SERVICES](NO_SESSI) | Initializing background services...
06:53:55 | INFO | [BACKGROUND_SERVICES](NO_SESSI) | Registered service: hunyuan3d_native

Server: 06:53:55 | INFO | [BACKGROUND_SERVICES](NO_SESSI) | Registered Hunyuan3D-2 native server service with text-to-3D support

Server: 06:53:55 | INFO | [BACKGROUND_SERVICES](NO_SESSI) | Background Services Status Report:
06:53:55 | INFO | [SYSTEM](NO_SESSI) | Started service monitoring

Server: 06:53:55 | INFO | [BACKGROUND_SERVICES](NO_SESSI) |   STOPPED hunyuan3d_native: stopped (restarts: 0/2)
06:53:55 | INFO | [BACKGROUND_SERVICES](NO_SESSI) | Background Services Status Report:
06:53:55 | INFO | [BACKGROUND_SERVICES](NO_SESSI) |   STOPPED hunyuan3d_native: stopped (restarts: 0/2)
06:53:55 | INFO | [BACKGROUND_SERVICES](NO_SESSI) | Background services initialized successfully

Server: Starting background services...
06:53:55 | INFO | [BACKGROUND_SERVICES](NO_SESSI) | Starting background services...
[BACKGROUND] Starting background services...
[BACKGROUND] Starting hunyuan3d_native...
[BACKGROUND] Starting hunyuan3d_native...
06:53:55 | INFO | [BACKGROUND_SERVICES](NO_SESSI) | Starting service: hunyuan3d_native

Server: [BACKGROUND] Executing batch file: run-with-text_to_3d.bat
06:53:55 | INFO | [BACKGROUND_SERVICES](NO_SESSI) | Started batch service hunyuan3d_native with PID 28636 (hidden window)

Server: [BACKGROUND] Waiting for hunyuan3d_native to be ready...
06:53:57 | INFO | [BACKGROUND_SERVICES](NO_SESSI) | Service hunyuan3d_native started successfully and is ready

Server: [BACKGROUND] hunyuan3d_native started successfully
06:53:59 | INFO | [BACKGROUND_SERVICES](NO_SESSI) | Background Services Status Report:

Server: 06:53:59 | INFO | [BACKGROUND_SERVICES](NO_SESSI) |   RUNNING hunyuan3d_native: running (restarts: 0/2)
06:53:59 | INFO | [BACKGROUND_SERVICES](NO_SESSI) |     Health check: http://localhost:8080/
06:53:59 | INFO | [BACKGROUND_SERVICES](NO_SESSI) | All background services started successfully

Server: [BACKGROUND] Background services started successfully
Background services initialization completed
============================================================
LOADING HUNYUAN3D-2 PIPELINE
============================================================
[HUNYUAN3D] Starting Hunyuan3D-2 pipeline initialization...
06:53:59 | INFO | [HUNYUAN3D_INTEGRATED](NO_SESSI) | Loading Hunyuan3D-2 pipeline components...

Server: [HUNYUAN3D] Stage 1/6: Setting up Hunyuan3D-2 environment...
06:53:59 | INFO | [HUNYUAN3D_INTEGRATED](NO_SESSI) | Hunyuan3D-2 environment setup completed

Server: [HUNYUAN3D] Stage 2/6: Loading background removal model...
06:54:11 | INFO | [HUNYUAN3D_INTEGRATED](NO_SESSI) | Background removal worker loaded

Server stderr: WARNING[XFORMERS]: xFormers can't load C++/CUDA extensions. xFormers was built for:
    PyTorch 2.7.0+cu126 with CUDA 1206 (you have 2.7.1+cpu)
    Python  3.12.10 (you have 3.12.10)
  Please reinstall xformers (see https://github.com/facebookresearch/xformers#installing-xformers)
  Memory-efficient attention, SwiGLU, sparse and more won't be available.
Traceback (most recent call last):
  File "E:\3D AI Studio\.venv\Lib\site-packages\xformers\_cpp_lib.py", line 132, in _register_extensions
    torch.ops.load_library(ext_specs.origin)
  File "E:\3D AI Studio\.venv\Lib\site-packages\torch\_ops.py", line 1392, in load_library
    ctypes.CDLL(path)
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.12_3.12.2800.0_x64__qbz5n2kfra8p0\Lib\ctypes\__init__.py", line 379, in __init__
    self._handle = _dlopen(self._name, mode)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: Could not find module 'E:\3D AI Studio\.venv\Lib\site-packages\xformers\_C.pyd' (or one of its dependencies). Try using the full path with constructor syntax.

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\3D AI Studio\.venv\Lib\site-packages\xformers\_cpp_lib.py", line 142, in <module>
    _build_metadata = _register_extensions()
                      ^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\xformers\_cpp_lib.py", line 134, in _register_extensions
    raise xFormersInvalidLibException(build_metadata) from exc
xformers._cpp_lib.xFormersInvalidLibException: xFormers can't load C++/CUDA extensions. xFormers was built for:
    PyTorch 2.7.0+cu126 with CUDA 1206 (you have 2.7.1+cpu)
    Python  3.12.10 (you have 3.12.10)
  Please reinstall xformers (see https://github.com/facebookresearch/xformers#installing-xformers)
  Memory-efficient attention, SwiGLU, sparse and more won't be available.

Server: [HUNYUAN3D] Stage 3/6: Loading shape generation pipeline...
06:54:55 | ERROR | [HUNYUAN3D_INTEGRATED](NO_SESSI) | Error loading Hunyuan3D-2 pipeline components: Failed to import diffusers.models.transformers.pixart_transformer_2d because of the following error (look up to see its traceback):
DLL load failed while importing _C_flashattention: The specified module could not be found.

Server: 06:54:55 | ERROR | [HUNYUAN3D_INTEGRATED](NO_SESSI) | Traceback: Traceback (most recent call last):
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\utils\import_utils.py", line 820, in _get_module
    return importlib.import_module("." + module_name, self.__name__)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.12_3.12.2800.0_x64__qbz5n2kfra8p0\Lib\importlib\__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1310, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\models\transformers\__init__.py", line 5, in <module>
    from .auraflow_transformer_2d import AuraFlowTransformer2DModel
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\models\transformers\auraflow_transformer_2d.py", line 26, in <module>
    from ..attention_processor import (
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\models\attention_processor.py", line 35, in <module>
    import xformers.ops
  File "E:\3D AI Studio\.venv\Lib\site-packages\xformers\ops\__init__.py", line 9, in <module>
    from .fmha import (
  File "E:\3D AI Studio\.venv\Lib\site-packages\xformers\ops\fmha\__init__.py", line 10, in <module>
    from . import (
  File "E:\3D AI Studio\.venv\Lib\site-packages\xformers\ops\fmha\flash.py", line 55, in <module>
    from ... import _C_flashattention  # type: ignore[attr-defined]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ImportError: DLL load failed while importing _C_flashattention: The specified module could not be found.

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\3D AI Studio\hunyuan3d_integrated_pipeline.py", line 103, in load_pipeline_components
    from hy3dgen.shapegen import (
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\__init__.py", line 15, in <module>
    from .pipelines import Hunyuan3DDiTPipeline, Hunyuan3DDiTFlowMatchingPipeline
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\pipelines.py", line 30, in <module>
    from .models.autoencoders import ShapeVAE
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\models\__init__.py", line 26, in <module>
    from .autoencoders import ShapeVAE
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\models\autoencoders\__init__.py", line 18, in <module>
    from .model import ShapeVAE, VectsetVAE
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\models\autoencoders\model.py", line 22, in <module>
    from .surface_extractors import MCSurfaceExtractor, SurfaceExtractors
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\models\autoencoders\surface_extractors.py", line 79, in <module>
    from mmgp import offload
  File "E:\3D AI Studio\.venv\Lib\site-packages\mmgp\offload.py", line 70, in <module>
    from optimum.quanto import freeze,  qfloat8, qint4 , qint8, quantize, QModuleMixin, QLinear, QTensor,  quantize_module, register_qmodule
  File "E:\3D AI Studio\.venv\Lib\site-packages\optimum\quanto\__init__.py", line 19, in <module>
    from .models import *
  File "E:\3D AI Studio\.venv\Lib\site-packages\optimum\quanto\models\__init__.py", line 34, in <module>
    from .diffusers_models import *
  File "E:\3D AI Studio\.venv\Lib\site-packages\optimum\quanto\models\diffusers_models.py", line 30, in <module>
    from diffusers import PixArtTransformer2DModel
  File "<frozen importlib._bootstrap>", line 1412, in _handle_fromlist
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\utils\import_utils.py", line 811, in __getattr__
    value = getattr(module, name)
            ^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\utils\import_utils.py", line 810, in __getattr__
    module = self._get_module(self._class_to_module[name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\utils\import_utils.py", line 822, in _get_module
    raise RuntimeError(
RuntimeError: Failed to import diffusers.models.transformers.pixart_transformer_2d because of the following error (look up to see its traceback):
DLL load failed while importing _C_flashattention: The specified module could not be found.


Server: [HUNYUAN3D] ERROR: Failed to load integrated pipeline
[HUNYUAN3D] WARNING: Will attempt to use native client as fallback
Using Hunyuan3D-2 native server for pipeline
[HUNYUAN3D] SUCCESS: Native client fallback ready
============================================================
HUNYUAN3D-2 INITIALIZATION COMPLETED
============================================================
 * Serving Flask app 'app'
 * Debug mode: off

Server stderr: WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
Press CTRL+C to quit

Server detected as ready via stderr!
Server stderr: 127.0.0.1 - - [06/Jun/2025 06:55:00] "GET / HTTP/1.1" 200 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:55:00] "GET /assets/index-DrFs_VGd.js HTTP/1.1" 200 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:55:01] "GET /assets/index-ehFM-iJR.css HTTP/1.1" 200 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:55:01] "GET /api/config/huggingface-token HTTP/1.1" 200 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:55:01] "GET /api/sample-images HTTP/1.1" 200 -

Server: 06:55:02 | INFO | [API](NO_SESSI) | API: GET delighter_status | Status: 200 | Duration: 0.045s | IP: 127.0.0.1

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:55:02] "GET /api/delighter/status HTTP/1.1" 200 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:55:03] "GET /api/sample-images/∩┐╜Pngtree∩┐╜3d%20rendering%20of%20a%20greek_15549830-sharpen.png HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:55:05] "
Server stderr: GET /api/sample-images/5ac3a645-10a0-48c7-93e4-c53a5a1c81a7-upscale-3.4x.png HTTP/1.1
Server stderr: " 304 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:55:05] "GET /api/sample-images/b0da10f6-3d28-4a74-95b8-3fa373c2d7a2.png HTTP/1.1
Server stderr: " 304 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:55:05] "
Server stderr: GET /api/sample-images/a793b9fc-3485-4c04-b57d-95cdb356c57c-upscale-3.4x.png HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:55:05] "GET /api/sample-images/d5aeb72c-82af-45f1-b518-ea72280119b3-sharpen-face-upscale-3.4x.jpeg HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:55:05] "GET /api/sample-images/d9ba77aa-ff91-4e43-ad55-fabe51244dc8.png HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:55:06] "GET /api/sample-images/02f234e7-140d-4775-9563-067b4e5862c4.png HTTP/1.1
Server stderr: " 304 -
127.0.0.1 - - [06/Jun/2025 06:55:06] "GET /api/sample-images/10745cfe-80a0-48f0-a56e-0308e9f870f2-upscale-3.4x.png HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:55:06] "GET /api/sample-images/50abdc8c-5c16-4bda-9ffa-e5ba71adb3eb-sharpen-upscale-3.4x.jpeg HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:55:06] "GET /api/sample-images/18722263-upscale-4x.jpeg HTTP/1.1
Server stderr: " 304 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:55:06] "GET /api/sample-images/da6939f2-25df-4133-94af-8b242e042314.png HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:55:06] "GET /api/sample-images/DP-23915-004-sharpen.jpeg HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:55:06] "GET /api/sample-images/ef257036-61ce-40d0-b627-83761afc45ed-upscale-3.4x.png HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:55:06] "GET /api/sample-images/fede52c1-c968-4daa-88e7-c753af5887ad.png HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:55:06] "GET /api/sample-images/istockphoto-173559916-612x612.jpg HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:55:06] "GET /api/sample-images/mayan-pot-in-the-form-of-a-kneeling-figure-800-1200_u-l-q1nhita0.jpg HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:55:19] "GET /api/health HTTP/1.1" 200 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:55:29] "GET /api/pipelines/available HTTP/1.1" 200 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:55:29] "GET /api/pipelines/available HTTP/1.1" 200 -

Server: [SPARSE] Backend: spconv, Attention: flash_attn
Warning: Could not import Trellis modules: No module named 'trellis.representations.mesh.flexicubes.flexicubes'
Trying to use Trellis environment at: E:\3D AI Studio\Resources\TRELLIS\app\env\Scripts\python.exe
Trellis pipeline loaded successfully
DEBUG: ===== PIPELINE AVAILABILITY CHECK =====
DEBUG: Hunyuan3D pipeline object: <hunyuan3d_native_client.Hunyuan3DNativeClientWrapper object at 0x000001F11AEC2240>
DEBUG: Pipeline type: Hunyuan3DNativeClientWrapper
DEBUG: Using native client availability check
DEBUG: Native client server available: True
DEBUG: Final availability result: True
DEBUG: =========================================
DEBUG: ===== PIPELINE AVAILABILITY CHECK =====
DEBUG: Hunyuan3D pipeline object: <hunyuan3d_native_client.Hunyuan3DNativeClientWrapper object at 0x000001F11AEC2240>
DEBUG: Pipeline type: Hunyuan3DNativeClientWrapper
DEBUG: Using native client availability check
DEBUG: Native client server available: True
DEBUG: Final availability result: True
DEBUG: =========================================
06:55:31 | INFO | [API](NO_SESSI) | API: GET get_background_services_status | Status: 200 | Duration: 2.057s | IP: 127.0.0.1

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:55:31] "GET /api/background-services/status HTTP/1.1" 200 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:55:32] "GET /api/system/resources HTTP/1.1" 200 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:55:49] "GET /api/health HTTP/1.1" 200 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:55:52] "GET /api/pipelines/available HTTP/1.1" 200 -

Server: DEBUG: ===== PIPELINE AVAILABILITY CHECK =====
DEBUG: Hunyuan3D pipeline object: <hunyuan3d_native_client.Hunyuan3DNativeClientWrapper object at 0x000001F11AEC2240>
DEBUG: Pipeline type: Hunyuan3DNativeClientWrapper
DEBUG: Using native client availability check
DEBUG: Native client server available: True
DEBUG: Final availability result: True
DEBUG: =========================================
06:55:54 | INFO | [API](NO_SESSI) | API: GET get_background_services_status | Status: 200 | Duration: 2.060s | IP: 127.0.0.1

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:55:54] "GET /api/background-services/status HTTP/1.1" 200 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:55:55] "GET /api/system/resources HTTP/1.1" 200 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:57:03] "GET /api/sample-images/10745cfe-80a0-48f0-a56e-0308e9f870f2-upscale-3.4x.png HTTP/1.1" 200 -

Server stderr: 127.0.0.1 - - [06/Jun/2025 06:57:27] "POST /api/proxy-image HTTP/1.1" 200 -
