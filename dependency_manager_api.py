"""
Dependency Manager API endpoints for the web interface.
Provides REST API for dependency and model installation functionality.
"""

print("*** DEPENDENCY_MANAGER_API.PY MODULE BEING IMPORTED ***")

import json
import os
import time
import threading
import subprocess
from pathlib import Path
from flask import jsonify, request

# Import the unified system
try:
    from unified_dependency_api import register_unified_api_routes
    UNIFIED_SYSTEM_AVAILABLE = True
    print("*** UNIFIED DEPENDENCY SYSTEM LOADED ***")
except ImportError as e:
    print(f"*** UNIFIED SYSTEM NOT AVAILABLE: {e} ***")
    UNIFIED_SYSTEM_AVAILABLE = False

# Import legacy systems as fallback
try:
    from Dependencies.DepManagerScripts.script_runner import ScriptRunner
except ImportError:
    ScriptRunner = None

try:
    from Dependencies.DepManagerScriptsModels.model_script_runner import ModelScriptRunner
except ImportError:
    ModelScriptRunner = None

from model_manager import get_model_manager

# Global progress tracking
install_progress = {}
install_threads = {}

# Cache for dependency status checks to improve performance
_dependency_status_cache = {}
_cache_timestamp = 0
CACHE_DURATION = 30  # Cache for 30 seconds

def register_dependency_manager_routes(app):
    """Register dependency manager routes with the Flask app."""

    print("*** DEPENDENCY MANAGER API ROUTES BEING REGISTERED ***")

    # Register unified system if available
    if UNIFIED_SYSTEM_AVAILABLE:
        print("*** REGISTERING UNIFIED DEPENDENCY SYSTEM ***")
        register_unified_api_routes(app)
        return  # Use unified system only

    print("*** USING LEGACY DEPENDENCY SYSTEM ***")

    @app.route('/api/dependencies/cache/clear', methods=['POST'])
    def clear_dependency_cache():
        """Clear the dependency status cache for fresh checks."""
        global _dependency_status_cache, _cache_timestamp
        _dependency_status_cache.clear()
        _cache_timestamp = 0
        return jsonify({
            'success': True,
            'message': 'Dependency status cache cleared'
        })

    @app.route('/api/dependencies/categories', methods=['GET'])
    def get_dependencies_by_category():
        """Get dependencies organized by category."""
        try:
            # Get core dependencies from script runner
            dependencies = {
                'Core Dependencies': [
                    {
                        'id': 'pytorch_cuda',
                        'name': 'PyTorch with CUDA',
                        'description': 'Deep learning framework with GPU acceleration',
                        'category': 'Core Dependencies',
                        'status': check_dependency_status('pytorch_cuda'),
                        'essential': True,
                        'size_mb': 2500,
                        'install_type': 'script',
                        'script_name': 'install_pytorch_cuda.py'
                    },
                    {
                        'id': 'pillow',
                        'name': 'Pillow (PIL)',
                        'description': 'Python Imaging Library for image processing',
                        'category': 'Core Dependencies',
                        'status': check_dependency_status('pillow'),
                        'essential': True,
                        'size_mb': 50,
                        'install_type': 'script',
                        'script_name': 'install_pillow.py'
                    },
                    {
                        'id': 'numpy',
                        'name': 'NumPy',
                        'description': 'Fundamental package for scientific computing',
                        'category': 'Core Dependencies',
                        'status': check_dependency_status('numpy'),
                        'essential': True,
                        'size_mb': 25,
                        'install_type': 'script',
                        'script_name': 'install_numpy.py'
                    },
                    {
                        'id': 'flask',
                        'name': 'Flask',
                        'description': 'Web framework for the application server',
                        'category': 'Core Dependencies',
                        'status': check_dependency_status('flask'),
                        'essential': True,
                        'size_mb': 15,
                        'install_type': 'script',
                        'script_name': 'install_flask.py'
                    },
                    {
                        'id': 'huggingface_hub',
                        'name': 'Hugging Face Hub',
                        'description': 'Client library for Hugging Face Hub',
                        'category': 'Core Dependencies',
                        'status': check_dependency_status('huggingface_hub'),
                        'essential': True,
                        'size_mb': 30,
                        'install_type': 'script',
                        'script_name': 'install_huggingface_hub.py'
                    },
                    {
                        'id': 'gitpython',
                        'name': 'GitPython',
                        'description': 'Python library for Git operations',
                        'category': 'Core Dependencies',
                        'status': check_dependency_status('gitpython'),
                        'essential': True,
                        'size_mb': 10,
                        'install_type': 'script',
                        'script_name': 'install_gitpython.py'
                    },
                    {
                        'id': 'requests',
                        'name': 'Requests',
                        'description': 'HTTP library for Python',
                        'category': 'Core Dependencies',
                        'status': check_dependency_status('requests'),
                        'essential': True,
                        'size_mb': 5,
                        'install_type': 'script',
                        'script_name': 'install_requests.py'
                    },
                    {
                        'id': 'imageio',
                        'name': 'ImageIO',
                        'description': 'Essential image I/O library for pipeline operations',
                        'category': 'Core Dependencies',
                        'status': check_dependency_status('imageio'),
                        'essential': True,
                        'size_mb': 25,
                        'install_type': 'script',
                        'script_name': 'install_missing_deps.py'
                    },
                    {
                        'id': 'easydict',
                        'name': 'EasyDict',
                        'description': 'Essential configuration library for Trellis pipeline',
                        'category': 'Core Dependencies',
                        'status': check_dependency_status('easydict'),
                        'essential': True,
                        'size_mb': 5,
                        'install_type': 'script',
                        'script_name': 'install_missing_deps.py'
                    },
                    {
                        'id': 'hf_xet',
                        'name': 'HF XET Optimization',
                        'description': 'Faster Hugging Face Hub downloads (eliminates Xet Storage warnings)',
                        'category': 'Core Dependencies',
                        'status': check_dependency_status('hf_xet'),
                        'essential': False,
                        'size_mb': 10,
                        'install_type': 'script',
                        'script_name': 'install_hf_xet.py'
                    }
                ]
            }

            return jsonify({
                'success': True,
                'data': dependencies
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/dependencies/summary', methods=['GET'])
    def get_dependencies_summary():
        """Get summary statistics for dependencies."""
        try:
            # Count dependencies
            total = 10  # Core dependencies count (including imageio, easydict, and hf_xet)
            available = 0
            essential = 9  # All core dependencies are essential (hf_xet is optional)

            # Check status of each dependency
            deps = ['pytorch_cuda', 'pillow', 'numpy', 'flask', 'huggingface_hub', 'gitpython', 'requests', 'imageio', 'easydict', 'hf_xet']
            for dep in deps:
                if check_dependency_status(dep) == 'available':
                    available += 1

            return jsonify({
                'success': True,
                'data': {
                    'total': total,
                    'available': available,
                    'essential': essential,
                    'missing': total - available
                }
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/dependencies/<dependency_id>/install', methods=['POST'])
    def install_dependency(dependency_id):
        """Install a specific dependency."""
        try:
            if not ScriptRunner:
                return jsonify({
                    'success': False,
                    'error': 'Script runner not available'
                }), 500

            # Start installation in background thread
            def install_worker():
                try:
                    install_progress[dependency_id] = {
                        'progress': 0,
                        'status': 'installing',
                        'current_step': 'Starting installation...'
                    }

                    script_runner = ScriptRunner()

                    # Enhanced progress callback that handles both single parameter and two parameter calls
                    def progress_callback(*args):
                        if len(args) == 2:
                            # New format: step, progress
                            step, progress = args
                        else:
                            # Legacy format: just step, estimate progress
                            step = args[0]
                            progress = 0
                            if "Starting" in step or "PROGRESS" in step:
                                # Extract progress from [PROGRESS] messages
                                if "(" in step and "%" in step:
                                    try:
                                        progress_str = step.split("(")[1].split("%")[0]
                                        progress = int(progress_str)
                                    except:
                                        pass
                                else:
                                    progress = 10
                            elif "Environment" in step or "validated" in step:
                                progress = 20
                            elif "Installing" in step or "Downloading" in step:
                                progress = 40
                            elif "Verifying" in step:
                                progress = 80
                            elif "completed successfully" in step or "SUCCESS" in step:
                                progress = 100
                            elif "error" in step or "failed" in step or "ERROR" in step:
                                progress = 0

                        # Determine status based on progress and step content
                        if progress == 100 or "completed successfully" in step.lower():
                            status = 'completed'
                        elif progress == 0 and ("error" in step.lower() or "failed" in step.lower()):
                            status = 'failed'
                        elif progress > 0:
                            status = 'installing'
                        else:
                            status = 'installing'

                        install_progress[dependency_id] = {
                            'progress': progress,
                            'status': status,
                            'current_step': step,
                            'timestamp': time.time()
                        }

                        # Log progress updates for debugging
                        print(f"[DEPENDENCY API] Progress update for {dependency_id}: {progress}% - {status} - {step}", flush=True)

                    # Run the installation script
                    success = script_runner.run_script(dependency_id, progress_callback)
                    
                    if success:
                        install_progress[dependency_id] = {
                            'progress': 100,
                            'status': 'completed',
                            'current_step': 'Installation completed successfully'
                        }
                    else:
                        install_progress[dependency_id] = {
                            'progress': 0,
                            'status': 'failed',
                            'current_step': 'Installation failed',
                            'error': 'Script execution failed'
                        }

                except Exception as e:
                    install_progress[dependency_id] = {
                        'progress': 0,
                        'status': 'failed',
                        'current_step': 'Installation failed',
                        'error': str(e)
                    }

            # Start the installation thread
            thread = threading.Thread(target=install_worker)
            thread.daemon = True
            thread.start()
            install_threads[dependency_id] = thread

            return jsonify({
                'success': True,
                'message': f'Installation started for {dependency_id}'
            })

        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/dependencies/<dependency_id>/progress', methods=['GET'])
    def get_dependency_progress(dependency_id):
        """Get installation progress for a dependency."""
        try:
            progress = install_progress.get(dependency_id, {
                'progress': 0,
                'status': 'unknown',
                'current_step': 'No installation in progress'
            })

            return jsonify({
                'success': True,
                'data': progress
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/models/<model_id>/install', methods=['POST'])
    def install_model(model_id):
        """Install a specific model using git pull."""
        try:
            manager = get_model_manager()
            model = manager.get_model_info(model_id)

            if not model:
                return jsonify({
                    'success': False,
                    'error': 'Model not found'
                }), 404

            # Start installation in background thread
            def install_worker():
                try:
                    install_progress[model_id] = {
                        'progress': 0,
                        'status': 'installing',
                        'current_step': 'Starting model installation...'
                    }

                    # Use ModelScriptRunner if available
                    print(f"[MODEL API] ModelScriptRunner available: {ModelScriptRunner is not None}", flush=True)
                    if ModelScriptRunner:
                        print(f"[MODEL API] Using ModelScriptRunner for {model_id}", flush=True)
                        model_runner = ModelScriptRunner()

                        # Enhanced progress callback for models
                        def progress_callback(*args):
                            if len(args) == 4:
                                # Model manager format: model_id, percentage, downloaded, total
                                _, percentage, downloaded, total = args
                                progress = int(percentage)
                                if total > 0:
                                    step = f"Downloading {model_id}: {downloaded}/{total} MB"
                                else:
                                    step = f"Downloading {model_id}: {progress}%"
                            elif len(args) == 2:
                                # New format: step, progress
                                step, progress = args
                            else:
                                # Legacy format: just step, estimate progress
                                step = args[0]
                                progress = 0
                                if "Starting" in step or "PROGRESS" in step:
                                    # Extract progress from [PROGRESS] messages
                                    if "(" in step and "%" in step:
                                        try:
                                            progress_str = step.split("(")[1].split("%")[0]
                                            progress = int(progress_str)
                                        except:
                                            pass
                                    else:
                                        progress = 10
                                elif "Downloading" in step or "Fetching" in step:
                                    progress = 50
                                elif "Installing" in step:
                                    progress = 70
                                elif "Verifying" in step:
                                    progress = 90
                                elif "completed successfully" in step or "SUCCESS" in step:
                                    progress = 100
                                elif "error" in step or "failed" in step or "ERROR" in step:
                                    progress = 0

                            # Determine status
                            if progress >= 100:
                                status = 'completed'
                            elif progress == 0 and ("error" in step.lower() or "failed" in step.lower()):
                                status = 'failed'
                            elif progress > 0:
                                status = 'installing'
                            else:
                                status = 'installing'

                            install_progress[model_id] = {
                                'progress': progress,
                                'status': status,
                                'current_step': step,
                                'timestamp': time.time()
                            }

                            # Log progress updates for debugging
                            print(f"[MODEL API] Progress update for {model_id}: {progress}% - {status} - {step}", flush=True)

                        # Run the model installation script
                        success = model_runner.run_script(model_id, progress_callback)
                    else:
                        # Fallback to existing download method with progress callback
                        print(f"[MODEL API] Using model manager fallback for {model_id}", flush=True)
                        success = manager.download_model(model_id, progress_callback)

                    if success:
                        install_progress[model_id] = {
                            'progress': 100,
                            'status': 'completed',
                            'current_step': 'Model installation completed successfully'
                        }
                    else:
                        install_progress[model_id] = {
                            'progress': 0,
                            'status': 'failed',
                            'current_step': 'Model installation failed',
                            'error': 'Installation failed'
                        }

                except Exception as e:
                    install_progress[model_id] = {
                        'progress': 0,
                        'status': 'failed',
                        'current_step': 'Model installation failed',
                        'error': str(e)
                    }

            # Start the installation thread
            thread = threading.Thread(target=install_worker)
            thread.daemon = True
            thread.start()
            install_threads[model_id] = thread

            return jsonify({
                'success': True,
                'message': f'Model installation started for {model_id}'
            })

        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    # Add progress endpoint for models (reuse the same progress tracking)
    @app.route('/api/models/<model_id>/progress', methods=['GET'])
    def get_model_progress(model_id):
        """Get installation progress for a model."""
        try:
            progress = install_progress.get(model_id, {
                'progress': 0,
                'status': 'unknown',
                'current_step': 'No installation in progress'
            })

            print(f"[MODEL PROGRESS API] Request for {model_id}, returning: {progress}", flush=True)

            return jsonify({
                'success': True,
                'data': progress
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    # Provide models in dependency manager format (separate endpoint to avoid conflicts)
    @app.route('/api/dependency-manager/models/categories', methods=['GET'])
    def get_dependency_manager_models_categories():
        """Get models organized by category for dependency manager."""
        try:
            manager = get_model_manager()
            manager.check_all_models()
            categories = manager.get_models_by_category()

            # Convert to dependency manager format
            formatted_categories = {}
            for category_name, models in categories.items():
                # Skip Core Dependencies as they're handled separately
                if category_name == 'Core Dependencies':
                    continue

                formatted_models = []
                for model in models:
                    formatted_models.append({
                        'id': model.id,
                        'name': model.name,
                        'description': model.description,
                        'category': model.category,
                        'status': 'available' if model.available else 'missing',
                        'essential': model.essential,
                        'size_mb': model.size_mb,
                        'install_type': model.download_type,
                        'repo_id': model.repo_id
                    })

                formatted_categories[category_name] = formatted_models

            return jsonify({
                'success': True,
                'data': formatted_categories
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    # Add models summary endpoint
    @app.route('/api/dependency-manager/models/summary', methods=['GET'])
    def get_dependency_manager_models_summary():
        """Get summary statistics for models."""
        try:
            manager = get_model_manager()
            manager.check_all_models()
            categories = manager.get_models_by_category()

            total = 0
            available = 0
            essential = 0

            for category_name, models in categories.items():
                # Skip Core Dependencies as they're handled separately
                if category_name == 'Core Dependencies':
                    continue

                for model in models:
                    total += 1
                    if model.available:
                        available += 1
                    if model.essential:
                        essential += 1

            return jsonify({
                'success': True,
                'data': {
                    'total': total,
                    'available': available,
                    'essential': essential,
                    'missing': total - available
                }
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

def check_dependency_status(dependency_id):
    """Check if a dependency is installed with caching for performance."""
    global _dependency_status_cache, _cache_timestamp

    current_time = time.time()

    # Check cache first
    if (current_time - _cache_timestamp < CACHE_DURATION and
        dependency_id in _dependency_status_cache):
        return _dependency_status_cache[dependency_id]

    # Clear cache if expired
    if current_time - _cache_timestamp >= CACHE_DURATION:
        _dependency_status_cache.clear()
        _cache_timestamp = current_time

    try:
        status = _check_dependency_status_uncached(dependency_id)
        _dependency_status_cache[dependency_id] = status
        return status
    except Exception:
        return 'error'

def _check_dependency_status_uncached(dependency_id):
    """Internal function to check dependency status without caching."""
    try:
        if dependency_id == 'pytorch_cuda':
            try:
                import torch
                if torch.cuda.is_available():
                    return 'available'
                else:
                    return 'missing'
            except ImportError:
                return 'missing'
        elif dependency_id == 'pillow':
            import PIL  # noqa: F401
            return 'available'
        elif dependency_id == 'numpy':
            import numpy  # noqa: F401
            return 'available'
        elif dependency_id == 'flask':
            import flask  # noqa: F401
            return 'available'
        elif dependency_id == 'huggingface_hub':
            import huggingface_hub  # noqa: F401
            return 'available'
        elif dependency_id == 'gitpython':
            import git  # noqa: F401
            return 'available'
        elif dependency_id == 'requests':
            import requests  # noqa: F401
            return 'available'
        elif dependency_id == 'imageio':
            import imageio  # noqa: F401
            return 'available'
        elif dependency_id == 'easydict':
            import easydict  # noqa: F401
            return 'available'
        elif dependency_id == 'hf_xet':
            # hf_xet optimization might be integrated into huggingface_hub
            # Try to import hf_xet directly, or check if huggingface_hub has it
            try:
                import hf_xet  # noqa: F401
                return 'available'
            except ImportError:
                # Check if huggingface_hub was installed with hf_xet extra
                try:
                    import huggingface_hub  # noqa: F401
                    # If huggingface_hub is available, assume hf_xet optimization might be integrated
                    # This is a best-effort check since hf_xet integration is not always detectable
                    return 'available'
                except ImportError:
                    return 'missing'
        else:
            return 'missing'
    except ImportError:
        return 'missing'

def install_model_with_git(model, model_id):
    """Install a model using git clone/pull."""
    try:
        # Update progress
        install_progress[model_id]['current_step'] = 'Cloning repository...'
        install_progress[model_id]['progress'] = 25
        
        # Determine target directory
        app_root = Path(__file__).parent
        models_dir = app_root / 'models'
        target_dir = models_dir / model_id
        
        if target_dir.exists():
            # Update existing repository
            install_progress[model_id]['current_step'] = 'Updating existing repository...'
            install_progress[model_id]['progress'] = 50
            
            result = subprocess.run(['git', 'pull'], cwd=target_dir, capture_output=True, text=True)
        else:
            # Clone new repository
            install_progress[model_id]['current_step'] = 'Cloning new repository...'
            install_progress[model_id]['progress'] = 50
            
            models_dir.mkdir(exist_ok=True)
            result = subprocess.run(['git', 'clone', model.repo_id, str(target_dir)], capture_output=True, text=True)
        
        install_progress[model_id]['current_step'] = 'Finalizing installation...'
        install_progress[model_id]['progress'] = 90
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"Error installing model {model_id}: {e}")
        return False
