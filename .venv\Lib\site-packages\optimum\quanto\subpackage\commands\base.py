# Copyright 2024 The HuggingFace Team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from optimum.commands import BaseOptimumCLICommand, CommandInfo
from optimum.commands.optimum_cli import optimum_cli_subcommand

from .quantize import QuantizeCommand


__all__ = ["QuantoCommand"]


@optimum_cli_subcommand()
class QuantoCommand(BaseOptimumCLICommand):
    COMMAND = CommandInfo(name="quanto", help="Hugging Face models quantization tools")
    SUBCOMMANDS = (
        CommandInfo(
            name="quantize",
            help="Quantize Hugging Face models.",
            subcommand_class=QuantizeCommand,
        ),
    )
