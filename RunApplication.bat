@echo off
title 3D AI Studio - Startup
echo ========================================
echo          3D AI Studio Launcher
echo ========================================
echo.

:: Check if Python is available
echo [1/5] Checking Python installation...
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Error: Python is required but not found.
    echo Please install Python 3.8+ from: https://www.python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation.
    pause
    exit /b 1
)
echo ✓ Python found

:: Check if virtual environment exists
echo [2/4] Checking virtual environment...
if not exist ".venv" (
    echo Creating virtual environment...
    python -m venv .venv
    if %ERRORLEVEL% NEQ 0 (
        echo Error: Failed to create virtual environment
        pause
        exit /b 1
    )
    echo Installing comprehensive dependencies (Trellis + Hunyuan3D-2)...
    call .venv\Scripts\activate.bat
    python install_startup_deps.py
    if %ERRORLEVEL% NEQ 0 (
        echo Warning: Some dependencies may not have installed correctly
        echo The application will attempt to start with available functionality
    )
)
echo [OK] Virtual environment ready

:: Quick environment check - all dependencies managed at startup
echo [3/4] Quick environment check...
echo   [INFO] All dependencies installed automatically at startup
echo   [INFO] Trellis and Hunyuan3D-2 systems integrated in single environment
python validate_environments.py --quick
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo   [INFO] Some dependencies may need attention
    echo   [INFO] Dependencies will be installed/repaired automatically
    echo   [INFO] Application will start with available functionality
    echo.
) else (
    echo   [OK] Basic environments ready
)

:: Activate virtual environment
echo [4/4] Activating Python environment...
call .venv\Scripts\activate.bat
if %ERRORLEVEL% NEQ 0 (
    echo Error: Failed to activate virtual environment
    echo   Use Dependency Manager to repair environments
    pause
    exit /b 1
)

:: Check for critical dependencies
python -c "from install_startup_deps import check_critical_dependencies; exit(0 if check_critical_dependencies() else 1)" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Installing missing dependencies...
    python install_startup_deps.py >nul 2>&1
    if %ERRORLEVEL% NEQ 0 (
        echo Warning: Some dependencies may not have installed correctly
        echo The application will attempt to start with available functionality
    )
)
echo   [OK] Python environment activated

:: Check if Node.js is available for Electron app
echo Checking Node.js for Electron app...
where node >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Error: Node.js is required for the desktop application.
    echo Please download and install Node.js from: https://nodejs.org/
    pause
    exit /b 1
) else (
    echo [OK] Node.js found
    :: Build and prepare Electron app
    if exist "Resources\UI\package.json" (
        echo Preparing desktop application...
        cd Resources\UI
        call npm install >nul 2>&1
        call npm run build >nul 2>&1
        cd ..\..
        echo [OK] Desktop application ready
    )
)

:: Start the Electron application
echo Starting 3D AI Studio Desktop App...
echo.
echo ========================================
echo     3D AI Studio Desktop Application
echo     Starting in desktop window...
echo.
echo     🚀 STREAMLINED SETUP
echo     All dependencies installed automatically at startup!
echo.
echo     FEATURES:
echo     ✅ Trellis pipeline for high-quality 3D generation
echo     ✅ Hunyuan3D-2 native server with text-to-3D support
echo     ✅ Automatic background service management
echo     ✅ Comprehensive progress tracking
echo     ✅ Single virtual environment for all systems
echo.
echo     🎯 Ready to use - no manual setup required!
echo ========================================
echo.

cd Resources\UI
call npm run electron

pause
