2025-06-06 00:04:25 | INFO     | [SYSTEM](NO_SESSI) | 3d_ai_studio.app | System startup | Data: {"platform": "Windows-11-10.0.26100-SP0", "python_version": "3.12.10", "cpu_count": 88, "memory_total": "79.9 GB", "disk_free": "538.9 GB", "torch_version": "2.7.1+cpu", "cuda_available": false}
2025-06-06 00:04:25 | INFO     | [[STARTUP]]((NO_SESS) | 3d_ai_studio.app | 3D AI Studio starting up
2025-06-06 00:04:37 | INFO     | [[HUNYUAN3D_DIRECT]]((NO_SESS) | 3d_ai_studio.app | Initial setup status - Compilation: True, Models: True
2025-06-06 00:04:37 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services using main app logging system
2025-06-06 00:04:37 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Initializing background services...
2025-06-06 00:04:37 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Registered service: hunyuan3d_native
2025-06-06 00:04:37 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Registered Hunyuan3D-2 native server service with text-to-3D support
2025-06-06 00:04:37 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-06 00:04:37 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Started service monitoring
2025-06-06 00:04:37 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: stopped (restarts: 0/2)
2025-06-06 00:04:37 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-06 00:04:37 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: stopped (restarts: 0/2)
2025-06-06 00:04:37 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services initialized successfully
2025-06-06 00:04:37 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting background services...
2025-06-06 00:04:37 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting service: hunyuan3d_native
2025-06-06 00:04:37 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Started batch service hunyuan3d_native with PID 7968 (hidden window)
2025-06-06 00:04:39 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Service hunyuan3d_native started successfully and is ready
2025-06-06 00:04:41 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-06 00:04:41 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   RUNNING hunyuan3d_native: running (restarts: 0/2)
2025-06-06 00:04:41 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |     Health check: http://localhost:8080/
2025-06-06 00:04:41 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | All background services started successfully
2025-06-06 00:04:41 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Loading Hunyuan3D-2 pipeline components...
2025-06-06 00:04:41 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Hunyuan3D-2 environment setup completed
2025-06-06 00:04:45 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Background removal worker loaded
2025-06-06 00:04:54 | ERROR    | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Error loading Hunyuan3D-2 pipeline components: Failed to import diffusers.models.transformers.pixart_transformer_2d because of the following error (look up to see its traceback):
DLL load failed while importing _C_flashattention: The specified module could not be found.
2025-06-06 00:04:55 | ERROR    | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Traceback: Traceback (most recent call last):
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\utils\import_utils.py", line 820, in _get_module
    return importlib.import_module("." + module_name, self.__name__)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.12_3.12.2800.0_x64__qbz5n2kfra8p0\Lib\importlib\__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1310, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\models\transformers\__init__.py", line 5, in <module>
    from .auraflow_transformer_2d import AuraFlowTransformer2DModel
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\models\transformers\auraflow_transformer_2d.py", line 26, in <module>
    from ..attention_processor import (
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\models\attention_processor.py", line 35, in <module>
    import xformers.ops
  File "E:\3D AI Studio\.venv\Lib\site-packages\xformers\ops\__init__.py", line 9, in <module>
    from .fmha import (
  File "E:\3D AI Studio\.venv\Lib\site-packages\xformers\ops\fmha\__init__.py", line 10, in <module>
    from . import (
  File "E:\3D AI Studio\.venv\Lib\site-packages\xformers\ops\fmha\flash.py", line 55, in <module>
    from ... import _C_flashattention  # type: ignore[attr-defined]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ImportError: DLL load failed while importing _C_flashattention: The specified module could not be found.

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\3D AI Studio\hunyuan3d_integrated_pipeline.py", line 100, in load_pipeline_components
    from hy3dgen.shapegen import (
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\__init__.py", line 15, in <module>
    from .pipelines import Hunyuan3DDiTPipeline, Hunyuan3DDiTFlowMatchingPipeline
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\pipelines.py", line 30, in <module>
    from .models.autoencoders import ShapeVAE
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\models\__init__.py", line 26, in <module>
    from .autoencoders import ShapeVAE
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\models\autoencoders\__init__.py", line 18, in <module>
    from .model import ShapeVAE, VectsetVAE
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\models\autoencoders\model.py", line 22, in <module>
    from .surface_extractors import MCSurfaceExtractor, SurfaceExtractors
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\models\autoencoders\surface_extractors.py", line 79, in <module>
    from mmgp import offload
  File "E:\3D AI Studio\.venv\Lib\site-packages\mmgp\offload.py", line 70, in <module>
    from optimum.quanto import freeze,  qfloat8, qint4 , qint8, quantize, QModuleMixin, QLinear, QTensor,  quantize_module, register_qmodule
  File "E:\3D AI Studio\.venv\Lib\site-packages\optimum\quanto\__init__.py", line 19, in <module>
    from .models import *
  File "E:\3D AI Studio\.venv\Lib\site-packages\optimum\quanto\models\__init__.py", line 34, in <module>
    from .diffusers_models import *
  File "E:\3D AI Studio\.venv\Lib\site-packages\optimum\quanto\models\diffusers_models.py", line 30, in <module>
    from diffusers import PixArtTransformer2DModel
  File "<frozen importlib._bootstrap>", line 1412, in _handle_fromlist
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\utils\import_utils.py", line 811, in __getattr__
    value = getattr(module, name)
            ^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\utils\import_utils.py", line 810, in __getattr__
    module = self._get_module(self._class_to_module[name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\utils\import_utils.py", line 822, in _get_module
    raise RuntimeError(
RuntimeError: Failed to import diffusers.models.transformers.pixart_transformer_2d because of the following error (look up to see its traceback):
DLL load failed while importing _C_flashattention: The specified module could not be found.

2025-06-06 06:53:38 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | System startup | Data: {"platform": "Windows-11-10.0.26100-SP0", "python_version": "3.12.10", "cpu_count": 88, "memory_total": "79.9 GB", "disk_free": "539.2 GB", "torch_version": "2.7.1+cpu", "cuda_available": false}
2025-06-06 06:53:38 | INFO     | [[STARTUP]]((NO_SESS) | 3d_ai_studio.app | 3D AI Studio starting up
2025-06-06 06:53:55 | INFO     | [[HUNYUAN3D_DIRECT]]((NO_SESS) | 3d_ai_studio.app | Initial setup status - Compilation: True, Models: True
2025-06-06 06:53:55 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services using main app logging system
2025-06-06 06:53:55 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Initializing background services...
2025-06-06 06:53:55 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Registered service: hunyuan3d_native
2025-06-06 06:53:55 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Registered Hunyuan3D-2 native server service with text-to-3D support
2025-06-06 06:53:55 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-06 06:53:55 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Started service monitoring
2025-06-06 06:53:55 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: stopped (restarts: 0/2)
2025-06-06 06:53:55 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-06 06:53:55 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: stopped (restarts: 0/2)
2025-06-06 06:53:55 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services initialized successfully
2025-06-06 06:53:55 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting background services...
2025-06-06 06:53:55 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting service: hunyuan3d_native
2025-06-06 06:53:55 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Started batch service hunyuan3d_native with PID 28636 (hidden window)
2025-06-06 06:53:57 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Service hunyuan3d_native started successfully and is ready
2025-06-06 06:53:59 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-06 06:53:59 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   RUNNING hunyuan3d_native: running (restarts: 0/2)
2025-06-06 06:53:59 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |     Health check: http://localhost:8080/
2025-06-06 06:53:59 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | All background services started successfully
2025-06-06 06:53:59 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Loading Hunyuan3D-2 pipeline components...
2025-06-06 06:53:59 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Hunyuan3D-2 environment setup completed
2025-06-06 06:54:11 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Background removal worker loaded
2025-06-06 06:54:55 | ERROR    | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Error loading Hunyuan3D-2 pipeline components: Failed to import diffusers.models.transformers.pixart_transformer_2d because of the following error (look up to see its traceback):
DLL load failed while importing _C_flashattention: The specified module could not be found.
2025-06-06 06:54:55 | ERROR    | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Traceback: Traceback (most recent call last):
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\utils\import_utils.py", line 820, in _get_module
    return importlib.import_module("." + module_name, self.__name__)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.12_3.12.2800.0_x64__qbz5n2kfra8p0\Lib\importlib\__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1310, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\models\transformers\__init__.py", line 5, in <module>
    from .auraflow_transformer_2d import AuraFlowTransformer2DModel
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\models\transformers\auraflow_transformer_2d.py", line 26, in <module>
    from ..attention_processor import (
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\models\attention_processor.py", line 35, in <module>
    import xformers.ops
  File "E:\3D AI Studio\.venv\Lib\site-packages\xformers\ops\__init__.py", line 9, in <module>
    from .fmha import (
  File "E:\3D AI Studio\.venv\Lib\site-packages\xformers\ops\fmha\__init__.py", line 10, in <module>
    from . import (
  File "E:\3D AI Studio\.venv\Lib\site-packages\xformers\ops\fmha\flash.py", line 55, in <module>
    from ... import _C_flashattention  # type: ignore[attr-defined]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ImportError: DLL load failed while importing _C_flashattention: The specified module could not be found.

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\3D AI Studio\hunyuan3d_integrated_pipeline.py", line 103, in load_pipeline_components
    from hy3dgen.shapegen import (
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\__init__.py", line 15, in <module>
    from .pipelines import Hunyuan3DDiTPipeline, Hunyuan3DDiTFlowMatchingPipeline
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\pipelines.py", line 30, in <module>
    from .models.autoencoders import ShapeVAE
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\models\__init__.py", line 26, in <module>
    from .autoencoders import ShapeVAE
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\models\autoencoders\__init__.py", line 18, in <module>
    from .model import ShapeVAE, VectsetVAE
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\models\autoencoders\model.py", line 22, in <module>
    from .surface_extractors import MCSurfaceExtractor, SurfaceExtractors
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\models\autoencoders\surface_extractors.py", line 79, in <module>
    from mmgp import offload
  File "E:\3D AI Studio\.venv\Lib\site-packages\mmgp\offload.py", line 70, in <module>
    from optimum.quanto import freeze,  qfloat8, qint4 , qint8, quantize, QModuleMixin, QLinear, QTensor,  quantize_module, register_qmodule
  File "E:\3D AI Studio\.venv\Lib\site-packages\optimum\quanto\__init__.py", line 19, in <module>
    from .models import *
  File "E:\3D AI Studio\.venv\Lib\site-packages\optimum\quanto\models\__init__.py", line 34, in <module>
    from .diffusers_models import *
  File "E:\3D AI Studio\.venv\Lib\site-packages\optimum\quanto\models\diffusers_models.py", line 30, in <module>
    from diffusers import PixArtTransformer2DModel
  File "<frozen importlib._bootstrap>", line 1412, in _handle_fromlist
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\utils\import_utils.py", line 811, in __getattr__
    value = getattr(module, name)
            ^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\utils\import_utils.py", line 810, in __getattr__
    module = self._get_module(self._class_to_module[name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\utils\import_utils.py", line 822, in _get_module
    raise RuntimeError(
RuntimeError: Failed to import diffusers.models.transformers.pixart_transformer_2d because of the following error (look up to see its traceback):
DLL load failed while importing _C_flashattention: The specified module could not be found.

2025-06-06 06:55:02 | INFO     | [[API]]((NO_SESS) | 3d_ai_studio.app | API: GET delighter_status | Status: 200 | Duration: 0.045s | IP: 127.0.0.1
2025-06-06 06:55:31 | INFO     | [[API]]((NO_SESS) | 3d_ai_studio.app | API: GET get_background_services_status | Status: 200 | Duration: 2.057s | IP: 127.0.0.1
2025-06-06 06:55:54 | INFO     | [[API]]((NO_SESS) | 3d_ai_studio.app | API: GET get_background_services_status | Status: 200 | Duration: 2.060s | IP: 127.0.0.1
2025-06-06 08:18:14 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | System startup | Data: {"platform": "Windows-11-10.0.26100-SP0", "python_version": "3.12.10", "cpu_count": 88, "memory_total": "79.9 GB", "disk_free": "530.8 GB", "torch_version": "2.5.1+cu124", "cuda_available": true, "cuda_device": "NVIDIA GeForce RTX 3060", "cuda_memory": "12.0 GB"}
2025-06-06 08:18:14 | INFO     | [[STARTUP]]((NO_SESS) | 3d_ai_studio.app | 3D AI Studio starting up
2025-06-06 08:18:27 | INFO     | [[HUNYUAN3D_DIRECT]]((NO_SESS) | 3d_ai_studio.app | Initial setup status - Compilation: True, Models: True
2025-06-06 08:18:27 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Loading Hunyuan3D-2 pipeline components...
2025-06-06 08:18:27 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Hunyuan3D-2 environment setup completed
2025-06-06 08:18:36 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Background removal worker loaded
2025-06-06 08:20:58 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Shape generation pipeline loaded
2025-06-06 08:20:58 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Mesh processing workers loaded
2025-06-06 08:21:01 | WARNING  | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Failed to load texture generation pipeline: Model path Resources\Hunyuan3D2_WinPortable\HuggingFaceHub\models--tencent--Hunyuan3D-2\snapshots\34e28261f71c32975727be8db0eace439a280f82 not found
2025-06-06 08:21:06 | WARNING  | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Failed to load text-to-image pipeline: 'SafeTensorFile' object has no attribute 'get_slice'
2025-06-06 08:21:06 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | All Hunyuan3D-2 pipeline components loaded successfully
2025-06-06 08:21:10 | INFO     | [[API]]((NO_SESS) | 3d_ai_studio.app | API: GET delighter_status | Status: 200 | Duration: 0.038s | IP: 127.0.0.1
2025-06-06 08:30:09 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | System startup | Data: {"platform": "Windows-11-10.0.26100-SP0", "python_version": "3.12.10", "cpu_count": 88, "memory_total": "79.9 GB", "disk_free": "530.0 GB", "torch_version": "2.5.1+cu124", "cuda_available": true, "cuda_device": "NVIDIA GeForce RTX 3060", "cuda_memory": "12.0 GB"}
2025-06-06 08:30:09 | INFO     | [[STARTUP]]((NO_SESS) | 3d_ai_studio.app | 3D AI Studio starting up
2025-06-06 08:30:22 | INFO     | [[HUNYUAN3D_DIRECT]]((NO_SESS) | 3d_ai_studio.app | Initial setup status - Compilation: True, Models: True
2025-06-06 08:30:22 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Loading Hunyuan3D-2 pipeline components...
2025-06-06 08:30:22 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Hunyuan3D-2 environment setup completed
2025-06-06 08:30:27 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Background removal worker loaded
2025-06-06 08:30:51 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Shape generation pipeline loaded
2025-06-06 08:30:51 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Mesh processing workers loaded
2025-06-06 08:30:51 | WARNING  | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Failed to load texture generation pipeline: Model path Resources\Hunyuan3D2_WinPortable\HuggingFaceHub\models--tencent--Hunyuan3D-2\snapshots\34e28261f71c32975727be8db0eace439a280f82 not found
2025-06-06 08:30:51 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Texture generation will be unavailable (shape generation still works)
2025-06-06 08:30:52 | WARNING  | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Failed to load text-to-image pipeline: 'SafeTensorFile' object has no attribute 'get_slice'
2025-06-06 08:30:52 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Text-to-image will be unavailable (image-to-3D still works)
2025-06-06 08:30:52 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | All Hunyuan3D-2 pipeline components loaded successfully
2025-06-06 08:30:56 | INFO     | [[API]]((NO_SESS) | 3d_ai_studio.app | API: GET delighter_status | Status: 200 | Duration: 0.038s | IP: 127.0.0.1
2025-06-06 09:19:05 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | System startup | Data: {"platform": "Windows-11-10.0.26100-SP0", "python_version": "3.12.10", "cpu_count": 88, "memory_total": "79.9 GB", "disk_free": "530.0 GB", "torch_version": "2.5.1+cu124", "cuda_available": true, "cuda_device": "NVIDIA GeForce RTX 3060", "cuda_memory": "12.0 GB"}
2025-06-06 09:19:05 | INFO     | [[STARTUP]]((NO_SESS) | 3d_ai_studio.app | 3D AI Studio starting up
2025-06-06 09:19:19 | INFO     | [[HUNYUAN3D_DIRECT]]((NO_SESS) | 3d_ai_studio.app | Initial setup status - Compilation: True, Models: True
2025-06-06 09:19:19 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Loading Hunyuan3D-2 pipeline components...
2025-06-06 09:19:19 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Hunyuan3D-2 environment setup completed
2025-06-06 09:19:23 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Background removal worker loaded
2025-06-06 09:21:39 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Shape generation pipeline loaded
2025-06-06 09:21:39 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Mesh processing workers loaded
2025-06-06 09:24:18 | WARNING  | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Failed to load texture generation pipeline: Model path Resources\Hunyuan3D2_WinPortable\HuggingFaceHub\models--tencent--Hunyuan3D-2\snapshots\34e28261f71c32975727be8db0eace439a280f82 not found
2025-06-06 09:24:18 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Texture generation will be unavailable (shape generation still works)
2025-06-06 09:25:09 | WARNING  | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Failed to load text-to-image pipeline: 'SafeTensorFile' object has no attribute 'get_slice'
2025-06-06 09:25:09 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Text-to-image will be unavailable (image-to-3D still works)
2025-06-06 09:25:09 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | All Hunyuan3D-2 pipeline components loaded successfully
2025-06-06 09:54:50 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | System startup | Data: {"platform": "Windows-11-10.0.26100-SP0", "python_version": "3.12.10", "cpu_count": 88, "memory_total": "79.9 GB", "disk_free": "530.0 GB", "torch_version": "2.5.1+cu124", "cuda_available": true, "cuda_device": "NVIDIA GeForce RTX 3060", "cuda_memory": "12.0 GB"}
2025-06-06 09:54:50 | INFO     | [[STARTUP]]((NO_SESS) | 3d_ai_studio.app | 3D AI Studio starting up
2025-06-06 09:55:03 | INFO     | [[HUNYUAN3D_DIRECT]]((NO_SESS) | 3d_ai_studio.app | Initial setup status - Compilation: True, Models: True
