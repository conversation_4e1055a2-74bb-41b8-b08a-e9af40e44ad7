Metadata-Version: 2.1
Name: python-rapidjson
Version: 1.20
Summary: Python wrapper around rapidjson
Home-page: https://github.com/python-rapidjson/python-rapidjson
Author: <PERSON>
Author-email: <EMAIL>
Maintainer: <PERSON><PERSON>
Maintainer-email: <EMAIL>
License: MIT License
Keywords: json rapidjson
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: C++
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python
Requires-Python: >=3.6
Description-Content-Type: text/x-rst
License-File: LICENSE

.. -*- coding: utf-8 -*-
.. :Project:   python-rapidjson -- Introduction
.. :Author:    <PERSON> <<EMAIL>>
.. :License:   MIT License
.. :Copyright: © 2015 Ken Robbins
.. :Copyright: © 2016, 2017, 2018, 2020, 2022 Lele Gaifax
..

==================
 python-rapidjson
==================

Python wrapper around RapidJSON
===============================

:Authors: <AUTHORS>
:License: `MIT License`__
:Status: |build| |doc|

__ https://raw.githubusercontent.com/python-rapidjson/python-rapidjson/master/LICENSE
.. |build| image:: https://travis-ci.org/python-rapidjson/python-rapidjson.svg?branch=master
   :target: https://travis-ci.org/python-rapidjson/python-rapidjson
   :alt: Build status
.. |doc| image:: https://readthedocs.org/projects/python-rapidjson/badge/?version=latest
   :target: https://readthedocs.org/projects/python-rapidjson/builds/
   :alt: Documentation status

RapidJSON_ is an extremely fast C++ JSON parser and serialization library: this module
wraps it into a Python 3 extension, exposing its serialization/deserialization (to/from
either ``bytes``, ``str`` or *file-like* instances) and `JSON Schema`__ validation
capabilities.

Latest version documentation is automatically rendered by `Read the Docs`__.

__ http://json-schema.org/documentation.html
__ https://python-rapidjson.readthedocs.io/en/latest/


Getting Started
---------------

First install ``python-rapidjson``:

.. code-block:: bash

    $ pip install python-rapidjson

or, if you prefer `Conda`__:

.. code-block:: bash

    $ conda install -c conda-forge python-rapidjson

__ https://conda.io/docs/

Basic usage looks like this:

.. code-block:: python

    >>> import rapidjson
    >>> data = {'foo': 100, 'bar': 'baz'}
    >>> rapidjson.dumps(data)
    '{"foo":100,"bar":"baz"}'
    >>> rapidjson.loads('{"bar":"baz","foo":100}')
    {'bar': 'baz', 'foo': 100}
    >>>
    >>> class Stream:
    ...   def write(self, data):
    ...      print("Chunk:", data)
    ...
    >>> rapidjson.dump(data, Stream(), chunk_size=5)
    Chunk: b'{"foo'
    Chunk: b'":100'
    Chunk: b',"bar'
    Chunk: b'":"ba'
    Chunk: b'z"}'


Development
-----------

If you want to install the development version (maybe to contribute fixes or
enhancements) you may clone the repository:

.. code-block:: bash

    $ git clone --recursive https://github.com/python-rapidjson/python-rapidjson.git

.. note:: The ``--recursive`` option is needed because we use a *submodule* to
          include RapidJSON_ sources. Alternatively you can do a plain
          ``clone`` immediately followed by a ``git submodule update --init``.

          Alternatively, if you already have (a *compatible* version of)
          RapidJSON includes around, you can compile the module specifying
          their location with the option ``--rj-include-dir``, for example:

          .. code-block:: shell

             $ python3 setup.py build --rj-include-dir=/usr/include/rapidjson

A set of makefiles implement most common operations, such as *build*, *check*
and *release*; see ``make help`` output for a list of available targets.


Performance
-----------

``python-rapidjson`` tries to be as performant as possible while staying
compatible with the ``json`` module.

See the `this section`__ in the documentation for a comparison with other JSON libraries.

__ https://python-rapidjson.readthedocs.io/en/latest/benchmarks.html


Incompatibility
---------------

Although we tried to implement an API similar to the standard library ``json``, being a
strict *drop-in* replacement in not our goal and we have decided to depart from there in
some aspects. See `this section`__ in the documentation for further details.

__ https://python-rapidjson.readthedocs.io/en/latest/quickstart.html#incompatibilities

.. _RapidJSON: http://rapidjson.org/


Changes
-------

1.20 (2024-08-05)
~~~~~~~~~~~~~~~~~

* Rectify type hints if ``loads()`` and ``Decoder.__call__()`` (`issue #214`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/214

* Ensure ``Validator`` receives valid UTF-8 ``bytes``/``bytearray`` arguments

* Generate wheels on PyPI using Python 3.13.0rc1 release, thanks to cibuildwheel `2.20.0`__

  __ https://cibuildwheel.pypa.io/en/stable/changelog/#v2200


1.19 (2024-07-28)
~~~~~~~~~~~~~~~~~

* Properly dump subclasses of ``float`` with custom ``__repr__()`` method ( `issue #213`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/213


1.18 (2024-06-29)
~~~~~~~~~~~~~~~~~

* Expose PEP-484 typing stubs, thanks to Rodion Kosianenko and GoodWasHere (`PR #204`__)

  __ https://github.com/python-rapidjson/python-rapidjson/pull/204


1.17 (2024-05-18)
~~~~~~~~~~~~~~~~~

* Use `current master`__ version of rapidjson

  __ https://github.com/Tencent/rapidjson/compare/5e17dbed34eef33af8f3e734820b5dc547a2a3aa...ab1842a2dae061284c0a62dca1cc6d5e7e37e346

* Generate wheels on PyPI using Python 3.13b1 release, thanks to cibuildwheel `2.18.0`__

  __ https://cibuildwheel.pypa.io/en/stable/changelog/#v2180


1.16 (2024-02-28)
~~~~~~~~~~~~~~~~~

* Produce Python 3.8 wheels again, I deactivated it too eagerly, it's in *security fixes
  only* mode, not yet reached its `end-of-life` state


1.15 (2024-02-28)
~~~~~~~~~~~~~~~~~

* Honor the `recursion limit`__ also at parse time, to avoid attacks as described by
  `CVE-2024-27454`__

  __ https://docs.python.org/3.12/library/sys.html#sys.setrecursionlimit
  __ https://monicz.dev/CVE-2024-27454


1.14 (2023-12-14)
~~~~~~~~~~~~~~~~~

* Produce binary wheels for macOS/arm64, thanks to timothyjlaurent (`PR #195`__)

  __ https://github.com/python-rapidjson/python-rapidjson/pull/170


1.13 (2023-10-29)
~~~~~~~~~~~~~~~~~

* Fix handling of write_mode in dump functions (problem emerged discussing `issue #191`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/191


1.12 (2023-10-07)
~~~~~~~~~~~~~~~~~

* Generate wheels on PyPI using final Python 3.12 release, thanks to cibuildwheel `2.16.2`__

  __ https://cibuildwheel.readthedocs.io/en/stable/changelog/#v2162


1.11 (2023-09-11)
~~~~~~~~~~~~~~~~~

* Use `current master`__ version of rapidjson

  __ https://github.com/Tencent/rapidjson/compare/083f359f5c36198accc2b9360ce1e32a333231d9...5e17dbed34eef33af8f3e734820b5dc547a2a3aa

* Use cibuildwheel `2.15.0`__

  __ https://cibuildwheel.readthedocs.io/en/stable/changelog/#v2150


1.10 (2023-03-15)
~~~~~~~~~~~~~~~~~

* Use `current master`__ version of rapidjson

  __ https://github.com/Tencent/rapidjson/commit/083f359f5c36198accc2b9360ce1e32a333231d9

* Produce ppc64le wheels, thanks to mgiessing (`PR #170`__)

  __ https://github.com/python-rapidjson/python-rapidjson/pull/170

* Use cibuildwheel `2.12.1`__

  __ https://cibuildwheel.readthedocs.io/en/stable/changelog/#v2121


1.9 (2022-10-17)
~~~~~~~~~~~~~~~~

* Produce Python 3.11 wheels, thanks to ``cibuildwheel`` `2.11.1`__

  __ https://cibuildwheel.readthedocs.io/en/stable/changelog/#v2111


1.8 (2022-07-07)
~~~~~~~~~~~~~~~~

* Fix `problem on macOS`__ explicitly requiring C++11, thanks to agate-pris (`issue
  #166`__)

  __ https://github.com/Tencent/rapidjson/commit/9965ab37f6cfae3d58a0a6e34c76112866ace0b1#commitcomment-77875054
  __ https://github.com/python-rapidjson/python-rapidjson/issues/166


1.7 (2022-07-06)
~~~~~~~~~~~~~~~~

* Use `current master`__ version of rapidjson

  __ https://github.com/Tencent/rapidjson/commit/232389d4f1012dddec4ef84861face2d2ba85709

* Update the test suite to work on Pyston, thanks to Kevin Modzelewski (`PR #161`__)

  __ https://github.com/python-rapidjson/python-rapidjson/pull/161


1.6 (2022-02-19)
~~~~~~~~~~~~~~~~

* Fix memory leak when using ``end_array`` (`issue #160`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/160


1.5 (2021-10-16)
~~~~~~~~~~~~~~~~

* Fix serialization bug when using DM_UNIX_TIME in a non-C locale context


1.4 (2021-06-25)
~~~~~~~~~~~~~~~~

* Build binary wheel for aarch64, thanks to odidev (`PR #156`__)

  __ https://github.com/python-rapidjson/python-rapidjson/pull/156


1.3 (2021-06-25)
~~~~~~~~~~~~~~~~

* Yet another attempt to fix automatic wheels upload


1.2 (2021-06-25)
~~~~~~~~~~~~~~~~

* Fix automatic wheels upload from GH Actions to PyPI


1.1 (2021-06-25)
~~~~~~~~~~~~~~~~

* Reduce decoder memory consumption by uniquifiying keys in the loaded dictionaries

* Implement an alternative way of transmogrify JSON objects, similar to ``json``\ 's
  ``object_pairs_hook`` load option (`issue #154`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/154


1.0 (2020-12-13)
~~~~~~~~~~~~~~~~

* Require Python 3.6 or greater

* New serialization options, ``iterable_mode`` and ``mapping_mode``, to give some control
  on how generic iterables and mappings get encoded (fix `issue #149`__ and
  `issue #150`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/149
  __ https://github.com/python-rapidjson/python-rapidjson/issues/150

* Internal refactorings, folding "skipkeys" and "sort_keys" arguments into the
  mapping_mode options, respectively as MM_SKIP_NON_STRING_KEYS and MM_SORT_KEYS: "old"
  arguments kept for backward compatibility

* Bump major version to 1, tag as "production/stable" and switch to a simpler X.Y
  versioning schema


0.9.4 (2020-11-16)
~~~~~~~~~~~~~~~~~~

* Fix memory leak loading an invalid JSON (`issue #148`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/148


0.9.3 (2020-10-24)
~~~~~~~~~~~~~~~~~~

* Fix access to ``Encoder`` instance attributes (`issue #147`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/147


0.9.2 (2020-10-24)
~~~~~~~~~~~~~~~~~~

* Use `current master`__ version of rapidjson

  __ https://github.com/Tencent/rapidjson/commit/0ccdbf364c577803e2a751f5aededce935314313

* Enable GH Actions-based test workflow, thanks to Martin Thoma (`PR #143`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/143

* Produce Python 3.9 wheels, disable testing under Python < 3.6

* Make the character used for indentation in pretty mode a parameter (`issue #135`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/135

* Handle wider precision range in timestamps fractional seconds (`PR 133`__), thanks to
  Karl Seguin

  __ https://github.com/python-rapidjson/python-rapidjson/pull/133

* Add comparison benchmarks against orjson and hyperjson (`issue #130`__ and `PR #131`__,
  thanks to Sebastian Pipping)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/130
  __ https://github.com/python-rapidjson/python-rapidjson/pull/131


0.9.1 (2019-11-13)
~~~~~~~~~~~~~~~~~~

* Fix memory leak in case of failed validation (`issue #126`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/126


0.9.0 (2019-11-13)
~~~~~~~~~~~~~~~~~~

* Produce Python 3.8 wheels

* Compatibility fix for Python 3.8 (`issue #125`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/125

* New dump option ``write_mode``, supporting RapidJSON's ``kFormatSingleLineArray`` option
  (`issue #123`__), thanks to Nguyễn Hồng Quân for the initial implementation (`PR #124`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/123
  __ https://github.com/python-rapidjson/python-rapidjson/pull/124


0.8.0 (2019-08-09)
~~~~~~~~~~~~~~~~~~

* New serialization option ``bytes_mode`` to control how bytes instances get encoded
  (`issue #122`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/122


0.7.2 (2019-06-09)
~~~~~~~~~~~~~~~~~~

* Hopefully fix the memory leak when loading from a stream (`issue #117`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/117


0.7.1 (2019-05-11)
~~~~~~~~~~~~~~~~~~

* Raise a more specific exception on loading errors, ``JSONDecodeError``, instead of
  generic ``ValueError`` (`issue #118`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/118

* Fix optimization path when using ``OrderedDict``\ s (`issue #119`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/119

* Fix serialization of ``IntEnum``\ s (`issue #121`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/121

* I spent *quite a lot* of time investigating on the memory leak when loading from a
  stream (`issue #117`__): as I was not able to fully replicate the problem, I cannot be
  sure I solved the problem... sorry!

  __ https://github.com/python-rapidjson/python-rapidjson/issues/117


0.7.0 (2019-02-11)
~~~~~~~~~~~~~~~~~~

* Raise correct exception in code samples (`PR #109`__), thanks to Thomas Dähling

  __ https://github.com/python-rapidjson/python-rapidjson/pull/109

* Fix compilation with system-wide install of rapidjson (`issue #110`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/110

* Use current master version of rapidjson, that includes a `fix`__ for its `issue #1368`__
  and `issue #1336`__, and cures several compilation warnings as well (`issue #112`__ and
  `issue #107`__)

  __ https://github.com/Tencent/rapidjson/commit/f5e5d47fac0f654749c4d6267015005b74643dff
  __ https://github.com/Tencent/rapidjson/issues/1368
  __ https://github.com/Tencent/rapidjson/issues/1336
  __ https://github.com/python-rapidjson/python-rapidjson/issues/112
  __ https://github.com/python-rapidjson/python-rapidjson/issues/107

* Fix memory leak when using ``object_hook`` (`issue #115`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/115


0.6.3 (2018-07-11)
~~~~~~~~~~~~~~~~~~

* No visible changes, but now PyPI carries binary wheels for Python 3.7.


0.6.2 (2018-06-08)
~~~~~~~~~~~~~~~~~~

* Use a more specific ValidationError, to differentiate from invalid JSON


0.6.1 (2018-06-06)
~~~~~~~~~~~~~~~~~~

* Nothing new, attempt to build Python 3.6 binary wheels on Travis CI


0.6.0 (2018-06-06)
~~~~~~~~~~~~~~~~~~

* Add a new comparison table involving ``ensure_ascii`` (`issue #98`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/98

* Use Python's ``repr()`` to emit float values instead of rapidjson's ``dtoa()`` (`issue
  #101`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/101

* Use a newer (although unreleased) version of rapidjson to fix an `issue`__ with
  JSONSchema validation (`PR #103`__), thanks to Anthony Miyaguchi

  __ https://github.com/Tencent/rapidjson/issues/825
  __ https://github.com/python-rapidjson/python-rapidjson/pull/103


0.5.2 (2018-03-31)
~~~~~~~~~~~~~~~~~~

* Tiny tweak to restore macOS build on Travis CI


0.5.1 (2018-03-31)
~~~~~~~~~~~~~~~~~~

* Minor tweaks to CI and PyPI deploy configuration


0.5.0 (2018-03-31)
~~~~~~~~~~~~~~~~~~

* New ``RawJSON`` class, allowing inclusion of *pre-serialized* content (`PR #95`__ and
  `PR #96`__), thanks to Silvio Tomatis

  __ https://github.com/python-rapidjson/python-rapidjson/pull/95
  __ https://github.com/python-rapidjson/python-rapidjson/pull/96


0.4.3 (2018-01-14)
~~~~~~~~~~~~~~~~~~

* Deserialize from ``bytes`` and ``bytearray`` instances, ensuring they
  contain valid UTF-8 data

* Speed up parsing of floating point numbers, avoiding intermediary conversion
  to a Python string (`PR #94`__)

  __ https://github.com/python-rapidjson/python-rapidjson/pull/94


0.4.2 (2018-01-09)
~~~~~~~~~~~~~~~~~~

* Fix precision handling of DM_UNIX_TIME timestamps


0.4.1 (2018-01-08)
~~~~~~~~~~~~~~~~~~

* Fix memory leaks in ``Decoder()`` and ``Encoder()`` classes, related to
  bad handling of ``PyObject_GetAttr()`` result value

* Fix compatibility with Python 3.7a


0.4.0 (2018-01-05)
~~~~~~~~~~~~~~~~~~

* Implemented the streaming interface, see `load()`__ and `dump()`__ (`issue #80`__)

  __ https://python-rapidjson.readthedocs.io/en/latest/load.html
  __ https://python-rapidjson.readthedocs.io/en/latest/dump.html
  __ https://github.com/python-rapidjson/python-rapidjson/issues/80

  **Backward incompatibility**: now the *flags* arguments on all the functions are
  *keyword only*, to mimic stdlib's ``json`` style


0.3.2 (2017-12-21)
~~~~~~~~~~~~~~~~~~

* Reduce compiler warnings (`issue #87`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/87


0.3.1 (2017-12-20)
~~~~~~~~~~~~~~~~~~

* Fix Travis CI recipe to accomodate MacOS


0.3.0 (2017-12-20)
~~~~~~~~~~~~~~~~~~

* Fix compilation on MacOS (`issue #78`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/78

* Handle generic iterables (`PR #89`__)

  __ https://github.com/python-rapidjson/python-rapidjson/pull/89

  **Backward incompatibility**: the ``dumps()`` function and the ``Encoder()``
  constructor used to accept a ``max_recursion_depth`` argument, to control
  the maximum allowed nesting of Python structures; since the underlying
  function is now effectively recursive, it has been replaced by the generic
  `sys.setrecursionlimit()`__ mechanism

  __ https://docs.python.org/3.6/library/sys.html#sys.setrecursionlimit


0.2.7 (2017-12-08)
~~~~~~~~~~~~~~~~~~

* Restore compatibility with Python < 3.6


0.2.6 (2017-12-08)
~~~~~~~~~~~~~~~~~~

* Fix memory leaks when using object_hook/start_object/end_object


0.2.5 (2017-09-30)
~~~~~~~~~~~~~~~~~~

* Fix bug where error handling code could raise an exception causing a
  confusing exception to be returned (`PR #82`__)

  __ https://github.com/python-rapidjson/python-rapidjson/pull/82

* Fix bug where loads's ``object_hook`` and dumps's ``default`` arguments
  could not be passed ``None`` explicitly (`PR #83`__)

  __ https://github.com/python-rapidjson/python-rapidjson/pull/83

* Fix crash when dealing with surrogate pairs (`issue #81`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/81


0.2.4 (2017-09-17)
~~~~~~~~~~~~~~~~~~

* Fix compatibility with MacOS/clang


0.2.3 (2017-08-24)
~~~~~~~~~~~~~~~~~~

* Limit the precision of DM_UNIX_TIME timestamps to six decimal digits


0.2.2 (2017-08-24)
~~~~~~~~~~~~~~~~~~

* Nothing new, attempt to fix production of Python 3.6 binary wheels


0.2.1 (2017-08-24)
~~~~~~~~~~~~~~~~~~

* Nothing new, attempt to fix production of Python 3.6 binary wheels


0.2.0 (2017-08-24)
~~~~~~~~~~~~~~~~~~

* New ``parse_mode`` option, implementing relaxed JSON syntax (`issue #73`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/73

* New ``Encoder`` and ``Decoder``, implementing a class-based interface

* New ``Validator``, exposing the underlying *JSON schema* validation (`issue #71`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/71


0.1.0 (2017-08-16)
~~~~~~~~~~~~~~~~~~

* Remove beta status


0.1.0b4 (2017-08-14)
~~~~~~~~~~~~~~~~~~~~

* Make execution of the test suite on Appveyor actually happen


0.1.0b3 (2017-08-12)
~~~~~~~~~~~~~~~~~~~~

* Exclude CI configurations from the source distribution


0.1.0b2 (2017-08-12)
~~~~~~~~~~~~~~~~~~~~

* Fix Powershell wheel upload script in appveyor configuration


0.1.0b1 (2017-08-12)
~~~~~~~~~~~~~~~~~~~~

* Compilable with somewhat old g++ (`issue #69`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/69

* **Backward incompatibilities**:

  - all ``DATETIME_MODE_XXX`` constants have been shortened to ``DM_XXX``
    ``DATETIME_MODE_ISO8601_UTC`` has been renamed to ``DM_SHIFT_TO_UTC``

  - all ``UUID_MODE_XXX`` constants have been shortened to ``UM_XXX``

* New option ``DM_UNIX_TIME`` to serialize date, datetime and time values as
  `UNIX timestamps`__ targeting `issue #61`__

  __ https://en.wikipedia.org/wiki/Unix_time
  __ https://github.com/python-rapidjson/python-rapidjson/issues/61

* New option ``DM_NAIVE_IS_UTC`` to treat naïve datetime and time values as if
  they were in the UTC timezone (also for issue #61)

* New keyword argument ``number_mode`` to use underlying C library numbers

* Binary wheels for GNU/Linux and Windows on PyPI (one would hope: this is the
  reason for the beta1 release)


0.0.11 (2017-03-05)
~~~~~~~~~~~~~~~~~~~

* Fix a couple of refcount handling glitches, hopefully targeting `issue
  #48`__.

  __ https://github.com/python-rapidjson/python-rapidjson/issues/48


0.0.10 (2017-03-02)
~~~~~~~~~~~~~~~~~~~

* Fix source distribution to contain all required stuff (`PR #64`__)

  __ https://github.com/python-rapidjson/python-rapidjson/pull/64


0.0.9 (2017-03-02)
~~~~~~~~~~~~~~~~~~

* CI testing on GitHub

* Allow using locally installed RapidJSON library (`issue #60`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/60

* Bug fixes (`issue #37`__, `issue #51`__, `issue #57`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/37
  __ https://github.com/python-rapidjson/python-rapidjson/issues/51
  __ https://github.com/python-rapidjson/python-rapidjson/issues/57


0.0.8 (2016-12-09)
~~~~~~~~~~~~~~~~~~

* Use unpatched RapidJSON 1.1 (`PR #46`__)

  __ https://github.com/python-rapidjson/python-rapidjson/pull/46

* Handle serialization and deserialization of datetime, date and time
  instances (`PR #35`__) and of UUID instances (`PR #40`__)

  __ https://github.com/python-rapidjson/python-rapidjson/pull/35
  __ https://github.com/python-rapidjson/python-rapidjson/pull/40

* Sphinx based documentation (`PR #44`__)

  __ https://github.com/python-rapidjson/python-rapidjson/pull/44

* Refresh benchmarks (`PR #45`__)

  __ https://github.com/python-rapidjson/python-rapidjson/pull/45

* Bug fixes (`issue #25`__, `issue #38`__, `PR #43`__)

  __ https://github.com/python-rapidjson/python-rapidjson/issues/25
  __ https://github.com/python-rapidjson/python-rapidjson/issues/38
  __ https://github.com/python-rapidjson/python-rapidjson/pull/43
