2025-06-06 10:10:04 | INFO     | [SYSTEM](NO_SESSI) | 3d_ai_studio.app | System startup | Data: {"platform": "Windows-11-10.0.26100-SP0", "python_version": "3.12.10", "cpu_count": 88, "memory_total": "79.9 GB", "disk_free": "530.0 GB", "torch_version": "2.5.1+cu124", "cuda_available": true, "cuda_device": "NVIDIA GeForce RTX 3060", "cuda_memory": "12.0 GB"}
2025-06-06 10:10:04 | INFO     | [[STARTUP]]((NO_SESS) | 3d_ai_studio.app | 3D AI Studio starting up
2025-06-06 10:10:16 | INFO     | [[HUNYUAN3D_DIRECT]]((NO_SESS) | 3d_ai_studio.app | Initial setup status - Compilation: True, Models: True
2025-06-06 10:13:50 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Hunyuan3D-2 environment setup completed
2025-06-06 10:14:44 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Loading Hunyuan3D-2 pipeline components...
2025-06-06 10:14:44 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Hunyuan3D-2 environment setup completed
2025-06-06 10:14:46 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Background removal worker loaded
2025-06-06 10:14:46 | ERROR    | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Error loading Hunyuan3D-2 pipeline components: No module named 'mmgp'
2025-06-06 10:14:46 | ERROR    | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Traceback: Traceback (most recent call last):
  File "E:\3D AI Studio\hunyuan3d_integrated_pipeline.py", line 128, in load_pipeline_components
    from hy3dgen.shapegen import (
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\__init__.py", line 15, in <module>
    from .pipelines import Hunyuan3DDiTPipeline, Hunyuan3DDiTFlowMatchingPipeline
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\pipelines.py", line 30, in <module>
    from .models.autoencoders import ShapeVAE
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\models\__init__.py", line 26, in <module>
    from .autoencoders import ShapeVAE
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\models\autoencoders\__init__.py", line 18, in <module>
    from .model import ShapeVAE, VectsetVAE
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\models\autoencoders\model.py", line 22, in <module>
    from .surface_extractors import MCSurfaceExtractor, SurfaceExtractors
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\models\autoencoders\surface_extractors.py", line 79, in <module>
    from mmgp import offload
ModuleNotFoundError: No module named 'mmgp'

2025-06-06 10:35:21 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Hunyuan3D-2 environment setup completed
2025-06-06 10:35:39 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Loading Hunyuan3D-2 pipeline components...
2025-06-06 10:35:39 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Hunyuan3D-2 environment setup completed
2025-06-06 10:35:40 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Background removal worker loaded
2025-06-06 10:35:56 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Shape generation pipeline loaded
2025-06-06 10:35:56 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Mesh processing workers loaded
2025-06-06 10:35:56 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Loading texture generation models (this may take 3-5 minutes)...
2025-06-06 10:35:56 | WARNING  | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Failed to load texture generation pipeline: Model path Resources\Hunyuan3D2_WinPortable\HuggingFaceHub\models--tencent--Hunyuan3D-2\snapshots\34e28261f71c32975727be8db0eace439a280f82 not found
2025-06-06 10:35:56 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Texture generation will be unavailable (shape generation still works)
2025-06-06 10:39:54 | WARNING  | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Failed to load text-to-image pipeline: 'SafeTensorFile' object has no attribute 'get_slice'
2025-06-06 10:39:54 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | Text-to-image will be unavailable (image-to-3D still works)
2025-06-06 10:39:54 | INFO     | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.app | All Hunyuan3D-2 pipeline components loaded successfully
