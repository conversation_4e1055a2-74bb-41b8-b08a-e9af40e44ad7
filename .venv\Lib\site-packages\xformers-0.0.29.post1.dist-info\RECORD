xformers-0.0.29.post1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
xformers-0.0.29.post1.dist-info/LICENSE,sha256=xPdISGPi2k__n6EMnda0mDEa6uov8po3MbHeQX3dhCI,1645
xformers-0.0.29.post1.dist-info/METADATA,sha256=SU1R9LsOmHG9zPoksdkRulAoCbs5Ju82q6LfIZZkd-c,1035
xformers-0.0.29.post1.dist-info/RECORD,,
xformers-0.0.29.post1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xformers-0.0.29.post1.dist-info/WHEEL,sha256=pWXrJbnZSH-J-PhYmKs2XNn4DHCPNBYq965vsBJBFvA,101
xformers-0.0.29.post1.dist-info/top_level.txt,sha256=4Px1VcGhKk0j3XhKXjA8HtTm6EQOb0hazeJ5nQsNlKk,9
xformers/_C.pyd,sha256=_UsvFVbOkGb4XBVYBE2HLo_xbplACSXU8rj0fDY9kI8,19640320
xformers/_C_flashattention.pyd,sha256=Z4HfRilYL906_VkRdNBCX2OD0KQM7ihzgC3L5Gb7MY0,432479744
xformers/__init__.py,sha256=6Gn6UY8d4ij1NHxUkzc84pos2phtISO3FRh1q5Mnsaw,1783
xformers/__pycache__/__init__.cpython-312.pyc,,
xformers/__pycache__/_cpp_lib.cpython-312.pyc,,
xformers/__pycache__/_deprecation_warning.cpython-312.pyc,,
xformers/__pycache__/attn_bias_utils.cpython-312.pyc,,
xformers/__pycache__/checkpoint.cpython-312.pyc,,
xformers/__pycache__/info.cpython-312.pyc,,
xformers/__pycache__/test.cpython-312.pyc,,
xformers/__pycache__/utils.cpython-312.pyc,,
xformers/__pycache__/version.cpython-312.pyc,,
xformers/_cpp_lib.py,sha256=krwmMrZN5GR1Hm-wkOGZQ0fn8gqYz3Wd-RS_ISPBpgA,5113
xformers/_deprecation_warning.py,sha256=oASW0P7S8aHENqyPNRkgReNZimZZKLYlLxFF_GxRUX8,468
xformers/_flash_attn/__init__.py,sha256=vZ1z743tJCEdRom8-oqq9xjebvMarZhZvapdsACQ2jM,302
xformers/_flash_attn/__pycache__/__init__.cpython-312.pyc,,
xformers/_flash_attn/__pycache__/bert_padding.cpython-312.pyc,,
xformers/_flash_attn/__pycache__/flash_attn_interface.cpython-312.pyc,,
xformers/_flash_attn/__pycache__/flash_attn_triton.cpython-312.pyc,,
xformers/_flash_attn/__pycache__/flash_attn_triton_og.cpython-312.pyc,,
xformers/_flash_attn/__pycache__/flash_blocksparse_attention.cpython-312.pyc,,
xformers/_flash_attn/__pycache__/flash_blocksparse_attn_interface.cpython-312.pyc,,
xformers/_flash_attn/__pycache__/fused_softmax.cpython-312.pyc,,
xformers/_flash_attn/bert_padding.py,sha256=dhN4U8f_JqPU2Qzo4fHNkYqThe3TGalR1eg4GEqlWe8,10148
xformers/_flash_attn/flash_attn_interface.py,sha256=_m7AtzwCgJDfrarL2rnyCsBTkopQurUlCGqYx_oGgz0,60972
xformers/_flash_attn/flash_attn_triton.py,sha256=W2NIPsie2qdcCfRcOcEqks8swWQFYgVOigfoOyFHq_4,42272
xformers/_flash_attn/flash_attn_triton_amd/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xformers/_flash_attn/flash_attn_triton_amd/__pycache__/__init__.cpython-312.pyc,,
xformers/_flash_attn/flash_attn_triton_amd/__pycache__/bench.cpython-312.pyc,,
xformers/_flash_attn/flash_attn_triton_amd/__pycache__/bwd_prefill.cpython-312.pyc,,
xformers/_flash_attn/flash_attn_triton_amd/__pycache__/bwd_ref.cpython-312.pyc,,
xformers/_flash_attn/flash_attn_triton_amd/__pycache__/fwd_decode.cpython-312.pyc,,
xformers/_flash_attn/flash_attn_triton_amd/__pycache__/fwd_prefill.cpython-312.pyc,,
xformers/_flash_attn/flash_attn_triton_amd/__pycache__/fwd_ref.cpython-312.pyc,,
xformers/_flash_attn/flash_attn_triton_amd/__pycache__/interface_fa.cpython-312.pyc,,
xformers/_flash_attn/flash_attn_triton_amd/__pycache__/interface_torch.cpython-312.pyc,,
xformers/_flash_attn/flash_attn_triton_amd/__pycache__/test.cpython-312.pyc,,
xformers/_flash_attn/flash_attn_triton_amd/__pycache__/utils.cpython-312.pyc,,
xformers/_flash_attn/flash_attn_triton_amd/bench.py,sha256=UKDUpBapU9hlIVXB7ZyynDCak2EpUaHTWnGlkHmw1qA,10127
xformers/_flash_attn/flash_attn_triton_amd/bwd_prefill.py,sha256=PR4-d5Nk2Rn_iiwmcB0uqZzA57-D78t-24PDNHqfoug,20960
xformers/_flash_attn/flash_attn_triton_amd/bwd_ref.py,sha256=nMsbyl8JFOWZx5ZtdUsbTyKWrFLbI32gHYVmWf3kx78,10300
xformers/_flash_attn/flash_attn_triton_amd/fwd_decode.py,sha256=pd75r0qeyn1rHie2Y0nYs0ndOMwK8Xm5pDhdTpT4Y-M,24138
xformers/_flash_attn/flash_attn_triton_amd/fwd_prefill.py,sha256=-PoamI4_4Is34pIWw4fryHmJ8C-2GDlXG2tmcj6rOEs,33638
xformers/_flash_attn/flash_attn_triton_amd/fwd_ref.py,sha256=Ltr7Jj5VsrQzm97VskyxcoKPJzT5FWprASrn6ShTfJo,11680
xformers/_flash_attn/flash_attn_triton_amd/interface_fa.py,sha256=OXUuUieT0YEaxQgsEPrAZj7Krr2ADlZJEA9ISqGOl3w,16827
xformers/_flash_attn/flash_attn_triton_amd/interface_torch.py,sha256=f5rC3AhOsCWkuiVe2SZ5ebo7Y2Gpu-OXQSZppB8ih3M,3405
xformers/_flash_attn/flash_attn_triton_amd/test.py,sha256=yoy0lB4ZH9vssWu_iMR2T7YayZU3m3y_W_cvOdIb2Ng,31556
xformers/_flash_attn/flash_attn_triton_amd/utils.py,sha256=cbCB0YK1pVZphlrQH35lhOUeglVTeNYVUrn0VhKpLHE,12521
xformers/_flash_attn/flash_attn_triton_og.py,sha256=LZm4Jlz0ECHtuOWEnpe69_iqUrNfNqGYT6AI5D4X5Qk,11693
xformers/_flash_attn/flash_blocksparse_attention.py,sha256=BvOsy6cS105Iijgmi0DgXl7-1PjUZoDxcMDztXvlyiA,7669
xformers/_flash_attn/flash_blocksparse_attn_interface.py,sha256=3z54--DCBdcS7cqF0oiC1Ux53Ye8o-TwbdSgdGJSea0,7465
xformers/_flash_attn/fused_softmax.py,sha256=-ZMBHj_1CjfOOZwsP9D1w1CZstUyRUVONUhz_rD5cAE,7994
xformers/_flash_attn/layers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xformers/_flash_attn/layers/__pycache__/__init__.cpython-312.pyc,,
xformers/_flash_attn/layers/__pycache__/patch_embed.cpython-312.pyc,,
xformers/_flash_attn/layers/__pycache__/rotary.cpython-312.pyc,,
xformers/_flash_attn/layers/patch_embed.py,sha256=_2b237fpHQa2Q6lggjVHKllo1I7ofNju7ZugWlZieqQ,2203
xformers/_flash_attn/layers/rotary.py,sha256=_gzEmWqc3mlieLG9NND06igqdNkLwJ5321R_AmmUJKU,21767
xformers/_flash_attn/losses/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xformers/_flash_attn/losses/__pycache__/__init__.cpython-312.pyc,,
xformers/_flash_attn/losses/__pycache__/cross_entropy.cpython-312.pyc,,
xformers/_flash_attn/losses/cross_entropy.py,sha256=iOQYdYubepYe7CBenfgvFvdqAi_1kU1WGJmMauldT-I,3282
xformers/_flash_attn/models/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xformers/_flash_attn/models/__pycache__/__init__.cpython-312.pyc,,
xformers/_flash_attn/models/__pycache__/baichuan.cpython-312.pyc,,
xformers/_flash_attn/models/__pycache__/bert.cpython-312.pyc,,
xformers/_flash_attn/models/__pycache__/bigcode.cpython-312.pyc,,
xformers/_flash_attn/models/__pycache__/btlm.cpython-312.pyc,,
xformers/_flash_attn/models/__pycache__/falcon.cpython-312.pyc,,
xformers/_flash_attn/models/__pycache__/gpt.cpython-312.pyc,,
xformers/_flash_attn/models/__pycache__/gpt_neox.cpython-312.pyc,,
xformers/_flash_attn/models/__pycache__/gptj.cpython-312.pyc,,
xformers/_flash_attn/models/__pycache__/llama.cpython-312.pyc,,
xformers/_flash_attn/models/__pycache__/opt.cpython-312.pyc,,
xformers/_flash_attn/models/__pycache__/vit.cpython-312.pyc,,
xformers/_flash_attn/models/baichuan.py,sha256=qAyur0jJBkdzNlNwFLgHgDpZwgBVngDXcmVFDTV2gVA,5881
xformers/_flash_attn/models/bert.py,sha256=BCImImz4Zeg6bCH60LLoWlHig5Ha5SDYAOqd3HYHEbo,33996
xformers/_flash_attn/models/bigcode.py,sha256=D16KsDAurcLs6Spw2WUpzY6L03r-dv9735tTT-Y98js,9616
xformers/_flash_attn/models/btlm.py,sha256=ojSk0O5mezLfbUJC5b-es3ol__rEdpNUM2Hlu63tEfc,4733
xformers/_flash_attn/models/falcon.py,sha256=Z8eFr6U7BaAOX0cElGRX9W-nZdgRVRI9NtSf5A3kT6Q,6176
xformers/_flash_attn/models/gpt.py,sha256=3up3lDM0LrUtEZUpyou6klZrvLOWabTXgq7pgAVpcMM,48749
xformers/_flash_attn/models/gpt_neox.py,sha256=JnfIppqL6neut6OrcOwZy6QueRsD34T9d5afxoP0isM,5283
xformers/_flash_attn/models/gptj.py,sha256=mrJcKwuYQk6mGsFsV2-HA3Db79KP0LCbyIb3vnS9jbc,4545
xformers/_flash_attn/models/llama.py,sha256=Gu-fr9ltOFxKgUTCDHpgwNpTpFeA_d6MGzYT3jyG394,17003
xformers/_flash_attn/models/opt.py,sha256=w6LSHfxDBPC1d_CIyNUDAU1KifeynDh4WuipbYkUhDA,5280
xformers/_flash_attn/models/vit.py,sha256=L9AE7hiJo_HVHfMtNEK2JbkTnwQyhDy3PCih4NSJQio,14447
xformers/_flash_attn/modules/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xformers/_flash_attn/modules/__pycache__/__init__.cpython-312.pyc,,
xformers/_flash_attn/modules/__pycache__/block.cpython-312.pyc,,
xformers/_flash_attn/modules/__pycache__/embedding.cpython-312.pyc,,
xformers/_flash_attn/modules/__pycache__/mha.cpython-312.pyc,,
xformers/_flash_attn/modules/__pycache__/mlp.cpython-312.pyc,,
xformers/_flash_attn/modules/block.py,sha256=91FIlNAF8rBQlZOHWzlUwGsD-7jzQUl70ukwhmxukoY,17746
xformers/_flash_attn/modules/embedding.py,sha256=3U2vTsd7aQXfD2wiDLGvEKhfVyw3kC7dtfxbnh1P6BY,8909
xformers/_flash_attn/modules/mha.py,sha256=zcvt45Xal19vK2qdw2HSYqCRCagK3SGhBMzJL3X4Jnk,44317
xformers/_flash_attn/modules/mlp.py,sha256=ThtP6EiTA78xc1paV1OkHi4_c-em6VttFECxghi1d9Y,6224
xformers/_flash_attn/ops/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xformers/_flash_attn/ops/__pycache__/__init__.cpython-312.pyc,,
xformers/_flash_attn/ops/__pycache__/activations.cpython-312.pyc,,
xformers/_flash_attn/ops/__pycache__/fused_dense.cpython-312.pyc,,
xformers/_flash_attn/ops/__pycache__/layer_norm.cpython-312.pyc,,
xformers/_flash_attn/ops/__pycache__/rms_norm.cpython-312.pyc,,
xformers/_flash_attn/ops/activations.py,sha256=jNBYjUjasYQCnG7oYea4xSfsrVdsevpVlWl_FoI6cFg,4074
xformers/_flash_attn/ops/fused_dense.py,sha256=IQL8zidCCObh1B_2tLPoPhErYWSTfjIyTnwc7xafa8c,28595
xformers/_flash_attn/ops/layer_norm.py,sha256=re3-HG7fv-qeZtehElszHG5sQKgH17GmSvX1bJmsPcw,23243
xformers/_flash_attn/ops/rms_norm.py,sha256=0YbzNABBn31R_7asugdJCFUzXZjvohk1XYkkPKeZ0_U,4162
xformers/_flash_attn/ops/triton/__init__.py,sha256=frcCV1k9oG9oKj3dpUqdJg1PxRT2RSN_XKdLCPjaYaY,2
xformers/_flash_attn/ops/triton/__pycache__/__init__.cpython-312.pyc,,
xformers/_flash_attn/ops/triton/__pycache__/cross_entropy.cpython-312.pyc,,
xformers/_flash_attn/ops/triton/__pycache__/k_activations.cpython-312.pyc,,
xformers/_flash_attn/ops/triton/__pycache__/layer_norm.cpython-312.pyc,,
xformers/_flash_attn/ops/triton/__pycache__/linear.cpython-312.pyc,,
xformers/_flash_attn/ops/triton/__pycache__/mlp.cpython-312.pyc,,
xformers/_flash_attn/ops/triton/__pycache__/rotary.cpython-312.pyc,,
xformers/_flash_attn/ops/triton/cross_entropy.py,sha256=7zcuiJPAUiRkEgwK6HDhBJdb_9PTNYWYnM_OgexdguI,13174
xformers/_flash_attn/ops/triton/k_activations.py,sha256=EXip4m6AwLI4f3Go1b3WRLg32RepR70SU_uBZCl_4co,4196
xformers/_flash_attn/ops/triton/layer_norm.py,sha256=96Y-j80UFVsAl-nZWJqU1t93CRhlF4Ejb13Nw47eLkQ,36827
xformers/_flash_attn/ops/triton/linear.py,sha256=hyB5-xYqH0cKtPGq26bqzCZJxAPAkN6M8QorwcVFzKs,21435
xformers/_flash_attn/ops/triton/mlp.py,sha256=9U7E7QT9og5J7kQkSSxFMuBM5FUM-V18n1wOsaNfQL0,6217
xformers/_flash_attn/ops/triton/rotary.py,sha256=IvcaAJ7YSIybbNY_tCdGUvp4vFPhpKbdKidJ1rjYJRw,8810
xformers/_flash_attn/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xformers/_flash_attn/utils/__pycache__/__init__.cpython-312.pyc,,
xformers/_flash_attn/utils/__pycache__/benchmark.cpython-312.pyc,,
xformers/_flash_attn/utils/__pycache__/distributed.cpython-312.pyc,,
xformers/_flash_attn/utils/__pycache__/generation.cpython-312.pyc,,
xformers/_flash_attn/utils/__pycache__/pretrained.cpython-312.pyc,,
xformers/_flash_attn/utils/benchmark.py,sha256=jU-SDghUtkLVtDNnetTAOWFnK1tsi5um1ua3rPoQBtk,7637
xformers/_flash_attn/utils/distributed.py,sha256=1KKwHrmoAjlGA2OD3O5ntO5LmoT34Xq3X9xkxX8X1Wg,5969
xformers/_flash_attn/utils/generation.py,sha256=LEg7sbmdeqjazwbi2dfEay9_VpMSF5TRBAzvKYSX1wM,31434
xformers/_flash_attn/utils/pretrained.py,sha256=z3aA3mwarpBSRVpXZX8y8bxNFqwzGeON0k14TvZDYIU,3325
xformers/attn_bias_utils.py,sha256=MMkD3B13F1gq2Aym1RMbSgNa2sAQYFPqIOBm5lPqivs,19679
xformers/benchmarks/LRA/__init__.py,sha256=M4NStf3p8zmHeaRF4kDPx3J4tJv4zraCm5oYv6rp7Lc,202
xformers/benchmarks/LRA/__pycache__/__init__.cpython-312.pyc,,
xformers/benchmarks/LRA/__pycache__/batch_fetch_results.cpython-312.pyc,,
xformers/benchmarks/LRA/__pycache__/batch_submit.cpython-312.pyc,,
xformers/benchmarks/LRA/__pycache__/run_grid_search.cpython-312.pyc,,
xformers/benchmarks/LRA/__pycache__/run_tasks.cpython-312.pyc,,
xformers/benchmarks/LRA/__pycache__/run_with_submitit.cpython-312.pyc,,
xformers/benchmarks/LRA/batch_fetch_results.py,sha256=P8_GIzJEONssaMGTnOhT-sYYga20lZxiIW5Bvx8ufB4,3604
xformers/benchmarks/LRA/batch_submit.py,sha256=spwxdzpH43ixNJSma-ZauJXXj1AHf5nrkqnDwgQaYa8,1734
xformers/benchmarks/LRA/code/__init__.py,sha256=M4NStf3p8zmHeaRF4kDPx3J4tJv4zraCm5oYv6rp7Lc,202
xformers/benchmarks/LRA/code/__pycache__/__init__.cpython-312.pyc,,
xformers/benchmarks/LRA/code/__pycache__/dataset.cpython-312.pyc,,
xformers/benchmarks/LRA/code/__pycache__/model_wrapper.cpython-312.pyc,,
xformers/benchmarks/LRA/code/dataset.py,sha256=GxlIXt0cdpZJ-mA1pAnaOuSBoEpu4sLi3C6lxoK7FCQ,1449
xformers/benchmarks/LRA/code/model_wrapper.py,sha256=vFfNfBh1eyJvVs2GIr7n48KImatW3t0BSbHDrzGLICs,10065
xformers/benchmarks/LRA/run_grid_search.py,sha256=bZZoLui4Zor8WV1eryaaHAUqSjgv3b9AFJwO5E5lHbU,5475
xformers/benchmarks/LRA/run_tasks.py,sha256=EqS616CALsfePWEE4THC3NjgZ1ZJpXHoJoY-pktlaWE,9596
xformers/benchmarks/LRA/run_with_submitit.py,sha256=M8YM_dfcC5roD6X0KtWvin0WRAA_VKJOn95FweoTGr8,4765
xformers/benchmarks/__init__.py,sha256=M4NStf3p8zmHeaRF4kDPx3J4tJv4zraCm5oYv6rp7Lc,202
xformers/benchmarks/__pycache__/__init__.cpython-312.pyc,,
xformers/benchmarks/__pycache__/benchmark_attn_decoding.cpython-312.pyc,,
xformers/benchmarks/__pycache__/benchmark_core.cpython-312.pyc,,
xformers/benchmarks/__pycache__/benchmark_indexing.cpython-312.pyc,,
xformers/benchmarks/__pycache__/benchmark_mem_eff_attention.cpython-312.pyc,,
xformers/benchmarks/__pycache__/benchmark_merge_attentions.cpython-312.pyc,,
xformers/benchmarks/__pycache__/benchmark_nystrom_utils.cpython-312.pyc,,
xformers/benchmarks/__pycache__/benchmark_revnet.cpython-312.pyc,,
xformers/benchmarks/__pycache__/benchmark_sddmm.cpython-312.pyc,,
xformers/benchmarks/__pycache__/benchmark_sequence_parallel_fused.cpython-312.pyc,,
xformers/benchmarks/__pycache__/benchmark_sp24.cpython-312.pyc,,
xformers/benchmarks/__pycache__/benchmark_swiglu.cpython-312.pyc,,
xformers/benchmarks/__pycache__/benchmark_tiled_matmul.cpython-312.pyc,,
xformers/benchmarks/__pycache__/utils.cpython-312.pyc,,
xformers/benchmarks/benchmark_attn_decoding.py,sha256=S-cOE8sDe3Uvy_EkOF3N6nzUT6vDEU8cKczyskMT_Hk,12773
xformers/benchmarks/benchmark_core.py,sha256=J0-LX1kJWmjf3qLjyPvJc8gvzC4S4fin9MnIcRIL5t0,8887
xformers/benchmarks/benchmark_indexing.py,sha256=PFMhGlPq1jywUs0Ebrv7plqFkZuyGB25a4q4zm6wufQ,5479
xformers/benchmarks/benchmark_mem_eff_attention.py,sha256=CD7cvy6kBcqJ5brHtKjbS-hvHhsbDHzBXq5FAWCxOew,10474
xformers/benchmarks/benchmark_merge_attentions.py,sha256=BpNwGFYo0mTwyz03b8tCrjz5tCtqyv3QphlRqq-CQvc,3134
xformers/benchmarks/benchmark_nystrom_utils.py,sha256=G7RAbrTN84QAQM_BDuGjeCYSrKaQS3sBVdri6a4g8UQ,3331
xformers/benchmarks/benchmark_revnet.py,sha256=UU1pht9zhGZaS6V0yjyiT518VfXpt9pxRE6DdA9yvV0,2754
xformers/benchmarks/benchmark_sddmm.py,sha256=HJLov0snqIBr7hpJNIMaLKN-u3icQwIp3_qtr7Zyi2Y,3885
xformers/benchmarks/benchmark_sequence_parallel_fused.py,sha256=xhQiU1oPUnqWWlnugDP0tyEFD6XEfJYAQ6LRCKvr2oI,14927
xformers/benchmarks/benchmark_sp24.py,sha256=oBfp63HjQXROyVYhynZYt_x_lBqy7NBYOFCs1kIbXII,5039
xformers/benchmarks/benchmark_swiglu.py,sha256=pJ9gJClOE1t-TWNgJeev5CCwfAQfmhX2Z4advrh77JU,4458
xformers/benchmarks/benchmark_tiled_matmul.py,sha256=9kpIlgr9BQ_n7Xa3O6BM_h21-TinJhrgEnbeETKAwQY,3568
xformers/benchmarks/utils.py,sha256=34LEwI1SWvSBZ3u12Yv7AXbeDnJWs07LtJkAdYf3WgY,24960
xformers/checkpoint.py,sha256=FuRwOK0XLTboQ5TKKLmM72xRGSXw0iQfT0G1AKod9U8,20883
xformers/components/__init__.py,sha256=NrIJrK1v1k39eMaj5VIxNQ9OT4YSA95TMSt4gSPwWII,1034
xformers/components/__pycache__/__init__.cpython-312.pyc,,
xformers/components/__pycache__/input_projection.cpython-312.pyc,,
xformers/components/__pycache__/residual.cpython-312.pyc,,
xformers/components/attention/__init__.py,sha256=vip5YmpRAi24pRDXT5LYapznezXz6B0W6Rpn7BIjqSY,3421
xformers/components/attention/__pycache__/__init__.cpython-312.pyc,,
xformers/components/attention/__pycache__/_sputnik_sparse.cpython-312.pyc,,
xformers/components/attention/__pycache__/attention_mask.cpython-312.pyc,,
xformers/components/attention/__pycache__/attention_patterns.cpython-312.pyc,,
xformers/components/attention/__pycache__/base.cpython-312.pyc,,
xformers/components/attention/__pycache__/core.cpython-312.pyc,,
xformers/components/attention/__pycache__/fourier_mix.cpython-312.pyc,,
xformers/components/attention/__pycache__/scaled_dot_product.cpython-312.pyc,,
xformers/components/attention/__pycache__/sparsity_config.cpython-312.pyc,,
xformers/components/attention/__pycache__/utils.cpython-312.pyc,,
xformers/components/attention/_sputnik_sparse.py,sha256=LOjNGsNeagDL6_VCOafkJLrK4exsefTdrIEejm3RYds,3285
xformers/components/attention/attention_mask.py,sha256=yidod0KphKvup4oP3Y-oKQNnWfUddwDZDV5KlzAgzJI,4728
xformers/components/attention/attention_patterns.py,sha256=2pliuyA3AfM8t2QX4YPoUopP5ryUkPfRxvHo-IT5biY,10240
xformers/components/attention/base.py,sha256=ElsvRD9XftwEynk5WJfUtcS2EHBP2R9rDp9rryARkGY,3400
xformers/components/attention/core.py,sha256=E2pXmrUsr55FQq7ZCNaB16o6HxKJAvBq1z-evz8clq8,7873
xformers/components/attention/fourier_mix.py,sha256=5PLunlyAOKtfMv5Q9fOBxMKzO4d_1N0XJUkv5OgIWKI,1219
xformers/components/attention/scaled_dot_product.py,sha256=HnhYiNOghpBWucf2tW3aYVtFlbPIdUz8izCVwotsZFI,4638
xformers/components/attention/sparsity_config.py,sha256=5I77G7bv9pIY2x9qFvcHocZGmnXcN0vdx4C3XgYz8s0,42420
xformers/components/attention/utils.py,sha256=rhtgdGUKRBHBesndiJWAttw1tpaNFxf0qbGdfcWQj4U,3911
xformers/components/input_projection.py,sha256=0E8N4WePLjrOt6L6zCbthll-JtlnOCduhW_oHZeoAK4,3223
xformers/components/residual.py,sha256=-20aoEzubyQe2_DC2SFflIK9c9QXsFwmjaZbCo1WTKA,5885
xformers/cpp_lib.json,sha256=7LQ0fcmuF59tJYs4ZaST8MZyaTrdAfGvPtAcZ-fpMjg,396
xformers/info.py,sha256=AE-_vCiZ_nzOzRGMnKLJH1S5IJ_VwF-WPHoTx73AKTI,2749
xformers/ops/__init__.py,sha256=berTAUm_ilMxOaZBKVn_wWk-xC422fOC9NeVtlj43mQ,3633
xformers/ops/__pycache__/__init__.cpython-312.pyc,,
xformers/ops/__pycache__/common.cpython-312.pyc,,
xformers/ops/__pycache__/differentiable_collectives.cpython-312.pyc,,
xformers/ops/__pycache__/indexing.cpython-312.pyc,,
xformers/ops/__pycache__/ipc.cpython-312.pyc,,
xformers/ops/__pycache__/modpar_layers.cpython-312.pyc,,
xformers/ops/__pycache__/rmsnorm.cpython-312.pyc,,
xformers/ops/__pycache__/rope_padded.cpython-312.pyc,,
xformers/ops/__pycache__/seqpar.cpython-312.pyc,,
xformers/ops/__pycache__/sequence_parallel_fused_ops.cpython-312.pyc,,
xformers/ops/__pycache__/sp24.cpython-312.pyc,,
xformers/ops/__pycache__/swiglu_op.cpython-312.pyc,,
xformers/ops/__pycache__/tiled_matmul.cpython-312.pyc,,
xformers/ops/__pycache__/unbind.cpython-312.pyc,,
xformers/ops/_triton/__init__.py,sha256=33DygBWecHzeWixrEb8j3IDBuTFVaIG2Ajbmp-VH85o,760
xformers/ops/_triton/__pycache__/__init__.cpython-312.pyc,,
xformers/ops/_triton/__pycache__/k_index_select_cat.cpython-312.pyc,,
xformers/ops/_triton/__pycache__/k_scaled_index_add.cpython-312.pyc,,
xformers/ops/_triton/__pycache__/matmul_perf_model.cpython-312.pyc,,
xformers/ops/_triton/__pycache__/rmsnorm_kernels.cpython-312.pyc,,
xformers/ops/_triton/__pycache__/rope_padded_kernels.cpython-312.pyc,,
xformers/ops/_triton/__pycache__/tiled_matmul_kernels.cpython-312.pyc,,
xformers/ops/_triton/k_index_select_cat.py,sha256=_XVj_ytE-qcG7xUBAXfacJWFG9H9hQ609ar6TTXEo2Y,6378
xformers/ops/_triton/k_scaled_index_add.py,sha256=-J3qUjQsWL3G4d8h5D-Xul9ljyOfBMvEtCzw12ceq0Y,13167
xformers/ops/_triton/matmul_perf_model.py,sha256=FL02xJpfCfvmVb_F7DngUznwwBZHmwULoOZKQc8pSks,8635
xformers/ops/_triton/rmsnorm_kernels.py,sha256=X_L8SkJ4qcRXjs4YlJ-xFG7WaOxW3jbXDIfzFdOJB28,5417
xformers/ops/_triton/rope_padded_kernels.py,sha256=0-l4pHcs7DoOtSyW2IFzdTZVj3t8hY5WOy0qik_ZWfg,7575
xformers/ops/_triton/tiled_matmul_kernels.py,sha256=ZUJ1VeYS3OVmMHfUqQ0zJVIEdsiznW_OOQwye5EpP1w,14266
xformers/ops/common.py,sha256=qF9wAu-whR9_0EXHjUkstWsXYQZbdWyiXz-_Ax2hOb0,1975
xformers/ops/differentiable_collectives.py,sha256=YKyCGxTRAIAAYzgi8pQ5FugH1XoytQ1r33YGtP42hUI,5530
xformers/ops/fmha/__init__.py,sha256=M9pL7oxkszYyAccgqPmYKNdOEjeoxSfmL7LTzOAFExc,31384
xformers/ops/fmha/__pycache__/__init__.cpython-312.pyc,,
xformers/ops/fmha/__pycache__/attn_bias.cpython-312.pyc,,
xformers/ops/fmha/__pycache__/ck.cpython-312.pyc,,
xformers/ops/fmha/__pycache__/ck_decoder.cpython-312.pyc,,
xformers/ops/fmha/__pycache__/ck_splitk.cpython-312.pyc,,
xformers/ops/fmha/__pycache__/common.cpython-312.pyc,,
xformers/ops/fmha/__pycache__/cutlass.cpython-312.pyc,,
xformers/ops/fmha/__pycache__/dispatch.cpython-312.pyc,,
xformers/ops/fmha/__pycache__/flash.cpython-312.pyc,,
xformers/ops/fmha/__pycache__/flash3.cpython-312.pyc,,
xformers/ops/fmha/__pycache__/torch_attention_compat.cpython-312.pyc,,
xformers/ops/fmha/__pycache__/triton_splitk.cpython-312.pyc,,
xformers/ops/fmha/_triton/__init__.py,sha256=M4NStf3p8zmHeaRF4kDPx3J4tJv4zraCm5oYv6rp7Lc,202
xformers/ops/fmha/_triton/__pycache__/__init__.cpython-312.pyc,,
xformers/ops/fmha/_triton/__pycache__/splitk_kernels.cpython-312.pyc,,
xformers/ops/fmha/_triton/splitk_kernels.py,sha256=4n5jVocUVe1DUN6z6u3tMgwQJlSjqvBkHD4DSuIovTs,42166
xformers/ops/fmha/attn_bias.py,sha256=tHeoI75ihoTuIOOv2jSENXaQKUsO4165cKzAptSPaGQ,67311
xformers/ops/fmha/ck.py,sha256=HfI7XnIgqmYxfKiEifARvlrRGRQuW4grGZF3Qrb1mwg,17525
xformers/ops/fmha/ck_decoder.py,sha256=WjrGt2zsVcBe8ejYZ-lfzm3af5qEFlbJ_Uw-F0km9kc,5231
xformers/ops/fmha/ck_splitk.py,sha256=hyNfBWESbSp-XjWN_oDey_87PExTPE1D83jPigJMJJw,6619
xformers/ops/fmha/common.py,sha256=B3FQhleb8CGnbtLuqVN58vgO_K9-ubQgkjj9YFAAsXc,19074
xformers/ops/fmha/cutlass.py,sha256=NowXbHnH23HKZn1Q2BECnENZNYSANlmcPT10JyvF-6M,17609
xformers/ops/fmha/dispatch.py,sha256=HiBr72wpdB9cGHpfkShPK1pY7I9A4wVoKDh5mcBCMno,6367
xformers/ops/fmha/flash.py,sha256=QQepwLPi8WyKoHvy2XMjraVq6YIPVdWgZipJLv8vUgo,29314
xformers/ops/fmha/flash3.py,sha256=bgAFD86tfxoHfnopg4a-wUZF7dtXsf3fODxAmNoeTDI,14609
xformers/ops/fmha/torch_attention_compat.py,sha256=kaZv6ugCF2cIvlgD0flqncNh8SqYPMR37TKDu-9ifxw,5735
xformers/ops/fmha/triton_splitk.py,sha256=FsZYLBEnI7uybVp-b97AG9_PD8-P3zEp_DHJL6-ywtM,37620
xformers/ops/indexing.py,sha256=PNItxgTsir-KlMyKSWtgEoGwOa7VDLx5JlkJXGKvfj8,7164
xformers/ops/ipc.py,sha256=SS4Y8vBwCdITTHFGJhlipHGBgCIR7fUwgOw0UIyH2SM,6813
xformers/ops/modpar_layers.py,sha256=vythQeMelCL48x7rCf7aHPDF5Z1J5OY3YEX0GRXAakw,5863
xformers/ops/rmsnorm.py,sha256=5t_BN6OXyz7FpnRQn_8UvhpA8f5QjCnyyelsY5s2Crg,3471
xformers/ops/rope_padded.py,sha256=QRqxXfQo-tWBNfkPbCgyKUlK5fg3_sLQVlNAWhgZTG0,12400
xformers/ops/seqpar.py,sha256=eiJ0His4zfh4-Zx8KRXe9SEfxh6h8LYCCOYoOXyXDyQ,12422
xformers/ops/sequence_parallel_fused_ops.py,sha256=YlpS7pYyfeQIhQS4lXdLKjCCK35GzbIzm8ZpBglKtoU,37932
xformers/ops/sp24.py,sha256=5-iyS1EAoD6uw-noGYeHXXDf2-G0Acc6mcQyw6JTMzo,28798
xformers/ops/swiglu_op.py,sha256=H1TSR1i80x4ZZhHeEmXF3J8THDoDtuHUWchumjAmgD4,17879
xformers/ops/tiled_matmul.py,sha256=mPUbxqd4b2i0OH-K6YITfyjsw2mx1mcKIcFYJTk-mCM,13110
xformers/ops/unbind.py,sha256=r41YS3wMzwbUWCPW-Bb12WtyydpVTh5NIUd_XATnQCc,4054
xformers/profiler/__init__.py,sha256=xvGnOERGU06eBFWqGHijjBwQLK9AIfkciZBPYSWT2B0,436
xformers/profiler/__pycache__/__init__.cpython-312.pyc,,
xformers/profiler/__pycache__/api.cpython-312.pyc,,
xformers/profiler/__pycache__/device_limits.cpython-312.pyc,,
xformers/profiler/__pycache__/find_slowest.cpython-312.pyc,,
xformers/profiler/__pycache__/profile_analyzer.cpython-312.pyc,,
xformers/profiler/__pycache__/profiler.cpython-312.pyc,,
xformers/profiler/__pycache__/profiler_dcgm.cpython-312.pyc,,
xformers/profiler/__pycache__/profiler_dcgm_impl.cpython-312.pyc,,
xformers/profiler/api.py,sha256=jCmutrWZfp2tAiBgp53d2r3Xy_qMo-jweupaHNd_D9s,2777
xformers/profiler/device_limits.py,sha256=CBiqpeiJVDPbSfHh9HF2LCM_uws0CzhNJ-eQHZEIzvE,3701
xformers/profiler/find_slowest.py,sha256=TxU4uMkH7yScTZHhoWTlq6QZUfhdgLTjZbt-RQ8mFPc,5244
xformers/profiler/profile_analyzer.py,sha256=oD6Ky34uuWaObe0XAR7KPYYmokY5n4GND6fC_q2fwgE,8952
xformers/profiler/profiler.py,sha256=3wp0aIUW_ouSTxvRNjPqpKnxbEseSOTiQHry8EZgV3I,13106
xformers/profiler/profiler_dcgm.py,sha256=X0ClInhmUcjdbW7k9iPUkdQvGR9nfvbffmdgPIR_d3g,1198
xformers/profiler/profiler_dcgm_impl.py,sha256=Npza5x9ts_vMx4Fi-DpEcL2plcDFEQHEMPjqttzrtY8,8688
xformers/sparse/__init__.py,sha256=dgI-6hEZfhbzKszzAeGSux-uumEG2PnKrePQC0uJcks,324
xformers/sparse/__pycache__/__init__.cpython-312.pyc,,
xformers/sparse/__pycache__/_csr_ops.cpython-312.pyc,,
xformers/sparse/__pycache__/blocksparse_tensor.cpython-312.pyc,,
xformers/sparse/__pycache__/csr_tensor.cpython-312.pyc,,
xformers/sparse/__pycache__/utils.cpython-312.pyc,,
xformers/sparse/_csr_ops.py,sha256=cJyyY_pCxEnpUwfvhyJWKesEU2azf15gh-nJeTheSLU,5039
xformers/sparse/blocksparse_tensor.py,sha256=Zauyo5YfyrNAlHKDjHeSidopLL4nj61s-ix0EjkCbFM,9172
xformers/sparse/csr_tensor.py,sha256=BN2Ii8ospSjPKzpUPX8I8xaN4NMX1U0IOsHpG88Cgas,14628
xformers/sparse/utils.py,sha256=WL9FQ6UWBiHWcbmu3XcnGeOXb_MKr1gDP32ypxvXYGk,4397
xformers/test.py,sha256=M4NStf3p8zmHeaRF4kDPx3J4tJv4zraCm5oYv6rp7Lc,202
xformers/triton/__init__.py,sha256=M4NStf3p8zmHeaRF4kDPx3J4tJv4zraCm5oYv6rp7Lc,202
xformers/triton/__pycache__/__init__.cpython-312.pyc,,
xformers/triton/__pycache__/vararg_kernel.cpython-312.pyc,,
xformers/triton/vararg_kernel.py,sha256=pIZ--JAaPjvmOajOIGJVNQBcBOCyyGBJHazbLKbf1fk,9206
xformers/utils.py,sha256=Z0r6Jw8ok9enhvo6Gg687UXzr4NeFohmRu29QsUduwY,5209
xformers/version.py,sha256=xRReoL_WIL7sHGAQFxthDxHvQiktt3Mh86-ivR5HSyc,44
