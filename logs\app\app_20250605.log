2025-06-05 12:48:25 | INFO     | [SYSTEM](NO_SESSI) | 3d_ai_studio.app | System startup | Data: {"platform": "Windows-11-10.0.26100-SP0", "python_version": "3.12.10", "cpu_count": 88, "memory_total": "79.9 GB", "disk_free": "512.7 GB", "torch_version": "2.6.0+cu124", "cuda_available": true, "cuda_device": "NVIDIA GeForce RTX 3060", "cuda_memory": "12.0 GB"}
2025-06-05 12:48:25 | INFO     | [[STARTUP]]((NO_SESS) | 3d_ai_studio.app | 3D AI Studio starting up
2025-06-05 12:48:52 | INFO     | [[API]]((NO_SESS) | 3d_ai_studio.app | API: GET delighter_status | Status: 200 | Duration: 0.552s | IP: 127.0.0.1
2025-06-05 12:49:03 | INFO     | [[UPLOAD]]((92b552e) | 3d_ai_studio.app | Image upload started: sample-image.jpg | Data: {"filename": "sample-image.jpg", "image_id": "92b552e2-1e43-46f9-9e86-9470dd426c2b"}
2025-06-05 12:49:03 | INFO     | [[BACKGROUND_REMOVAL]]((92b552e) | 3d_ai_studio.app | Starting background removal for sample-image.jpg
2025-06-05 12:49:21 | INFO     | [[BACKGROUND_REMOVAL]]((92b552e) | 3d_ai_studio.app | Image opened successfully. Format: PNG, Size: (3461, 3461)
2025-06-05 12:49:21 | INFO     | [[BACKGROUND_REMOVAL]]((92b552e) | 3d_ai_studio.app | Removing background...
2025-06-05 12:49:31 | INFO     | [[BACKGROUND_REMOVAL]]((92b552e) | 3d_ai_studio.app | Background removal completed in 9.69s
2025-06-05 12:49:33 | INFO     | [[API]]((92b552e) | 3d_ai_studio.app | API: POST upload_image | Status: 200 | Duration: 30.681s | IP: 127.0.0.1
2025-06-05 12:58:42 | INFO     | [[API]]((92b552e) | 3d_ai_studio.app | API: POST generate_model | Status: 200 | Duration: 458.848s | IP: 127.0.0.1
2025-06-05 13:00:47 | INFO     | [[API]]((92b552e) | 3d_ai_studio.app | API: GET delighter_status | Status: 200 | Duration: 0.001s | IP: 127.0.0.1
2025-06-05 13:10:42 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | System startup | Data: {"platform": "Windows-11-10.0.26100-SP0", "python_version": "3.12.10", "cpu_count": 88, "memory_total": "79.9 GB", "disk_free": "512.6 GB", "torch_version": "2.6.0+cu124", "cuda_available": true, "cuda_device": "NVIDIA GeForce RTX 3060", "cuda_memory": "12.0 GB"}
2025-06-05 13:10:42 | INFO     | [[STARTUP]]((NO_SESS) | 3d_ai_studio.app | 3D AI Studio starting up
2025-06-05 13:10:57 | INFO     | [[API]]((NO_SESS) | 3d_ai_studio.app | API: GET delighter_status | Status: 200 | Duration: 0.026s | IP: 127.0.0.1
2025-06-05 13:11:05 | INFO     | [[UPLOAD]]((d46eb05) | 3d_ai_studio.app | Image upload started: sample-image.jpg | Data: {"filename": "sample-image.jpg", "image_id": "d46eb050-8910-46d0-863e-d1eb61d077ba"}
2025-06-05 13:11:06 | INFO     | [[BACKGROUND_REMOVAL]]((d46eb05) | 3d_ai_studio.app | Starting background removal for sample-image.jpg
2025-06-05 13:11:06 | INFO     | [[BACKGROUND_REMOVAL]]((d46eb05) | 3d_ai_studio.app | Image opened successfully. Format: PNG, Size: (3461, 3461)
2025-06-05 13:11:06 | INFO     | [[BACKGROUND_REMOVAL]]((d46eb05) | 3d_ai_studio.app | Removing background...
2025-06-05 13:11:10 | INFO     | [[BACKGROUND_REMOVAL]]((d46eb05) | 3d_ai_studio.app | Background removal completed in 4.13s
2025-06-05 13:11:12 | INFO     | [[API]]((d46eb05) | 3d_ai_studio.app | API: POST upload_image | Status: 200 | Duration: 7.218s | IP: 127.0.0.1
2025-06-05 13:18:15 | INFO     | [[API]]((d46eb05) | 3d_ai_studio.app | API: POST generate_model | Status: 200 | Duration: 409.303s | IP: 127.0.0.1
2025-06-05 14:35:23 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | System startup | Data: {"platform": "Windows-11-10.0.26100-SP0", "python_version": "3.12.10", "cpu_count": 88, "memory_total": "79.9 GB", "disk_free": "427.1 GB", "torch_version": "2.6.0+cu124", "cuda_available": true, "cuda_device": "NVIDIA GeForce RTX 3060", "cuda_memory": "12.0 GB"}
2025-06-05 14:35:23 | INFO     | [[STARTUP]]((NO_SESS) | 3d_ai_studio.app | 3D AI Studio starting up
2025-06-05 14:35:43 | INFO     | [[API]]((NO_SESS) | 3d_ai_studio.app | API: GET delighter_status | Status: 200 | Duration: 0.198s | IP: 127.0.0.1
2025-06-05 14:35:55 | INFO     | [[UPLOAD]]((cd3a17d) | 3d_ai_studio.app | Image upload started: sample-image.jpg | Data: {"filename": "sample-image.jpg", "image_id": "cd3a17db-881d-4bd6-8ee9-9f1cb1af3629"}
2025-06-05 14:35:55 | INFO     | [[BACKGROUND_REMOVAL]]((cd3a17d) | 3d_ai_studio.app | Starting background removal for sample-image.jpg
2025-06-05 14:35:55 | INFO     | [[BACKGROUND_REMOVAL]]((cd3a17d) | 3d_ai_studio.app | Image opened successfully. Format: PNG, Size: (3461, 3461)
2025-06-05 14:35:55 | INFO     | [[BACKGROUND_REMOVAL]]((cd3a17d) | 3d_ai_studio.app | Removing background...
2025-06-05 14:35:58 | INFO     | [[BACKGROUND_REMOVAL]]((cd3a17d) | 3d_ai_studio.app | Background removal completed in 3.43s
2025-06-05 14:36:01 | INFO     | [[API]]((cd3a17d) | 3d_ai_studio.app | API: POST upload_image | Status: 200 | Duration: 6.447s | IP: 127.0.0.1
2025-06-05 14:51:16 | INFO     | [[API]]((cd3a17d) | 3d_ai_studio.app | API: POST generate_model | Status: 500 | Duration: 900.442s | IP: 127.0.0.1
2025-06-05 14:57:25 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | System startup | Data: {"platform": "Windows-11-10.0.26100-SP0", "python_version": "3.12.10", "cpu_count": 88, "memory_total": "79.9 GB", "disk_free": "441.3 GB", "torch_version": "2.6.0+cu124", "cuda_available": true, "cuda_device": "NVIDIA GeForce RTX 3060", "cuda_memory": "12.0 GB"}
2025-06-05 14:57:25 | INFO     | [[STARTUP]]((NO_SESS) | 3d_ai_studio.app | 3D AI Studio starting up
2025-06-05 14:57:39 | INFO     | [[API]]((NO_SESS) | 3d_ai_studio.app | API: GET delighter_status | Status: 200 | Duration: 0.035s | IP: 127.0.0.1
2025-06-05 14:57:49 | INFO     | [[UPLOAD]]((1690854) | 3d_ai_studio.app | Image upload started: sample-image.jpg | Data: {"filename": "sample-image.jpg", "image_id": "1690854c-55c5-4580-9d3e-c4d6d8e101a7"}
2025-06-05 14:57:49 | INFO     | [[BACKGROUND_REMOVAL]]((1690854) | 3d_ai_studio.app | Starting background removal for sample-image.jpg
2025-06-05 14:57:49 | INFO     | [[BACKGROUND_REMOVAL]]((1690854) | 3d_ai_studio.app | Image opened successfully. Format: PNG, Size: (3461, 3461)
2025-06-05 14:57:49 | INFO     | [[BACKGROUND_REMOVAL]]((1690854) | 3d_ai_studio.app | Removing background...
2025-06-05 14:57:52 | INFO     | [[BACKGROUND_REMOVAL]]((1690854) | 3d_ai_studio.app | Background removal completed in 2.84s
2025-06-05 14:57:54 | INFO     | [[API]]((1690854) | 3d_ai_studio.app | API: POST upload_image | Status: 200 | Duration: 5.820s | IP: 127.0.0.1
2025-06-05 15:19:02 | INFO     | [[API]]((1690854) | 3d_ai_studio.app | API: POST generate_model | Status: 200 | Duration: 1244.145s | IP: 127.0.0.1
2025-06-05 17:58:44 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | System startup | Data: {"platform": "Windows-11-10.0.26100-SP0", "python_version": "3.12.10", "cpu_count": 88, "memory_total": "79.9 GB", "disk_free": "452.7 GB", "torch_version": "2.6.0+cu124", "cuda_available": true, "cuda_device": "NVIDIA GeForce RTX 3060", "cuda_memory": "12.0 GB"}
2025-06-05 17:58:44 | INFO     | [[STARTUP]]((NO_SESS) | 3d_ai_studio.app | 3D AI Studio starting up
2025-06-05 17:59:06 | INFO     | [[API]]((NO_SESS) | 3d_ai_studio.app | API: GET delighter_status | Status: 200 | Duration: 0.201s | IP: 127.0.0.1
2025-06-05 17:59:35 | INFO     | [[UPLOAD]]((978cee3) | 3d_ai_studio.app | Image upload started: sample-image.jpg | Data: {"filename": "sample-image.jpg", "image_id": "978cee35-2b87-4571-a4bd-aedf250d53b4"}
2025-06-05 17:59:35 | INFO     | [[BACKGROUND_REMOVAL]]((978cee3) | 3d_ai_studio.app | Starting background removal for sample-image.jpg
2025-06-05 17:59:37 | INFO     | [[BACKGROUND_REMOVAL]]((978cee3) | 3d_ai_studio.app | Image opened successfully. Format: PNG, Size: (3461, 3461)
2025-06-05 17:59:37 | INFO     | [[BACKGROUND_REMOVAL]]((978cee3) | 3d_ai_studio.app | Removing background...
2025-06-05 17:59:41 | INFO     | [[BACKGROUND_REMOVAL]]((978cee3) | 3d_ai_studio.app | Background removal completed in 3.56s
2025-06-05 17:59:44 | INFO     | [[API]]((978cee3) | 3d_ai_studio.app | API: POST upload_image | Status: 200 | Duration: 8.557s | IP: 127.0.0.1
2025-06-05 18:13:23 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services using main app logging system
2025-06-05 18:13:23 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Test message from background services
2025-06-05 18:13:23 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Initializing background services...
2025-06-05 18:13:23 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Registered service: hunyuan3d_native
2025-06-05 18:13:23 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Registered Hunyuan3D-2 native server service
2025-06-05 18:13:23 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 18:13:23 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Started service monitoring
2025-06-05 18:13:23 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   ❌ hunyuan3d_native: stopped (restarts: 0/1)
2025-06-05 18:13:23 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 18:13:23 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   ❌ hunyuan3d_native: stopped (restarts: 0/1)
2025-06-05 18:13:23 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services initialized successfully
2025-06-05 18:13:23 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 18:13:23 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   ❌ hunyuan3d_native: stopped (restarts: 0/1)
2025-06-05 18:13:32 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting background services...
2025-06-05 18:13:32 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting service: hunyuan3d_native
2025-06-05 18:13:32 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Started batch service hunyuan3d_native with PID 9328 (hidden window)
2025-06-05 18:15:10 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Service hunyuan3d_native started successfully and is ready
2025-06-05 18:15:12 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 18:15:12 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   ✅ hunyuan3d_native: running (restarts: 0/1)
2025-06-05 18:15:12 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |     🌐 Health check: http://localhost:8080/
2025-06-05 18:15:12 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | All background services started successfully
2025-06-05 18:15:24 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Stopping background services...
2025-06-05 18:15:29 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Stopped service monitoring
2025-06-05 18:15:29 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Stopping service hunyuan3d_native (PID 9328)
2025-06-05 18:15:29 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Service hunyuan3d_native stopped
2025-06-05 18:15:29 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | All background services stopped successfully
2025-06-05 18:15:29 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 18:15:29 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   ❌ hunyuan3d_native: stopped (restarts: 0/1)
2025-06-05 18:16:21 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | System startup | Data: {"platform": "Windows-11-10.0.26100-SP0", "python_version": "3.12.10", "cpu_count": 88, "memory_total": "79.9 GB", "disk_free": "452.7 GB", "torch_version": "2.6.0+cu124", "cuda_available": true, "cuda_device": "NVIDIA GeForce RTX 3060", "cuda_memory": "12.0 GB"}
2025-06-05 18:16:21 | INFO     | [[STARTUP]]((NO_SESS) | 3d_ai_studio.app | 3D AI Studio starting up
2025-06-05 18:16:31 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services using main app logging system
2025-06-05 18:16:31 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Initializing background services...
2025-06-05 18:16:31 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Registered service: hunyuan3d_native
2025-06-05 18:16:31 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Registered Hunyuan3D-2 native server service
2025-06-05 18:16:31 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 18:16:31 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Started service monitoring
2025-06-05 18:16:31 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   ❌ hunyuan3d_native: stopped (restarts: 0/1)
2025-06-05 18:16:31 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 18:16:31 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   ❌ hunyuan3d_native: stopped (restarts: 0/1)
2025-06-05 18:16:31 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services initialized successfully
2025-06-05 18:16:31 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting background services...
2025-06-05 18:16:31 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting service: hunyuan3d_native
2025-06-05 18:16:31 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Started batch service hunyuan3d_native with PID 29792 (hidden window)
2025-06-05 18:16:33 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Service hunyuan3d_native started successfully and is ready
2025-06-05 18:16:35 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 18:16:35 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   ✅ hunyuan3d_native: running (restarts: 0/1)
2025-06-05 18:16:35 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |     🌐 Health check: http://localhost:8080/
2025-06-05 18:16:35 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | All background services started successfully
2025-06-05 18:16:39 | INFO     | [[API]]((NO_SESS) | 3d_ai_studio.app | API: GET delighter_status | Status: 200 | Duration: 0.017s | IP: 127.0.0.1
2025-06-05 18:17:32 | INFO     | [[UPLOAD]]((22be182) | 3d_ai_studio.app | Image upload started: sample-image.jpg | Data: {"filename": "sample-image.jpg", "image_id": "22be182f-480b-43bd-8efe-a45d50fd08d3"}
2025-06-05 18:17:32 | INFO     | [[BACKGROUND_REMOVAL]]((22be182) | 3d_ai_studio.app | Starting background removal for sample-image.jpg
2025-06-05 18:17:32 | INFO     | [[BACKGROUND_REMOVAL]]((22be182) | 3d_ai_studio.app | Image opened successfully. Format: PNG, Size: (3461, 3461)
2025-06-05 18:17:32 | INFO     | [[BACKGROUND_REMOVAL]]((22be182) | 3d_ai_studio.app | Removing background...
2025-06-05 18:17:36 | INFO     | [[BACKGROUND_REMOVAL]]((22be182) | 3d_ai_studio.app | Background removal completed in 3.97s
2025-06-05 18:17:39 | INFO     | [[API]]((22be182) | 3d_ai_studio.app | API: POST upload_image | Status: 200 | Duration: 6.954s | IP: 127.0.0.1
2025-06-05 18:22:03 | INFO     | [[BACKGROUND_SERVICES]]((22be182) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 18:22:03 | INFO     | [[BACKGROUND_SERVICES]]((22be182) | 3d_ai_studio.app |   ✅ hunyuan3d_native: running (restarts: 0/1)
2025-06-05 18:22:03 | INFO     | [[BACKGROUND_SERVICES]]((22be182) | 3d_ai_studio.app |     🌐 Health check: http://localhost:8080/
2025-06-05 18:27:36 | INFO     | [[BACKGROUND_SERVICES]]((22be182) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 18:27:36 | INFO     | [[BACKGROUND_SERVICES]]((22be182) | 3d_ai_studio.app |   ✅ hunyuan3d_native: running (restarts: 0/1)
2025-06-05 18:27:36 | INFO     | [[BACKGROUND_SERVICES]]((22be182) | 3d_ai_studio.app |     🌐 Health check: http://localhost:8080/
2025-06-05 18:33:08 | INFO     | [[BACKGROUND_SERVICES]]((22be182) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 18:33:08 | INFO     | [[BACKGROUND_SERVICES]]((22be182) | 3d_ai_studio.app |   ✅ hunyuan3d_native: running (restarts: 0/1)
2025-06-05 18:33:08 | INFO     | [[BACKGROUND_SERVICES]]((22be182) | 3d_ai_studio.app |     🌐 Health check: http://localhost:8080/
2025-06-05 18:37:54 | INFO     | [[API]]((22be182) | 3d_ai_studio.app | API: POST generate_model | Status: 200 | Duration: 1094.542s | IP: 127.0.0.1
2025-06-05 18:38:39 | INFO     | [[BACKGROUND_SERVICES]]((22be182) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 18:38:39 | INFO     | [[BACKGROUND_SERVICES]]((22be182) | 3d_ai_studio.app |   ✅ hunyuan3d_native: running (restarts: 0/1)
2025-06-05 18:38:39 | INFO     | [[BACKGROUND_SERVICES]]((22be182) | 3d_ai_studio.app |     🌐 Health check: http://localhost:8080/
2025-06-05 19:20:01 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | System startup | Data: {"platform": "Windows-11-10.0.26100-SP0", "python_version": "3.12.10", "cpu_count": 88, "memory_total": "79.9 GB", "disk_free": "530.7 GB", "torch_version": "2.6.0+cu124", "cuda_available": true, "cuda_device": "NVIDIA GeForce RTX 3060", "cuda_memory": "12.0 GB"}
2025-06-05 19:20:01 | INFO     | [[STARTUP]]((NO_SESS) | 3d_ai_studio.app | 3D AI Studio starting up
2025-06-05 19:20:16 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services using main app logging system
2025-06-05 19:20:16 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Initializing background services...
2025-06-05 19:20:16 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Registered service: hunyuan3d_native
2025-06-05 19:20:16 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Registered Hunyuan3D-2 native server service
2025-06-05 19:20:16 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 19:20:16 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Started service monitoring
2025-06-05 19:20:16 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: stopped (restarts: 0/1)
2025-06-05 19:20:16 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 19:20:16 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: stopped (restarts: 0/1)
2025-06-05 19:20:16 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services initialized successfully
2025-06-05 19:20:16 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting background services...
2025-06-05 19:20:16 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting service: hunyuan3d_native
2025-06-05 19:20:16 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Started batch service hunyuan3d_native with PID 7012 (hidden window)
2025-06-05 19:20:18 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Service hunyuan3d_native started successfully and is ready
2025-06-05 19:20:20 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 19:20:20 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   RUNNING hunyuan3d_native: running (restarts: 0/1)
2025-06-05 19:20:20 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |     Health check: http://localhost:8080/
2025-06-05 19:20:20 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | All background services started successfully
2025-06-05 19:20:24 | INFO     | [[API]]((NO_SESS) | 3d_ai_studio.app | API: GET delighter_status | Status: 200 | Duration: 0.034s | IP: 127.0.0.1
2025-06-05 19:25:47 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 19:25:47 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   RUNNING hunyuan3d_native: running (restarts: 0/1)
2025-06-05 19:25:47 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |     Health check: http://localhost:8080/
2025-06-05 19:28:07 | INFO     | [[UPLOAD]]((2712dc8) | 3d_ai_studio.app | Image upload started: sample-image.jpg | Data: {"filename": "sample-image.jpg", "image_id": "2712dc89-8e5c-4e63-b226-aca1254c8abf"}
2025-06-05 19:28:08 | INFO     | [[BACKGROUND_REMOVAL]]((2712dc8) | 3d_ai_studio.app | Starting background removal for sample-image.jpg
2025-06-05 19:28:08 | INFO     | [[BACKGROUND_REMOVAL]]((2712dc8) | 3d_ai_studio.app | Image opened successfully. Format: PNG, Size: (3461, 3461)
2025-06-05 19:28:08 | INFO     | [[BACKGROUND_REMOVAL]]((2712dc8) | 3d_ai_studio.app | Removing background...
2025-06-05 19:28:11 | INFO     | [[BACKGROUND_REMOVAL]]((2712dc8) | 3d_ai_studio.app | Background removal completed in 3.30s
2025-06-05 19:28:14 | INFO     | [[API]]((2712dc8) | 3d_ai_studio.app | API: POST upload_image | Status: 200 | Duration: 6.861s | IP: 127.0.0.1
2025-06-05 19:32:03 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | System startup | Data: {"platform": "Windows-11-10.0.26100-SP0", "python_version": "3.12.10", "cpu_count": 88, "memory_total": "79.9 GB", "disk_free": "530.6 GB", "torch_version": "2.6.0+cu124", "cuda_available": true, "cuda_device": "NVIDIA GeForce RTX 3060", "cuda_memory": "12.0 GB"}
2025-06-05 19:32:03 | INFO     | [[STARTUP]]((NO_SESS) | 3d_ai_studio.app | 3D AI Studio starting up
2025-06-05 19:32:14 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services using main app logging system
2025-06-05 19:32:14 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Initializing background services...
2025-06-05 19:32:14 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Registered service: hunyuan3d_native
2025-06-05 19:32:14 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Registered Hunyuan3D-2 native server service
2025-06-05 19:32:14 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 19:32:14 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Started service monitoring
2025-06-05 19:32:14 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: stopped (restarts: 0/1)
2025-06-05 19:32:14 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 19:32:14 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: stopped (restarts: 0/1)
2025-06-05 19:32:14 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services initialized successfully
2025-06-05 19:32:14 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting background services...
2025-06-05 19:32:14 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting service: hunyuan3d_native
2025-06-05 19:32:14 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Started batch service hunyuan3d_native with PID 16816 (hidden window)
2025-06-05 19:32:16 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Service hunyuan3d_native started successfully and is ready
2025-06-05 19:32:18 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 19:32:18 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   RUNNING hunyuan3d_native: running (restarts: 0/1)
2025-06-05 19:32:18 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |     Health check: http://localhost:8080/
2025-06-05 19:32:18 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | All background services started successfully
2025-06-05 19:34:07 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | System startup | Data: {"platform": "Windows-11-10.0.26100-SP0", "python_version": "3.12.10", "cpu_count": 88, "memory_total": "79.9 GB", "disk_free": "530.6 GB", "torch_version": "2.6.0+cu124", "cuda_available": true, "cuda_device": "NVIDIA GeForce RTX 3060", "cuda_memory": "12.0 GB"}
2025-06-05 19:34:07 | INFO     | [[STARTUP]]((NO_SESS) | 3d_ai_studio.app | 3D AI Studio starting up
2025-06-05 19:34:17 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services using main app logging system
2025-06-05 19:34:17 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Initializing background services...
2025-06-05 19:34:17 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Registered service: hunyuan3d_native
2025-06-05 19:34:17 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Registered Hunyuan3D-2 native server service
2025-06-05 19:34:17 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 19:34:17 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Started service monitoring
2025-06-05 19:34:17 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: stopped (restarts: 0/1)
2025-06-05 19:34:17 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 19:34:17 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: stopped (restarts: 0/1)
2025-06-05 19:34:17 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services initialized successfully
2025-06-05 19:34:17 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting background services...
2025-06-05 19:34:17 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting service: hunyuan3d_native
2025-06-05 19:34:18 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Started batch service hunyuan3d_native with PID 32876 (hidden window)
2025-06-05 19:34:20 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Service hunyuan3d_native started successfully and is ready
2025-06-05 19:34:22 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 19:34:22 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   RUNNING hunyuan3d_native: running (restarts: 0/1)
2025-06-05 19:34:22 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |     Health check: http://localhost:8080/
2025-06-05 19:34:22 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | All background services started successfully
2025-06-05 19:34:26 | INFO     | [[API]]((NO_SESS) | 3d_ai_studio.app | API: GET delighter_status | Status: 200 | Duration: 0.028s | IP: 127.0.0.1
2025-06-05 19:35:17 | INFO     | [[UPLOAD]]((9c55fdd) | 3d_ai_studio.app | Image upload started: sample-image.jpg | Data: {"filename": "sample-image.jpg", "image_id": "9c55fdda-ef2e-4bbb-8c7f-240c1821076d"}
2025-06-05 19:35:17 | INFO     | [[BACKGROUND_REMOVAL]]((9c55fdd) | 3d_ai_studio.app | Starting background removal for sample-image.jpg
2025-06-05 19:35:17 | INFO     | [[BACKGROUND_REMOVAL]]((9c55fdd) | 3d_ai_studio.app | Image opened successfully. Format: PNG, Size: (3461, 3461)
2025-06-05 19:35:17 | INFO     | [[BACKGROUND_REMOVAL]]((9c55fdd) | 3d_ai_studio.app | Removing background...
2025-06-05 19:35:21 | INFO     | [[BACKGROUND_REMOVAL]]((9c55fdd) | 3d_ai_studio.app | Background removal completed in 3.83s
2025-06-05 19:35:24 | INFO     | [[API]]((9c55fdd) | 3d_ai_studio.app | API: POST upload_image | Status: 200 | Duration: 7.457s | IP: 127.0.0.1
2025-06-05 19:39:49 | INFO     | [[BACKGROUND_SERVICES]]((9c55fdd) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 19:39:49 | INFO     | [[BACKGROUND_SERVICES]]((9c55fdd) | 3d_ai_studio.app |   RUNNING hunyuan3d_native: running (restarts: 0/1)
2025-06-05 19:39:49 | INFO     | [[BACKGROUND_SERVICES]]((9c55fdd) | 3d_ai_studio.app |     Health check: http://localhost:8080/
2025-06-05 19:49:11 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | System startup | Data: {"platform": "Windows-11-10.0.26100-SP0", "python_version": "3.12.10", "cpu_count": 88, "memory_total": "79.9 GB", "disk_free": "530.6 GB", "torch_version": "2.6.0+cu124", "cuda_available": true, "cuda_device": "NVIDIA GeForce RTX 3060", "cuda_memory": "12.0 GB"}
2025-06-05 19:49:11 | INFO     | [[STARTUP]]((NO_SESS) | 3d_ai_studio.app | 3D AI Studio starting up
2025-06-05 19:49:30 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services using main app logging system
2025-06-05 19:49:30 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Initializing background services...
2025-06-05 19:49:30 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Registered service: hunyuan3d_native
2025-06-05 19:49:30 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Registered Hunyuan3D-2 native server service
2025-06-05 19:49:30 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 19:49:30 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Started service monitoring
2025-06-05 19:49:30 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: stopped (restarts: 0/1)
2025-06-05 19:49:30 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 19:49:30 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: stopped (restarts: 0/1)
2025-06-05 19:49:30 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services initialized successfully
2025-06-05 19:49:30 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting background services...
2025-06-05 19:49:30 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting service: hunyuan3d_native
2025-06-05 19:49:30 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Started batch service hunyuan3d_native with PID 33936 (hidden window)
2025-06-05 19:49:32 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Service hunyuan3d_native started successfully and is ready
2025-06-05 19:49:34 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 19:49:34 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   RUNNING hunyuan3d_native: running (restarts: 0/1)
2025-06-05 19:49:34 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |     Health check: http://localhost:8080/
2025-06-05 19:49:34 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | All background services started successfully
2025-06-05 19:49:38 | INFO     | [[API]]((NO_SESS) | 3d_ai_studio.app | API: GET delighter_status | Status: 200 | Duration: 0.285s | IP: 127.0.0.1
2025-06-05 19:50:16 | INFO     | [[UPLOAD]]((a741e65) | 3d_ai_studio.app | Image upload started: sample-image.jpg | Data: {"filename": "sample-image.jpg", "image_id": "a741e655-d56a-4c12-9d09-a1a735beacaa"}
2025-06-05 19:50:17 | INFO     | [[BACKGROUND_REMOVAL]]((a741e65) | 3d_ai_studio.app | Starting background removal for sample-image.jpg
2025-06-05 19:56:58 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | System startup | Data: {"platform": "Windows-11-10.0.26100-SP0", "python_version": "3.12.10", "cpu_count": 88, "memory_total": "79.9 GB", "disk_free": "530.6 GB", "torch_version": "2.6.0+cu124", "cuda_available": true, "cuda_device": "NVIDIA GeForce RTX 3060", "cuda_memory": "12.0 GB"}
2025-06-05 19:56:58 | INFO     | [[STARTUP]]((NO_SESS) | 3d_ai_studio.app | 3D AI Studio starting up
2025-06-05 19:57:09 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services using main app logging system
2025-06-05 19:57:09 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Initializing background services...
2025-06-05 19:57:09 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Registered service: hunyuan3d_native
2025-06-05 19:57:09 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Registered Hunyuan3D-2 native server service
2025-06-05 19:57:09 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 19:57:09 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Started service monitoring
2025-06-05 19:57:09 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: stopped (restarts: 0/1)
2025-06-05 19:57:09 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 19:57:09 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: stopped (restarts: 0/1)
2025-06-05 19:57:09 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services initialized successfully
2025-06-05 19:57:09 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting background services...
2025-06-05 19:57:09 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting service: hunyuan3d_native
2025-06-05 19:57:09 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Started batch service hunyuan3d_native with PID 20908 (hidden window)
2025-06-05 19:57:11 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Service hunyuan3d_native started successfully and is ready
2025-06-05 19:57:13 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 19:57:13 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   RUNNING hunyuan3d_native: running (restarts: 0/1)
2025-06-05 19:57:13 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |     Health check: http://localhost:8080/
2025-06-05 19:57:13 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | All background services started successfully
2025-06-05 19:57:17 | INFO     | [[API]]((NO_SESS) | 3d_ai_studio.app | API: GET delighter_status | Status: 200 | Duration: 0.022s | IP: 127.0.0.1
2025-06-05 19:58:41 | INFO     | [[UPLOAD]]((274fb25) | 3d_ai_studio.app | Image upload started: sample-image.jpg | Data: {"filename": "sample-image.jpg", "image_id": "274fb25a-468f-46d4-a6b9-ac69c6ec6d80"}
2025-06-05 19:58:41 | INFO     | [[BACKGROUND_REMOVAL]]((274fb25) | 3d_ai_studio.app | Starting background removal for sample-image.jpg
2025-06-05 19:58:49 | INFO     | [[BACKGROUND_REMOVAL]]((274fb25) | 3d_ai_studio.app | Image opened successfully. Format: PNG, Size: (3461, 3461)
2025-06-05 19:58:49 | INFO     | [[BACKGROUND_REMOVAL]]((274fb25) | 3d_ai_studio.app | Removing background...
2025-06-05 19:58:57 | INFO     | [[BACKGROUND_REMOVAL]]((274fb25) | 3d_ai_studio.app | Background removal completed in 7.59s
2025-06-05 19:59:02 | INFO     | [[API]]((274fb25) | 3d_ai_studio.app | API: POST upload_image | Status: 200 | Duration: 21.383s | IP: 127.0.0.1
2025-06-05 20:04:19 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | System startup | Data: {"platform": "Windows-11-10.0.26100-SP0", "python_version": "3.12.10", "cpu_count": 88, "memory_total": "79.9 GB", "disk_free": "530.5 GB", "torch_version": "2.6.0+cu124", "cuda_available": true, "cuda_device": "NVIDIA GeForce RTX 3060", "cuda_memory": "12.0 GB"}
2025-06-05 20:04:19 | INFO     | [[STARTUP]]((NO_SESS) | 3d_ai_studio.app | 3D AI Studio starting up
2025-06-05 20:04:29 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services using main app logging system
2025-06-05 20:04:29 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Initializing background services...
2025-06-05 20:04:29 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Registered service: hunyuan3d_native
2025-06-05 20:04:29 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Registered Hunyuan3D-2 native server service
2025-06-05 20:04:29 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 20:04:29 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Started service monitoring
2025-06-05 20:04:29 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: stopped (restarts: 0/1)
2025-06-05 20:04:29 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 20:04:29 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: stopped (restarts: 0/1)
2025-06-05 20:04:29 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services initialized successfully
2025-06-05 20:04:29 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting background services...
2025-06-05 20:04:29 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting service: hunyuan3d_native
2025-06-05 20:04:29 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Started batch service hunyuan3d_native with PID 22532 (hidden window)
2025-06-05 20:04:31 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Service hunyuan3d_native started successfully and is ready
2025-06-05 20:04:33 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 20:04:33 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   RUNNING hunyuan3d_native: running (restarts: 0/1)
2025-06-05 20:04:33 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |     Health check: http://localhost:8080/
2025-06-05 20:04:33 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | All background services started successfully
2025-06-05 20:04:37 | INFO     | [[API]]((NO_SESS) | 3d_ai_studio.app | API: GET delighter_status | Status: 200 | Duration: 0.022s | IP: 127.0.0.1
2025-06-05 20:04:57 | INFO     | [[UPLOAD]]((8044adf) | 3d_ai_studio.app | Image upload started: sample-image.jpg | Data: {"filename": "sample-image.jpg", "image_id": "8044adf9-ab40-4ae7-887f-a9125c60f74c"}
2025-06-05 20:04:57 | INFO     | [[BACKGROUND_REMOVAL]]((8044adf) | 3d_ai_studio.app | Starting background removal for sample-image.jpg
2025-06-05 20:04:57 | INFO     | [[BACKGROUND_REMOVAL]]((8044adf) | 3d_ai_studio.app | Image opened successfully. Format: PNG, Size: (3461, 3461)
2025-06-05 20:04:57 | INFO     | [[BACKGROUND_REMOVAL]]((8044adf) | 3d_ai_studio.app | Removing background...
2025-06-05 20:05:00 | INFO     | [[BACKGROUND_REMOVAL]]((8044adf) | 3d_ai_studio.app | Background removal completed in 3.09s
2025-06-05 20:05:03 | INFO     | [[API]]((8044adf) | 3d_ai_studio.app | API: POST upload_image | Status: 200 | Duration: 6.131s | IP: 127.0.0.1
2025-06-05 20:13:49 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services using main app logging system
2025-06-05 20:13:49 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Initializing background services...
2025-06-05 20:13:49 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Registered service: hunyuan3d_native
2025-06-05 20:13:49 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Registered Hunyuan3D-2 native server service with text-to-3D support
2025-06-05 20:13:49 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 20:13:49 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Started service monitoring
2025-06-05 20:13:49 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: stopped (restarts: 0/2)
2025-06-05 20:13:49 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 20:13:49 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: stopped (restarts: 0/2)
2025-06-05 20:13:49 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services initialized successfully
2025-06-05 20:13:49 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting background services...
2025-06-05 20:13:49 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting service: hunyuan3d_native
2025-06-05 20:13:49 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Started batch service hunyuan3d_native with PID 27624 (hidden window)
2025-06-05 20:13:51 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Service hunyuan3d_native started successfully and is ready
2025-06-05 20:13:53 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 20:13:53 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   RUNNING hunyuan3d_native: running (restarts: 0/2)
2025-06-05 20:13:53 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |     Health check: http://localhost:8080/
2025-06-05 20:13:53 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | All background services started successfully
2025-06-05 20:14:05 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Stopping background services...
2025-06-05 20:14:10 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Stopped service monitoring
2025-06-05 20:14:10 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Stopping service hunyuan3d_native (PID 27624)
2025-06-05 20:14:10 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Service hunyuan3d_native stopped
2025-06-05 20:14:10 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | All background services stopped successfully
2025-06-05 20:16:29 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | System startup | Data: {"platform": "Windows-11-10.0.26100-SP0", "python_version": "3.12.10", "cpu_count": 88, "memory_total": "79.9 GB", "disk_free": "530.5 GB", "torch_version": "2.6.0+cu124", "cuda_available": true, "cuda_device": "NVIDIA GeForce RTX 3060", "cuda_memory": "12.0 GB"}
2025-06-05 20:16:29 | INFO     | [[STARTUP]]((NO_SESS) | 3d_ai_studio.app | 3D AI Studio starting up
2025-06-05 20:16:40 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services using main app logging system
2025-06-05 20:16:40 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Initializing background services...
2025-06-05 20:16:40 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Registered service: hunyuan3d_native
2025-06-05 20:16:40 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Registered Hunyuan3D-2 native server service with text-to-3D support
2025-06-05 20:16:40 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 20:16:40 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Started service monitoring
2025-06-05 20:16:40 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: stopped (restarts: 0/2)
2025-06-05 20:16:40 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 20:16:40 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: stopped (restarts: 0/2)
2025-06-05 20:16:40 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services initialized successfully
2025-06-05 20:16:40 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting background services...
2025-06-05 20:16:40 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting service: hunyuan3d_native
2025-06-05 20:16:40 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Started batch service hunyuan3d_native with PID 32616 (hidden window)
2025-06-05 20:16:42 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Service hunyuan3d_native started successfully and is ready
2025-06-05 20:16:44 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 20:16:44 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   RUNNING hunyuan3d_native: running (restarts: 0/2)
2025-06-05 20:16:44 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |     Health check: http://localhost:8080/
2025-06-05 20:16:44 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | All background services started successfully
2025-06-05 20:16:48 | INFO     | [[API]]((NO_SESS) | 3d_ai_studio.app | API: GET delighter_status | Status: 200 | Duration: 0.031s | IP: 127.0.0.1
2025-06-05 20:18:11 | INFO     | [[UPLOAD]]((5cca7fa) | 3d_ai_studio.app | Image upload started: sample-image.jpg | Data: {"filename": "sample-image.jpg", "image_id": "5cca7fae-9f17-42a3-b5d8-58184cdfb674"}
2025-06-05 20:18:11 | INFO     | [[BACKGROUND_REMOVAL]]((5cca7fa) | 3d_ai_studio.app | Starting background removal for sample-image.jpg
2025-06-05 20:18:11 | INFO     | [[BACKGROUND_REMOVAL]]((5cca7fa) | 3d_ai_studio.app | Image opened successfully. Format: PNG, Size: (3461, 3461)
2025-06-05 20:18:11 | INFO     | [[BACKGROUND_REMOVAL]]((5cca7fa) | 3d_ai_studio.app | Removing background...
2025-06-05 20:18:14 | INFO     | [[BACKGROUND_REMOVAL]]((5cca7fa) | 3d_ai_studio.app | Background removal completed in 3.46s
2025-06-05 20:18:17 | INFO     | [[API]]((5cca7fa) | 3d_ai_studio.app | API: POST upload_image | Status: 200 | Duration: 6.830s | IP: 127.0.0.1
2025-06-05 20:18:23 | ERROR    | [[HUNYUAN3D_CLIENT]]((5cca7fa) | 3d_ai_studio.app | Error during image-to-3D generation: cannot access free variable 'update_progress' where it is not associated with a value in enclosing scope
2025-06-05 20:18:23 | INFO     | [[API]]((5cca7fa) | 3d_ai_studio.app | API: POST generate_model | Status: 500 | Duration: 2.099s | IP: 127.0.0.1
2025-06-05 21:41:42 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | System startup | Data: {"platform": "Windows-11-10.0.26100-SP0", "python_version": "3.12.10", "cpu_count": 88, "memory_total": "79.9 GB", "disk_free": "533.1 GB", "torch_version": "2.7.1+cpu", "cuda_available": false}
2025-06-05 21:41:42 | INFO     | [[STARTUP]]((NO_SESS) | 3d_ai_studio.app | 3D AI Studio starting up
2025-06-05 21:41:53 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services using main app logging system
2025-06-05 21:41:53 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Initializing background services...
2025-06-05 21:41:53 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Registered service: hunyuan3d_native
2025-06-05 21:41:53 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Registered Hunyuan3D-2 native server service with text-to-3D support
2025-06-05 21:41:53 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 21:41:53 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Started service monitoring
2025-06-05 21:41:53 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: stopped (restarts: 0/2)
2025-06-05 21:41:53 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 21:41:53 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: stopped (restarts: 0/2)
2025-06-05 21:41:53 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services initialized successfully
2025-06-05 21:41:53 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting background services...
2025-06-05 21:41:53 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting service: hunyuan3d_native
2025-06-05 21:41:53 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Started batch service hunyuan3d_native with PID 27724 (hidden window)
2025-06-05 21:47:08 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 21:47:08 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: starting (restarts: 0/2)
2025-06-05 21:52:23 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 21:52:23 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: starting (restarts: 0/2)
2025-06-05 21:57:09 | ERROR    | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Service hunyuan3d_native failed to become ready within 900 seconds
2025-06-05 21:57:09 | ERROR    | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Service hunyuan3d_native failed to become ready within timeout
2025-06-05 21:57:09 | ERROR    | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Failed to start service: hunyuan3d_native
2025-06-05 21:57:09 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 21:57:09 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: failed (restarts: 0/2)
2025-06-05 21:57:09 | WARNING  | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Some background services failed to start
2025-06-05 21:57:38 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 21:57:38 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: failed (restarts: 0/2)
2025-06-05 22:02:53 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 22:02:53 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: failed (restarts: 0/2)
2025-06-05 22:08:08 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 22:08:08 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: failed (restarts: 0/2)
2025-06-05 22:13:23 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 22:13:23 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: failed (restarts: 0/2)
2025-06-05 22:14:46 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | System startup | Data: {"platform": "Windows-11-10.0.26100-SP0", "python_version": "3.12.10", "cpu_count": 88, "memory_total": "79.9 GB", "disk_free": "533.1 GB", "torch_version": "2.7.1+cpu", "cuda_available": false}
2025-06-05 22:14:46 | INFO     | [[STARTUP]]((NO_SESS) | 3d_ai_studio.app | 3D AI Studio starting up
2025-06-05 22:14:57 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services using main app logging system
2025-06-05 22:14:57 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Initializing background services...
2025-06-05 22:14:57 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Registered service: hunyuan3d_native
2025-06-05 22:14:57 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Registered Hunyuan3D-2 native server service with text-to-3D support
2025-06-05 22:14:57 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 22:14:57 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Started service monitoring
2025-06-05 22:14:57 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: stopped (restarts: 0/2)
2025-06-05 22:14:57 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 22:14:57 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: stopped (restarts: 0/2)
2025-06-05 22:14:57 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services initialized successfully
2025-06-05 22:14:57 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting background services...
2025-06-05 22:14:57 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting service: hunyuan3d_native
2025-06-05 22:14:57 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Started batch service hunyuan3d_native with PID 18508 (hidden window)
2025-06-05 22:14:59 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Service hunyuan3d_native started successfully and is ready
2025-06-05 22:15:01 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 22:15:01 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   RUNNING hunyuan3d_native: running (restarts: 0/2)
2025-06-05 22:15:01 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |     Health check: http://localhost:8080/
2025-06-05 22:15:01 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | All background services started successfully
2025-06-05 22:15:13 | INFO     | [[API]]((NO_SESS) | 3d_ai_studio.app | API: GET delighter_status | Status: 200 | Duration: 0.034s | IP: 127.0.0.1
2025-06-05 22:15:48 | INFO     | [[UPLOAD]]((d2fdc46) | 3d_ai_studio.app | Image upload started: sample-image.jpg | Data: {"filename": "sample-image.jpg", "image_id": "d2fdc462-f7d1-46b3-9a69-3929c4aae3f5"}
2025-06-05 22:15:49 | INFO     | [[BACKGROUND_REMOVAL]]((d2fdc46) | 3d_ai_studio.app | Starting background removal for sample-image.jpg
2025-06-05 22:17:11 | INFO     | [[BACKGROUND_REMOVAL]]((d2fdc46) | 3d_ai_studio.app | Image opened successfully. Format: PNG, Size: (3461, 3461)
2025-06-05 22:17:11 | INFO     | [[BACKGROUND_REMOVAL]]((d2fdc46) | 3d_ai_studio.app | Removing background...
2025-06-05 22:17:18 | INFO     | [[BACKGROUND_REMOVAL]]((d2fdc46) | 3d_ai_studio.app | Background removal completed in 6.64s
2025-06-05 22:17:27 | INFO     | [[API]]((d2fdc46) | 3d_ai_studio.app | API: POST upload_image | Status: 200 | Duration: 98.947s | IP: 127.0.0.1
2025-06-05 22:20:29 | INFO     | [[BACKGROUND_SERVICES]]((d2fdc46) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 22:20:29 | INFO     | [[BACKGROUND_SERVICES]]((d2fdc46) | 3d_ai_studio.app |   RUNNING hunyuan3d_native: running (restarts: 0/2)
2025-06-05 22:20:29 | INFO     | [[BACKGROUND_SERVICES]]((d2fdc46) | 3d_ai_studio.app |     Health check: http://localhost:8080/
2025-06-05 22:26:01 | INFO     | [[BACKGROUND_SERVICES]]((d2fdc46) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 22:26:01 | INFO     | [[BACKGROUND_SERVICES]]((d2fdc46) | 3d_ai_studio.app |   RUNNING hunyuan3d_native: running (restarts: 0/2)
2025-06-05 22:26:01 | INFO     | [[BACKGROUND_SERVICES]]((d2fdc46) | 3d_ai_studio.app |     Health check: http://localhost:8080/
2025-06-05 22:26:48 | INFO     | [[API]]((d2fdc46) | 3d_ai_studio.app | API: POST generate_model | Status: 200 | Duration: 410.277s | IP: 127.0.0.1
2025-06-05 22:30:09 | INFO     | [[API]]((d2fdc46) | 3d_ai_studio.app | API: GET delighter_status | Status: 200 | Duration: 0.001s | IP: 127.0.0.1
2025-06-05 22:31:33 | INFO     | [[BACKGROUND_SERVICES]]((d2fdc46) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 22:31:33 | INFO     | [[BACKGROUND_SERVICES]]((d2fdc46) | 3d_ai_studio.app |   RUNNING hunyuan3d_native: running (restarts: 0/2)
2025-06-05 22:31:33 | INFO     | [[BACKGROUND_SERVICES]]((d2fdc46) | 3d_ai_studio.app |     Health check: http://localhost:8080/
2025-06-05 22:33:36 | INFO     | [[API]]((d2fdc46) | 3d_ai_studio.app | API: POST generate_model | Status: 200 | Duration: 227.317s | IP: 127.0.0.1
2025-06-05 22:36:41 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | System startup | Data: {"platform": "Windows-11-10.0.26100-SP0", "python_version": "3.12.10", "cpu_count": 88, "memory_total": "79.9 GB", "disk_free": "539.2 GB", "torch_version": "2.7.1+cpu", "cuda_available": false}
2025-06-05 22:36:41 | INFO     | [[STARTUP]]((NO_SESS) | 3d_ai_studio.app | 3D AI Studio starting up
2025-06-05 22:36:57 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services using main app logging system
2025-06-05 22:36:57 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Initializing background services...
2025-06-05 22:36:57 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Registered service: hunyuan3d_native
2025-06-05 22:36:57 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Registered Hunyuan3D-2 native server service with text-to-3D support
2025-06-05 22:36:57 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 22:36:57 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Started service monitoring
2025-06-05 22:36:57 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: stopped (restarts: 0/2)
2025-06-05 22:36:57 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 22:36:57 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: stopped (restarts: 0/2)
2025-06-05 22:36:57 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services initialized successfully
2025-06-05 22:36:57 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting background services...
2025-06-05 22:36:57 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting service: hunyuan3d_native
2025-06-05 22:36:57 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Started batch service hunyuan3d_native with PID 23088 (hidden window)
2025-06-05 22:36:59 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Service hunyuan3d_native started successfully and is ready
2025-06-05 22:37:01 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 22:37:01 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   RUNNING hunyuan3d_native: running (restarts: 0/2)
2025-06-05 22:37:01 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |     Health check: http://localhost:8080/
2025-06-05 22:37:01 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | All background services started successfully
2025-06-05 22:37:05 | INFO     | [[API]]((NO_SESS) | 3d_ai_studio.app | API: GET delighter_status | Status: 200 | Duration: 0.048s | IP: 127.0.0.1
2025-06-05 22:42:24 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | System startup | Data: {"platform": "Windows-11-10.0.26100-SP0", "python_version": "3.12.10", "cpu_count": 88, "memory_total": "79.9 GB", "disk_free": "539.2 GB", "torch_version": "2.7.1+cpu", "cuda_available": false}
2025-06-05 22:42:24 | INFO     | [[STARTUP]]((NO_SESS) | 3d_ai_studio.app | 3D AI Studio starting up
2025-06-05 22:42:34 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services using main app logging system
2025-06-05 22:42:34 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Initializing background services...
2025-06-05 22:42:34 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Registered service: hunyuan3d_native
2025-06-05 22:42:34 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Registered Hunyuan3D-2 native server service with text-to-3D support
2025-06-05 22:42:34 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 22:42:34 | INFO     | [[SYSTEM]]((NO_SESS) | 3d_ai_studio.app | Started service monitoring
2025-06-05 22:42:34 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: stopped (restarts: 0/2)
2025-06-05 22:42:34 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 22:42:34 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   STOPPED hunyuan3d_native: stopped (restarts: 0/2)
2025-06-05 22:42:34 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background services initialized successfully
2025-06-05 22:42:34 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting background services...
2025-06-05 22:42:34 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Starting service: hunyuan3d_native
2025-06-05 22:42:34 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Started batch service hunyuan3d_native with PID 13748 (hidden window)
2025-06-05 22:42:36 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Service hunyuan3d_native started successfully and is ready
2025-06-05 22:42:38 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | Background Services Status Report:
2025-06-05 22:42:38 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |   RUNNING hunyuan3d_native: running (restarts: 0/2)
2025-06-05 22:42:38 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app |     Health check: http://localhost:8080/
2025-06-05 22:42:38 | INFO     | [[BACKGROUND_SERVICES]]((NO_SESS) | 3d_ai_studio.app | All background services started successfully
2025-06-05 22:42:42 | INFO     | [[API]]((NO_SESS) | 3d_ai_studio.app | API: GET delighter_status | Status: 200 | Duration: 0.004s | IP: 127.0.0.1
2025-06-05 22:43:21 | INFO     | [[UPLOAD]]((ccb2bdb) | 3d_ai_studio.app | Image upload started: sample-image.jpg | Data: {"filename": "sample-image.jpg", "image_id": "ccb2bdb6-623a-4a4f-8a43-a6bd82d4179a"}
2025-06-05 22:43:21 | INFO     | [[BACKGROUND_REMOVAL]]((ccb2bdb) | 3d_ai_studio.app | Starting background removal for sample-image.jpg
2025-06-05 22:43:21 | INFO     | [[BACKGROUND_REMOVAL]]((ccb2bdb) | 3d_ai_studio.app | Image opened successfully. Format: PNG, Size: (2500, 2500)
2025-06-05 22:43:21 | INFO     | [[BACKGROUND_REMOVAL]]((ccb2bdb) | 3d_ai_studio.app | Removing background...
2025-06-05 22:43:24 | INFO     | [[BACKGROUND_REMOVAL]]((ccb2bdb) | 3d_ai_studio.app | Background removal completed in 3.15s
2025-06-05 22:43:25 | INFO     | [[API]]((ccb2bdb) | 3d_ai_studio.app | API: POST upload_image | Status: 200 | Duration: 4.606s | IP: 127.0.0.1
2025-06-05 22:43:49 | ERROR    | [[HUNYUAN3D_CLIENT]]((ccb2bdb) | 3d_ai_studio.app | Error during image-to-3D generation: cannot access free variable 'update_progress' where it is not associated with a value in enclosing scope
2025-06-05 22:43:49 | INFO     | [[API]]((ccb2bdb) | 3d_ai_studio.app | API: POST generate_model | Status: 500 | Duration: 2.096s | IP: 127.0.0.1
