#!/usr/bin/env python3
"""
Mesh Statistics Utilities
Provides functions to extract detailed statistics from 3D mesh files.
"""

import os
import trimesh
import numpy as np
from PIL import Image
from pathlib import Path
import logging

# Set up logging
try:
    from utils.logging_system import get_logger
    logger = get_logger()
except ImportError:
    import logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

def get_mesh_statistics(file_path):
    """
    Extract comprehensive statistics from a 3D mesh file.
    
    Args:
        file_path (str): Path to the mesh file (.glb, .ply, .obj, etc.)
        
    Returns:
        dict: Dictionary containing mesh statistics
    """
    if not os.path.exists(file_path):
        logger.warning(f"Mesh file not found: {file_path}")
        return {}
    
    try:
        # Load the mesh using trimesh
        mesh = trimesh.load(file_path)
        
        # Handle scene vs single mesh
        if hasattr(mesh, 'geometry'):
            # It's a scene with multiple geometries
            geometries = list(mesh.geometry.values())
            if geometries:
                # Combine all geometries for statistics
                combined_vertices = []
                combined_faces = []
                vertex_offset = 0
                
                for geom in geometries:
                    if hasattr(geom, 'vertices') and hasattr(geom, 'faces'):
                        combined_vertices.append(geom.vertices)
                        # Adjust face indices for combined mesh
                        adjusted_faces = geom.faces + vertex_offset
                        combined_faces.append(adjusted_faces)
                        vertex_offset += len(geom.vertices)
                
                if combined_vertices and combined_faces:
                    vertices = np.vstack(combined_vertices)
                    faces = np.vstack(combined_faces)
                else:
                    vertices = np.array([])
                    faces = np.array([])
            else:
                vertices = np.array([])
                faces = np.array([])
        else:
            # It's a single mesh
            vertices = mesh.vertices if hasattr(mesh, 'vertices') else np.array([])
            faces = mesh.faces if hasattr(mesh, 'faces') else np.array([])
        
        # Calculate basic statistics
        vertex_count = len(vertices) if len(vertices.shape) > 1 else 0
        face_count = len(faces) if len(faces.shape) > 1 else 0
        polygon_count = face_count  # For triangular meshes, face count = polygon count
        
        # Calculate file size
        file_size = os.path.getsize(file_path)
        
        # Get file format
        file_extension = Path(file_path).suffix.lower()
        file_format = file_extension[1:] if file_extension.startswith('.') else file_extension
        
        # Calculate mesh bounds and volume (if possible)
        bounds = None
        volume = 0
        surface_area = 0

        if vertex_count > 0:
            try:
                if hasattr(mesh, 'bounds'):
                    bounds = mesh.bounds
                elif len(vertices) > 0:
                    bounds = [vertices.min(axis=0), vertices.max(axis=0)]

                # Convert bounds to JSON-serializable format
                if bounds is not None:
                    if hasattr(bounds, 'tolist'):
                        # NumPy array
                        bounds = bounds.tolist()
                    elif isinstance(bounds, list) and len(bounds) == 2:
                        # List of arrays
                        bounds = [
                            bound.tolist() if hasattr(bound, 'tolist') else list(bound)
                            for bound in bounds
                        ]

                # Calculate volume and surface area for single mesh
                if hasattr(mesh, 'volume') and not hasattr(mesh, 'geometry'):
                    volume = float(mesh.volume) if mesh.volume is not None else 0
                if hasattr(mesh, 'area') and not hasattr(mesh, 'geometry'):
                    surface_area = float(mesh.area) if mesh.area is not None else 0

            except Exception as e:
                logger.warning(f"Could not calculate mesh bounds/volume: {e}")
        
        # Try to detect texture information
        texture_info = get_texture_info(file_path, mesh)
        
        # Compile statistics (ensure all values are JSON serializable)
        stats = {
            'vertices': int(vertex_count),
            'faces': int(face_count),
            'polygons': int(polygon_count),
            'file_size': int(file_size),
            'file_format': str(file_format),
            'bounds': bounds,  # Already converted to list above
            'volume': float(volume),
            'surface_area': float(surface_area),
            **texture_info
        }
        
        logger.info(f"Extracted mesh statistics: {vertex_count} vertices, {face_count} faces, {file_size} bytes",
                   component="MESH_STATS")

        # Ensure all data is JSON serializable
        return ensure_json_serializable(stats)
        
    except Exception as e:
        logger.error(f"Error extracting mesh statistics from {file_path}: {e}", component="MESH_STATS")
        error_stats = {
            'vertices': 0,
            'faces': 0,
            'polygons': 0,
            'file_size': os.path.getsize(file_path) if os.path.exists(file_path) else 0,
            'file_format': Path(file_path).suffix[1:] if Path(file_path).suffix else 'unknown',
            'error': str(e)
        }
        return ensure_json_serializable(error_stats)

def get_texture_info(file_path, mesh):
    """
    Extract texture information from a mesh file.
    
    Args:
        file_path (str): Path to the mesh file
        mesh: Loaded trimesh object
        
    Returns:
        dict: Dictionary containing texture information
    """
    texture_info = {
        'has_textures': False,
        'texture_count': 0,
        'texture_resolution': None,
        'texture_formats': []
    }
    
    try:
        # Check for materials and textures
        if hasattr(mesh, 'visual') and mesh.visual is not None:
            if hasattr(mesh.visual, 'material'):
                material = mesh.visual.material
                
                # Check for texture images
                if hasattr(material, 'image') and material.image is not None:
                    texture_info['has_textures'] = True
                    texture_info['texture_count'] = 1
                    
                    # Get texture resolution
                    if hasattr(material.image, 'size'):
                        texture_info['texture_resolution'] = f"{material.image.size[0]}x{material.image.size[1]}"
                    elif hasattr(material.image, 'width') and hasattr(material.image, 'height'):
                        texture_info['texture_resolution'] = f"{material.image.width}x{material.image.height}"
                
                # Check for multiple materials (scene case)
                elif hasattr(material, '__iter__'):
                    texture_count = 0
                    resolutions = []
                    formats = []
                    
                    for mat in material:
                        if hasattr(mat, 'image') and mat.image is not None:
                            texture_count += 1
                            
                            if hasattr(mat.image, 'size'):
                                resolutions.append(f"{mat.image.size[0]}x{mat.image.size[1]}")
                            elif hasattr(mat.image, 'width') and hasattr(mat.image, 'height'):
                                resolutions.append(f"{mat.image.width}x{mat.image.height}")
                    
                    if texture_count > 0:
                        texture_info['has_textures'] = True
                        texture_info['texture_count'] = texture_count
                        if resolutions:
                            # Use the largest resolution found
                            texture_info['texture_resolution'] = max(resolutions, key=lambda x: int(x.split('x')[0]))
        
        # For GLB files, try to extract texture info from the file structure
        if file_path.lower().endswith('.glb'):
            try:
                # Check if there are associated texture files in the same directory
                base_dir = os.path.dirname(file_path)
                base_name = os.path.splitext(os.path.basename(file_path))[0]
                
                texture_extensions = ['.png', '.jpg', '.jpeg', '.tga', '.bmp']
                found_textures = []
                
                for ext in texture_extensions:
                    texture_path = os.path.join(base_dir, f"{base_name}{ext}")
                    if os.path.exists(texture_path):
                        found_textures.append(texture_path)
                
                if found_textures:
                    texture_info['has_textures'] = True
                    texture_info['texture_count'] = len(found_textures)
                    
                    # Get resolution from the first texture
                    try:
                        with Image.open(found_textures[0]) as img:
                            texture_info['texture_resolution'] = f"{img.width}x{img.height}"
                            texture_info['texture_formats'] = [Path(t).suffix[1:] for t in found_textures]
                    except Exception:
                        pass
                        
            except Exception as e:
                logger.debug(f"Could not extract GLB texture info: {e}")
    
    except Exception as e:
        logger.debug(f"Error extracting texture info: {e}")
    
    return texture_info

def format_file_size(size_bytes):
    """
    Format file size in human-readable format.
    
    Args:
        size_bytes (int): File size in bytes
        
    Returns:
        str: Formatted file size (e.g., "1.2 MB")
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"

def get_generation_timing_stats(start_time, end_time, stage_times=None):
    """
    Calculate generation timing statistics.

    Args:
        start_time (float): Generation start timestamp
        end_time (float): Generation end timestamp
        stage_times (dict): Optional dictionary of stage timings

    Returns:
        dict: Dictionary containing timing statistics
    """
    total_time = end_time - start_time

    timing_stats = {
        'total_time': total_time,
        'total_time_formatted': f"{total_time:.1f}s"
    }

    if stage_times:
        timing_stats.update(stage_times)

    return timing_stats

def ensure_json_serializable(obj):
    """
    Recursively convert NumPy arrays and other non-serializable objects to JSON-serializable types.

    Args:
        obj: Object to convert

    Returns:
        JSON-serializable version of the object
    """
    import numpy as np

    if isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, dict):
        return {key: ensure_json_serializable(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [ensure_json_serializable(item) for item in obj]
    elif isinstance(obj, tuple):
        return [ensure_json_serializable(item) for item in obj]
    else:
        return obj
