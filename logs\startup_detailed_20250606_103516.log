2025-06-06 10:35:16,078 |     INFO | startup_detailed | log_system_info:68 | ================================================================================
2025-06-06 10:35:16,079 |     INFO | startup_detailed | log_system_info:69 | 3D AI STUDIO STARTUP - DETAILED LOGGING
2025-06-06 10:35:16,079 |     INFO | startup_detailed | log_system_info:70 | ================================================================================
2025-06-06 10:35:21,026 |     INFO | startup_detailed | log_system_info:94 | SYSTEM INFORMATION:
2025-06-06 10:35:21,027 |     INFO | startup_detailed | log_system_info:96 |   timestamp: 2025-06-06T10:35:20.836194
2025-06-06 10:35:21,027 |     INFO | startup_detailed | log_system_info:96 |   platform: Windows-11-10.0.26100-SP0
2025-06-06 10:35:21,028 |     INFO | startup_detailed | log_system_info:96 |   python_version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
2025-06-06 10:35:21,029 |     INFO | startup_detailed | log_system_info:96 |   python_executable: E:\3D AI Studio\.venv\Scripts\python.exe
2025-06-06 10:35:21,029 |     INFO | startup_detailed | log_system_info:96 |   working_directory: E:\3D AI Studio
2025-06-06 10:35:21,030 |     INFO | startup_detailed | log_system_info:96 |   cpu_count: 88
2025-06-06 10:35:21,030 |     INFO | startup_detailed | log_system_info:96 |   memory_total: 79.9 GB
2025-06-06 10:35:21,030 |     INFO | startup_detailed | log_system_info:96 |   disk_free: 529.8 GB
2025-06-06 10:35:21,031 |     INFO | startup_detailed | log_system_info:96 |   torch_version: 2.5.1+cu124
2025-06-06 10:35:21,031 |     INFO | startup_detailed | log_system_info:96 |   cuda_available: True
2025-06-06 10:35:21,032 |     INFO | startup_detailed | log_system_info:96 |   cuda_device: NVIDIA GeForce RTX 3060
2025-06-06 10:35:21,032 |     INFO | startup_detailed | log_system_info:96 |   cuda_memory: 12.0 GB
2025-06-06 10:35:39,384 |     INFO | startup_detailed | log_pipeline_loading:157 | PIPELINE: Hunyuan3D-2 - STARTING
2025-06-06 10:35:39,385 |     INFO | startup_detailed | log_step:112 | STEP: Background removal model - LOADING
2025-06-06 10:35:40,907 |     INFO | startup_detailed | log_model_loading:163 | MODEL: Background Removal - LOADED (1.5s)
2025-06-06 10:35:40,908 |    DEBUG | startup_detailed | log_model_loading:164 | Model path: hy3dgen.rembg
2025-06-06 10:35:40,908 |     INFO | startup_detailed | log_step:112 | STEP: Shape generation imports - IMPORTING
2025-06-06 10:35:40,909 |    DEBUG | startup_detailed | log_import_attempt:142 | IMPORT SUCCESS: hy3dgen.shapegen
2025-06-06 10:35:40,909 |     INFO | startup_detailed | log_step:112 | STEP: Shape generation imports - COMPLETED (0.0s)
2025-06-06 10:35:40,910 |     INFO | startup_detailed | log_step:112 | STEP: Main shape generation pipeline - LOADING
2025-06-06 10:35:40,910 |     INFO | startup_detailed | log_model_loading:163 | MODEL: Hunyuan3D DiT - FAILED (0.0s)
2025-06-06 10:35:40,911 |    DEBUG | startup_detailed | log_model_loading:164 | Model path: tencent/Hunyuan3D-2mini/hunyuan3d-dit-v2-mini-turbo
2025-06-06 10:35:53,412 |     INFO | startup_detailed | log_model_loading:163 | MODEL: Hunyuan3D DiT - LOADED (12.5s)
2025-06-06 10:35:53,413 |    DEBUG | startup_detailed | log_model_loading:164 | Model path: tencent/Hunyuan3D-2mini/hunyuan3d-dit-v2-mini-turbo
2025-06-06 10:35:53,413 |     INFO | startup_detailed | log_step:112 | STEP: FlashVDM optimization - ENABLING
2025-06-06 10:35:56,033 |     INFO | startup_detailed | log_success:123 | SUCCESS: FlashVDM enabled with dmc algorithm
2025-06-06 10:35:56,035 |     INFO | startup_detailed | log_pipeline_loading:157 | PIPELINE: Shape Generation - LOADED (15.1s)
2025-06-06 10:35:56,037 |     INFO | startup_detailed | log_step:112 | STEP: Texture generation pipeline - STARTING
2025-06-06 10:35:56,038 |     INFO | startup_detailed | log_step:112 | STEP: Texture generation imports - IMPORTING
2025-06-06 10:35:56,038 |    DEBUG | startup_detailed | log_import_attempt:142 | IMPORT SUCCESS: hy3dgen.texgen
2025-06-06 10:35:56,039 |     INFO | startup_detailed | log_step:112 | STEP: Texture model cache check - Checking Resources\Hunyuan3D2_WinPortable\HuggingFaceHub\models--tencent--Hunyuan3D-2\snapshots
2025-06-06 10:35:56,041 |     INFO | startup_detailed | log_step:112 | STEP: Texture model paths - Delight: True, Paint: True
2025-06-06 10:35:56,044 |     INFO | startup_detailed | log_step:112 | STEP: Texture pipeline loading - LOADING MODELS
2025-06-06 10:35:56,471 |    ERROR | startup_detailed | log_error:134 | ERROR: Texture generation pipeline failed after 0.4s
2025-06-06 10:35:56,472 |    ERROR | startup_detailed | log_error:136 | Exception: Model path Resources\Hunyuan3D2_WinPortable\HuggingFaceHub\models--tencent--Hunyuan3D-2\snapshots\34e28261f71c32975727be8db0eace439a280f82 not found
2025-06-06 10:35:56,493 |    ERROR | startup_detailed | log_error:137 | Traceback (most recent call last):
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\texgen\pipelines.py", line 71, in from_pretrained
    return cls(Hunyuan3DTexGenConfig(delight_model_path, multiview_model_path))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\texgen\pipelines.py", line 91, in __init__
    self.render = MeshRender(
                  ^^^^^^^^^^^
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\texgen\differentiable_renderer\mesh_render.py", line 145, in __init__
    import custom_rasterizer as cr
  File "E:\3D AI Studio\.venv\Lib\site-packages\custom_rasterizer\__init__.py", line 22, in <module>
    from .render import *
  File "E:\3D AI Studio\.venv\Lib\site-packages\custom_rasterizer\render.py", line 15, in <module>
    import custom_rasterizer_kernel
ImportError: DLL load failed while importing custom_rasterizer_kernel: The specified procedure could not be found.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\3D AI Studio\hunyuan3d_integrated_pipeline.py", line 232, in load_pipeline_components
    self.texgen_worker = Hunyuan3DPaintPipeline.from_pretrained(self.texgen_model_path)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\texgen\pipelines.py", line 76, in from_pretrained
    raise RuntimeError(f"Model path {model_path} not found")
RuntimeError: Model path Resources\Hunyuan3D2_WinPortable\HuggingFaceHub\models--tencent--Hunyuan3D-2\snapshots\34e28261f71c32975727be8db0eace439a280f82 not found

