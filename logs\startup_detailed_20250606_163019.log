2025-06-06 16:30:19,260 |     INFO | startup_detailed | log_system_info:68 | ================================================================================
2025-06-06 16:30:19,261 |     INFO | startup_detailed | log_system_info:69 | 3D AI STUDIO STARTUP - DETAILED LOGGING
2025-06-06 16:30:19,262 |     INFO | startup_detailed | log_system_info:70 | ================================================================================
2025-06-06 16:30:24,053 |     INFO | startup_detailed | log_system_info:94 | SYSTEM INFORMATION:
2025-06-06 16:30:24,054 |     INFO | startup_detailed | log_system_info:96 |   timestamp: 2025-06-06T16:30:23.867682
2025-06-06 16:30:24,054 |     INFO | startup_detailed | log_system_info:96 |   platform: Windows-11-10.0.26100-SP0
2025-06-06 16:30:24,055 |     INFO | startup_detailed | log_system_info:96 |   python_version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
2025-06-06 16:30:24,055 |     INFO | startup_detailed | log_system_info:96 |   python_executable: E:\3D AI Studio\.venv\Scripts\python.exe
2025-06-06 16:30:24,056 |     INFO | startup_detailed | log_system_info:96 |   working_directory: E:\3D AI Studio
2025-06-06 16:30:24,056 |     INFO | startup_detailed | log_system_info:96 |   cpu_count: 88
2025-06-06 16:30:24,056 |     INFO | startup_detailed | log_system_info:96 |   memory_total: 79.9 GB
2025-06-06 16:30:24,056 |     INFO | startup_detailed | log_system_info:96 |   disk_free: 529.7 GB
2025-06-06 16:30:24,057 |     INFO | startup_detailed | log_system_info:96 |   torch_version: 2.6.0+cu124
2025-06-06 16:30:24,057 |     INFO | startup_detailed | log_system_info:96 |   cuda_available: True
2025-06-06 16:30:24,057 |     INFO | startup_detailed | log_system_info:96 |   cuda_device: NVIDIA GeForce RTX 3060
2025-06-06 16:30:24,058 |     INFO | startup_detailed | log_system_info:96 |   cuda_memory: 12.0 GB
2025-06-06 16:30:40,804 |     INFO | startup_detailed | log_pipeline_loading:157 | PIPELINE: Hunyuan3D-2 - STARTING
2025-06-06 16:30:40,806 |     INFO | startup_detailed | log_step:112 | STEP: Background removal model - LOADING
2025-06-06 16:30:42,316 |     INFO | startup_detailed | log_model_loading:163 | MODEL: Background Removal - LOADED (1.5s)
2025-06-06 16:30:42,317 |    DEBUG | startup_detailed | log_model_loading:164 | Model path: hy3dgen.rembg
2025-06-06 16:30:42,317 |     INFO | startup_detailed | log_step:112 | STEP: Shape generation imports - IMPORTING
2025-06-06 16:30:42,318 |    DEBUG | startup_detailed | log_import_attempt:142 | IMPORT SUCCESS: hy3dgen.shapegen
2025-06-06 16:30:42,318 |     INFO | startup_detailed | log_step:112 | STEP: Shape generation imports - COMPLETED (0.0s)
2025-06-06 16:30:42,318 |     INFO | startup_detailed | log_step:112 | STEP: Main shape generation pipeline - LOADING
2025-06-06 16:30:42,319 |     INFO | startup_detailed | log_model_loading:163 | MODEL: Hunyuan3D DiT - FAILED (0.0s)
2025-06-06 16:30:42,319 |    DEBUG | startup_detailed | log_model_loading:164 | Model path: tencent/Hunyuan3D-2mini/hunyuan3d-dit-v2-mini-turbo
2025-06-06 16:30:53,176 |     INFO | startup_detailed | log_model_loading:163 | MODEL: Hunyuan3D DiT - LOADED (10.9s)
2025-06-06 16:30:53,177 |    DEBUG | startup_detailed | log_model_loading:164 | Model path: tencent/Hunyuan3D-2mini/hunyuan3d-dit-v2-mini-turbo
2025-06-06 16:30:53,177 |     INFO | startup_detailed | log_step:112 | STEP: FlashVDM optimization - ENABLING
2025-06-06 16:30:55,782 |     INFO | startup_detailed | log_success:123 | SUCCESS: FlashVDM enabled with dmc algorithm
2025-06-06 16:30:55,784 |     INFO | startup_detailed | log_pipeline_loading:157 | PIPELINE: Shape Generation - LOADED (13.5s)
2025-06-06 16:30:55,787 |     INFO | startup_detailed | log_step:112 | STEP: Texture generation pipeline - STARTING
2025-06-06 16:30:55,788 |     INFO | startup_detailed | log_step:112 | STEP: Texture generation imports - IMPORTING
2025-06-06 16:30:55,789 |    DEBUG | startup_detailed | log_import_attempt:142 | IMPORT SUCCESS: hy3dgen.texgen
2025-06-06 16:30:55,789 |     INFO | startup_detailed | log_step:112 | STEP: Texture model cache check - Checking Resources\Hunyuan3D2_WinPortable\HuggingFaceHub\models--tencent--Hunyuan3D-2\snapshots
2025-06-06 16:30:55,792 |     INFO | startup_detailed | log_step:112 | STEP: Texture model paths - Delight: True, Paint: True
2025-06-06 16:30:55,794 |     INFO | startup_detailed | log_step:112 | STEP: Texture pipeline loading - LOADING MODELS
2025-06-06 16:30:55,795 |     INFO | startup_detailed | log_step:112 | STEP: Using local texture model path - Resources\Hunyuan3D2_WinPortable\HuggingFaceHub\models--tencent--Hunyuan3D-2\snapshots\34e28261f71c32975727be8db0eace439a280f82
2025-06-06 16:31:00,023 |    ERROR | startup_detailed | log_error:134 | ERROR: Texture generation pipeline failed after 4.2s
2025-06-06 16:31:00,024 |    ERROR | startup_detailed | log_error:136 | Exception: 'SafeTensorFile' object has no attribute 'get_slice'
2025-06-06 16:31:00,046 |    ERROR | startup_detailed | log_error:137 | Traceback (most recent call last):
  File "E:\3D AI Studio\hunyuan3d_integrated_pipeline.py", line 252, in load_pipeline_components
    self.texgen_worker = Hunyuan3DPaintPipeline.from_pretrained(local_texgen_path)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\texgen\pipelines.py", line 86, in from_pretrained
    return cls(Hunyuan3DTexGenConfig(delight_model_path, multiview_model_path))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\texgen\pipelines.py", line 95, in __init__
    self.load_models()
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\texgen\pipelines.py", line 101, in load_models
    self.models['delight_model'] = Light_Shadow_Remover(self.config)
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\texgen\utils\dehighlight_utils.py", line 28, in __init__
    pipeline = StableDiffusionInstructPix2PixPipeline.from_pretrained(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\pipelines\pipeline_utils.py", line 961, in from_pretrained
    loaded_sub_model = load_sub_model(
                       ^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\pipelines\pipeline_loading_utils.py", line 777, in load_sub_model
    loaded_sub_model = load_method(os.path.join(cached_folder, name), **loading_kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\transformers\modeling_utils.py", line 309, in _wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\transformers\modeling_utils.py", line 4574, in from_pretrained
    ) = cls._load_pretrained_model(
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\transformers\modeling_utils.py", line 4833, in _load_pretrained_model
    load_state_dict(checkpoint_files[0], map_location="meta", weights_only=weights_only).keys()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\transformers\modeling_utils.py", line 541, in load_state_dict
    _slice = f.get_slice(k)
             ^^^^^^^^^^^
AttributeError: 'SafeTensorFile' object has no attribute 'get_slice'

2025-06-06 16:31:00,064 |     INFO | startup_detailed | log_step:112 | STEP: Text-to-image pipeline - LOADING
2025-06-06 16:31:01,146 |  WARNING | startup_detailed | log_warning:127 | WARNING: Text-to-image pipeline failed: 'SafeTensorFile' object has no attribute 'get_slice'
