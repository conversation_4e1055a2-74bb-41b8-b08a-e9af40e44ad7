# LICENSE HEADER MANAGED BY add-license-header
#
# Copyright 2018 Kornia Team
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# Lazy loading auto module
from kornia.augmentation import auto, container
from kornia.augmentation._2d import (
    CenterCrop,
    ColorJiggle,
    ColorJitter,
    Denormalize,
    LongestMaxSize,
    Normalize,
    PadTo,
    RandomAffine,
    RandomAutoContrast,
    RandomBoxBlur,
    RandomBrightness,
    RandomChannelDropout,
    RandomChannelShuffle,
    RandomClahe,
    RandomContrast,
    RandomCrop,
    RandomCutMixV2,
    RandomDissolving,
    RandomElasticTransform,
    RandomEqualize,
    RandomErasing,
    RandomFisheye,
    RandomGamma,
    RandomGaussianBlur,
    RandomGaussianIllumination,
    RandomGaussianNoise,
    RandomGrayscale,
    RandomHorizontalFlip,
    RandomHue,
    RandomInvert,
    RandomJigsaw,
    RandomJPEG,
    RandomLinearCornerIllumination,
    RandomLinearIllumination,
    RandomMedianBlur,
    RandomMixUpV2,
    RandomMosaic,
    RandomMotionBlur,
    RandomPerspective,
    RandomPlanckianJitter,
    RandomPlasmaBrightness,
    RandomPlasmaContrast,
    RandomPlasmaShadow,
    RandomPosterize,
    RandomRain,
    RandomResizedCrop,
    RandomRGBShift,
    RandomRotation,
    RandomRotation90,
    RandomSaltAndPepperNoise,
    RandomSaturation,
    RandomSharpness,
    RandomShear,
    RandomSnow,
    RandomSolarize,
    RandomThinPlateSpline,
    RandomTranslate,
    RandomTransplantation,
    RandomVerticalFlip,
    Resize,
    SmallestMaxSize,
)
from kornia.augmentation._2d.base import AugmentationBase2D, RigidAffineAugmentationBase2D
from kornia.augmentation._2d.geometric.base import GeometricAugmentationBase2D
from kornia.augmentation._2d.intensity.base import IntensityAugmentationBase2D
from kornia.augmentation._2d.mix.base import MixAugmentationBaseV2
from kornia.augmentation._3d import (
    CenterCrop3D,
    RandomAffine3D,
    RandomCrop3D,
    RandomDepthicalFlip3D,
    RandomEqualize3D,
    RandomHorizontalFlip3D,
    RandomMotionBlur3D,
    RandomPerspective3D,
    RandomRotation3D,
    RandomTransplantation3D,
    RandomVerticalFlip3D,
)
from kornia.augmentation._3d.base import AugmentationBase3D, RigidAffineAugmentationBase3D
from kornia.augmentation._3d.geometric.base import GeometricAugmentationBase3D
from kornia.augmentation._3d.intensity.base import IntensityAugmentationBase3D
from kornia.augmentation.container import (
    AugmentationSequential,
    ImageSequential,
    ManyToManyAugmentationDispather,
    ManyToOneAugmentationDispather,
    PatchSequential,
    VideoSequential,
)

__all__ = [
    "AugmentationBase2D",
    "AugmentationBase3D",
    "AugmentationSequential",
    "CenterCrop",
    "CenterCrop3D",
    "ColorJiggle",
    "ColorJitter",
    "Denormalize",
    "GeometricAugmentationBase2D",
    "GeometricAugmentationBase3D",
    "ImageSequential",
    "IntensityAugmentationBase2D",
    "IntensityAugmentationBase3D",
    "LongestMaxSize",
    "ManyToManyAugmentationDispather",
    "ManyToOneAugmentationDispather",
    "MixAugmentationBaseV2",
    "Normalize",
    "PadTo",
    "PatchSequential",
    "RandomAffine",
    "RandomAffine3D",
    "RandomAutoContrast",
    "RandomBoxBlur",
    "RandomBrightness",
    "RandomChannelDropout",
    "RandomChannelShuffle",
    "RandomContrast",
    "RandomCrop",
    "RandomCrop3D",
    "RandomCutMixV2",
    "RandomDepthicalFlip3D",
    "RandomDissolving",
    "RandomElasticTransform",
    "RandomEqualize",
    "RandomEqualize3D",
    "RandomErasing",
    "RandomFisheye",
    "RandomGamma",
    "RandomGaussianBlur",
    "RandomGaussianIllumination",
    "RandomGaussianNoise",
    "RandomGrayscale",
    "RandomHorizontalFlip",
    "RandomHorizontalFlip3D",
    "RandomHue",
    "RandomInvert",
    "RandomJigsaw",
    "RandomLinearCornerIllumination",
    "RandomLinearIllumination",
    "RandomMedianBlur",
    "RandomMixUpV2",
    "RandomMosaic",
    "RandomMotionBlur",
    "RandomMotionBlur3D",
    "RandomPerspective",
    "RandomPerspective3D",
    "RandomPlanckianJitter",
    "RandomPlasmaBrightness",
    "RandomPlasmaContrast",
    "RandomPlasmaShadow",
    "RandomPosterize",
    "RandomRGBShift",
    "RandomRGBShift",
    "RandomRain",
    "RandomResizedCrop",
    "RandomRotation",
    "RandomRotation3D",
    "RandomRotation90",
    "RandomSaltAndPepperNoise",
    "RandomSaturation",
    "RandomSharpness",
    "RandomShear",
    "RandomSnow",
    "RandomSolarize",
    "RandomThinPlateSpline",
    "RandomTranslate",
    "RandomVerticalFlip",
    "RandomVerticalFlip3D",
    "Resize",
    "RigidAffineAugmentationBase2D",
    "RigidAffineAugmentationBase3D",
    "SmallestMaxSize",
    "VideoSequential",
    "auto",
    "container",
]
