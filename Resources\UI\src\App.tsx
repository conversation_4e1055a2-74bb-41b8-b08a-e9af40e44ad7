import React, { useState, useCallback, useEffect } from 'react';
import { ImageUpload } from './components/ImageUpload';
import { TextInput } from './components/TextInput';
import { ModelViewer } from './components/ModelViewer';
import { Settings } from './components/Settings';
import { SampleGallery } from './components/SampleGallery';
import { ProgressBar } from './components/ProgressBar';
import { SettingsModal } from './components/SettingsModal';
import { PromptCrafter } from './components/PromptCrafter';
import { ProjectsGallery } from './components/ProjectsGallery';

import { AgisoftDelighter } from './components/AgisoftDelighter';
import { DependencyManager } from './components/DependencyManager';
import { Image as ImageIcon, Type as TypeIcon, Cuboid as Cube, Download, Play, Sun, Moon, AlertCircle, Settings as SettingsIcon, FolderOpen, Database } from 'lucide-react';

// API URL
const API_URL = 'http://localhost:5000/api';

interface ModelSettings {
  ss_steps: number;
  ss_cfg_strength: number;
  slat_steps: number;
  slat_cfg_strength: number;
  seed?: number;
  randomize_seed?: boolean;
  simplify: number;
  texture_size: number;
  enable_lighting_optimizer?: boolean;
  // Hunyuan3D-2 specific settings
  octree_resolution?: number;
  num_inference_steps?: number;
  guidance_scale?: number;
  enable_texture?: boolean;
  face_count?: number;
}

interface Pipeline {
  id: string;
  name: string;
  description: string;
  available: boolean;
  features: string[];
}

type InputMode = 'image' | 'text';
type NavigationMode = 'generator' | 'projects';

function App() {
  const [navigationMode, setNavigationMode] = useState<NavigationMode>('generator');
  const [inputMode, setInputMode] = useState<InputMode>('image');
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [textPrompt, setTextPrompt] = useState<string>('');
  const [uploadedFile, setUploadedFile] = useState<{ image_id: string; filename: string } | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showTextured, setShowTextured] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(true);
  const [modelUrl, setModelUrl] = useState<string | null>(null);
  const [videoUrl, setVideoUrl] = useState<string | null>(null);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [modelError, setModelError] = useState<string | null>(null);
  const [isLoadingSample, setIsLoadingSample] = useState(false);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [isModelManagerOpen, setIsModelManagerOpen] = useState(false);
  const [selectedImageModel, setSelectedImageModel] = useState('sdxl_turbo');
  const [settings, setSettings] = useState<ModelSettings>({
    ss_steps: 12,
    ss_cfg_strength: 7.5,
    slat_steps: 12,
    slat_cfg_strength: 3.0,
    randomize_seed: true,
    seed: Math.floor(Math.random() * 1000000),
    simplify: 0.95,
    texture_size: 1024,
    enable_lighting_optimizer: true,
    // Hunyuan3D-2 defaults
    hunyuan_model: 'turbo',
    octree_resolution: 128,
    num_inference_steps: 5,
    guidance_scale: 5.0,
    enable_texture: true,
    face_count: 40000
  });

  // Pipeline selection state
  const [selectedPipeline, setSelectedPipeline] = useState('trellis');
  const [availablePipelines, setAvailablePipelines] = useState<Pipeline[]>([]);

  // Agisoft De-Lighter texture enhancement state (disabled by default - advanced feature)
  const [delighterEnabled, setDelighterEnabled] = useState(false);
  const [delighterQuality, setDelighterQuality] = useState('high');
  const [delighterPreviewImage, setDelighterPreviewImage] = useState<string | null>(null);

  // Generation stats for info panel
  const [generationStats, setGenerationStats] = useState<any>(null);

  // Load current model preference and available pipelines on component mount
  useEffect(() => {
    const loadModelPreference = async () => {
      try {
        const response = await fetch('/api/config/huggingface-token');
        if (response.ok) {
          const status = await response.json();
          if (status.preferred_model) {
            setSelectedImageModel(status.preferred_model);
          }
        }
      } catch (error) {
        console.error('Error loading model preference:', error);
      }
    };

    const loadAvailablePipelines = async () => {
      try {
        console.log('Loading available pipelines...');
        const response = await fetch('/api/pipelines/available');
        if (response.ok) {
          const data = await response.json();
          console.log('Pipeline availability response:', data);
          setAvailablePipelines(data.pipelines || []);

          // Only auto-select if no pipeline is currently selected or if current selection is not available
          const currentlySelected = selectedPipeline;
          const currentPipelineAvailable = data.pipelines.find((p: Pipeline) => p.id === currentlySelected && p.available);

          if (!currentPipelineAvailable) {
            // Current selection is not available, find the best alternative
            const hunyuan3d = data.pipelines.find((p: Pipeline) => p.id === 'hunyuan3d' && p.available);
            const trellis = data.pipelines.find((p: Pipeline) => p.id === 'trellis' && p.available);
            const defaultPipeline = data.pipelines.find((p: Pipeline) => p.id === data.default && p.available);

            if (hunyuan3d) {
              setSelectedPipeline('hunyuan3d');
              console.log('Auto-selected Hunyuan3D-2 pipeline (preferred for availability)');
            } else if (trellis) {
              setSelectedPipeline('trellis');
              console.log('Auto-selected Trellis pipeline (fallback)');
            } else if (defaultPipeline) {
              setSelectedPipeline(data.default);
              console.log(`Auto-selected default pipeline: ${data.default}`);
            }
          } else {
            console.log(`Keeping current pipeline selection: ${currentlySelected}`);
          }
        }
      } catch (error) {
        console.error('Error loading available pipelines:', error);
      }
    };

    loadModelPreference();
    loadAvailablePipelines();
  }, []);

  const handleImageUpload = useCallback(async (file: File) => {
    setUploadError(null);
    setModelError(null);
    setModelUrl(null);
    setVideoUrl(null);

    // Display the original image preview initially
    const reader = new FileReader();
    reader.onload = (e) => {
      setSelectedImage(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    // Upload the file to the server (basic upload without shadow removal)
    try {
      const formData = new FormData();
      formData.append('image', file);

      // Disable shadow removal in upload - it will be handled by the preview controls
      formData.append('enable_shadow_removal', 'false');
      formData.append('shadow_removal_preset', 'balanced');

      const response = await fetch(`${API_URL}/upload`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to upload image');
      }

      const data = await response.json();

      // Update the image preview with the processed image if available
      if (data.processed_image_data) {
        setSelectedImage(`data:image/png;base64,${data.processed_image_data}`);
      }

      setUploadedFile({
        image_id: data.image_id,
        filename: data.filename
      });

      // Clear shadow preview when new image is uploaded
      setDelighterPreviewImage(null);
    } catch (err) {
      console.error('Error uploading image:', err);
      setUploadError('Failed to upload image. Please try again.');
    }
  }, []);

  const handleTextChange = useCallback((prompt: string) => {
    setTextPrompt(prompt);

    // Create or clear the "uploaded file" based on prompt content
    if (prompt.trim()) {
      setUploadedFile({
        image_id: 'text-' + Date.now(),
        filename: 'text-prompt.txt'
      });
    } else {
      setUploadedFile(null);
    }
  }, []);

  const handlePromptSelect = useCallback((prompt: string) => {
    setTextPrompt(prompt);
    // Also trigger the same logic as handleTextChange
    if (prompt.trim()) {
      setUploadedFile({
        image_id: 'text-' + Date.now(),
        filename: 'text-prompt.txt'
      });
    } else {
      setUploadedFile(null);
    }
  }, []);

  const handleTextSubmit = useCallback((prompt: string) => {
    setTextPrompt(prompt);
    setUploadError(null);
    setModelError(null);
    setModelUrl(null);
    setVideoUrl(null);

    // Create a mock "uploaded file" for text prompts
    setUploadedFile({
      image_id: 'text-' + Date.now(),
      filename: 'text-prompt.txt'
    });
  }, []);

  const handleModeSwitch = useCallback((mode: InputMode) => {
    setInputMode(mode);
    // Clear current input when switching modes
    setSelectedImage(null);
    setTextPrompt('');
    setUploadedFile(null);
    setUploadError(null);
    setModelError(null);
    setModelUrl(null);
    setVideoUrl(null);
    setDelighterPreviewImage(null);
  }, []);

  // Agisoft De-Lighter handlers
  const handleDelighterEnabledChange = useCallback((enabled: boolean) => {
    setDelighterEnabled(enabled);
  }, []);

  const handleDelighterQualityChange = useCallback((quality: string) => {
    setDelighterQuality(quality);
  }, []);

  const handleImageModelChange = useCallback(async (model: string) => {
    setSelectedImageModel(model);

    // Update the backend with the new model preference
    try {
      await fetch('/api/config/model-preference', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          preferred_model: model
        }),
      });
    } catch (error) {
      console.error('Error updating model preference:', error);
    }
  }, []);

  const handlePipelineChange = useCallback((pipeline: string) => {
    console.log(`Pipeline selection changed from ${selectedPipeline} to ${pipeline}`);

    // Validate that the selected pipeline is available
    const selectedPipelineData = availablePipelines.find(p => p.id === pipeline);
    if (selectedPipelineData && selectedPipelineData.available) {
      setSelectedPipeline(pipeline);
      console.log(`Successfully switched to pipeline: ${pipeline} (${selectedPipelineData.name})`);
    } else {
      console.warn(`Pipeline ${pipeline} is not available. Available pipelines:`,
                   availablePipelines.filter(p => p.available).map(p => `${p.id} (${p.name})`));
      // Don't change the selection if the pipeline is not available
    }
  }, [selectedPipeline, availablePipelines]);

  const handleSampleSelect = useCallback(async (imageUrl: string) => {
    setIsLoadingSample(true);
    setUploadError(null);
    setModelError(null);
    setModelUrl(null);
    setVideoUrl(null);

    // Display the sample image immediately
    setSelectedImage(imageUrl);

    try {
      // Use a proxy approach to avoid CORS issues
      const proxyUrl = `${API_URL}/proxy-image`;
      const response = await fetch(proxyUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ imageUrl }),
      });

      if (!response.ok) {
        // Fallback: try direct fetch
        const directResponse = await fetch(imageUrl, { mode: 'cors' });
        const blob = await directResponse.blob();
        const file = new File([blob], 'sample-image.jpg', { type: blob.type });
        await handleImageUpload(file);
      } else {
        const blob = await response.blob();
        const file = new File([blob], 'sample-image.jpg', { type: blob.type });
        await handleImageUpload(file);
      }
    } catch (err) {
      console.error('Error loading sample image:', err);
      // Don't show error in model preview, just log it
      console.warn('Sample image loading failed, but image is still displayed');
      // Set a minimal uploadedFile so generation can still work with the displayed image
      setUploadedFile({
        image_id: 'sample-' + Date.now(),
        filename: 'sample-image.jpg'
      });
    } finally {
      setIsLoadingSample(false);
    }
  }, [handleImageUpload]);

  const handleGenerate = useCallback(async () => {
    if (!uploadedFile) return;

    setIsProcessing(true);
    setModelError(null);
    setGenerationStats(null); // Clear previous stats

    const startTime = Date.now();

    try {
      if (inputMode === 'text') {
        // Real text-to-3D generation
        console.log('=== TEXT-TO-3D GENERATION REQUEST ===');
        console.log('Selected pipeline:', selectedPipeline);
        console.log('Available pipelines:', availablePipelines);
        console.log('Text prompt:', textPrompt);
        console.log('Image model:', selectedImageModel);
        console.log('Settings:', settings);
        console.log('=====================================');

        const response = await fetch(`${API_URL}/generate-text-to-3d`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            prompt: textPrompt,
            settings: {
              ...settings,
              // Include Agisoft De-Lighter settings for backend processing
              enable_delighter: delighterEnabled,
              delighter_quality: delighterQuality
            },
            session_id: uploadedFile.image_id,  // Use the existing session ID
            image_model: selectedImageModel,  // Include the selected image generation model
            pipeline: selectedPipeline  // Include the selected 3D pipeline
          }),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          if (errorData.error && errorData.error.includes('timed out')) {
            throw new Error('Text-to-image generation timed out. This may happen during first-time model download. Please try again.');
          }
          throw new Error(errorData.error || 'Failed to generate 3D model from text');
        }

        const data = await response.json();

        if (data.success && data.files) {
          // Set the model URL for the 3D viewer
          // Use GLB if available, otherwise use PLY (Gaussian splat)
          const modelFile = data.files.glb || data.files.gaussian;
          const videoFile = data.files.video;

          if (modelFile) {
            setModelUrl(`http://localhost:5000/api/files/${modelFile}`);
          }
          if (videoFile) {
            setVideoUrl(`http://localhost:5000/api/files/${videoFile}`);
          }

          if (!modelFile && !videoFile) {
            throw new Error('No output files generated');
          }

          // Collect generation stats
          const totalTime = (Date.now() - startTime) / 1000;
          setGenerationStats({
            generationMode: 'text-to-3d',
            prompt: data.original_prompt || textPrompt,
            enhancedPrompt: data.enhanced_prompt,
            imageModel: selectedImageModel,
            settings: {
              ...settings,
              enable_delighter: delighterEnabled,
              delighter_quality: delighterQuality
            },
            fileInfo: {
              type: data.mesh_stats?.file_format || (modelFile?.includes('.glb') ? 'glb' : 'ply'),
              size: data.mesh_stats?.file_size || data.file_size,
              vertices: data.mesh_stats?.vertices || 0,
              faces: data.mesh_stats?.faces || 0,
              polygons: data.mesh_stats?.polygons || data.mesh_stats?.faces || 0,
              textureResolution: data.mesh_stats?.texture_resolution || null,
              hasTextures: data.mesh_stats?.has_textures || false
            },
            timing: {
              totalTime: totalTime
            }
          });

          console.log('Text-to-3D generation completed successfully');
          console.log('Generated from prompt:', data.original_prompt);
          console.log('Enhanced prompt used:', data.enhanced_prompt);
        } else {
          throw new Error(data.error || 'Unknown error');
        }
        return;
      }

      // Existing image-to-3D generation
      console.log('=== IMAGE-TO-3D GENERATION REQUEST ===');
      console.log('Selected pipeline:', selectedPipeline);
      console.log('Available pipelines:', availablePipelines);
      console.log('Settings:', settings);
      console.log('=====================================');

      const response = await fetch(`${API_URL}/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          image_id: uploadedFile.image_id,
          filename: uploadedFile.filename,
          settings: {
            ...settings,
            // Include Agisoft De-Lighter settings for backend processing
            enable_delighter: delighterEnabled,
            delighter_quality: delighterQuality
          },
          pipeline: selectedPipeline  // Include the selected 3D pipeline
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate 3D model');
      }

      const data = await response.json();

      if (data.success && data.files) {
        // Set the model URL for the 3D viewer
        // Use GLB if available, otherwise use PLY (Gaussian splat)
        const modelFile = data.files.glb || data.files.gaussian;
        const videoFile = data.files.video;

        if (modelFile) {
          setModelUrl(`http://localhost:5000/api/files/${modelFile}`);
        }
        if (videoFile) {
          setVideoUrl(`http://localhost:5000/api/files/${videoFile}`);
        }

        if (!modelFile && !videoFile) {
          throw new Error('No output files generated');
        }

        // Collect generation stats
        const totalTime = (Date.now() - startTime) / 1000;
        setGenerationStats({
          generationMode: 'image-to-3d',
          settings: {
            ...settings,
            enable_delighter: delighterEnabled,
            delighter_quality: delighterQuality
          },
          fileInfo: {
            type: data.mesh_stats?.file_format || (modelFile?.includes('.glb') ? 'glb' : 'ply'),
            size: data.mesh_stats?.file_size || data.file_size,
            vertices: data.mesh_stats?.vertices || 0,
            faces: data.mesh_stats?.faces || 0,
            polygons: data.mesh_stats?.polygons || data.mesh_stats?.faces || 0,
            textureResolution: data.mesh_stats?.texture_resolution || null,
            hasTextures: data.mesh_stats?.has_textures || false
          },
          timing: {
            totalTime: totalTime
          }
        });
      } else {
        throw new Error(data.error || 'Unknown error');
      }
    } catch (err) {
      console.error('Error generating model:', err);
      setModelError('Failed to generate 3D model. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  }, [uploadedFile, settings, inputMode, textPrompt]);

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
  };

  return (
    <div className={`h-screen overflow-hidden ${isDarkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
      <header className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <div className="max-w-7xl mx-auto px-4 py-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between">
            {/* Navigation Menu */}
            <div className="flex items-center gap-8">
              {/* Logo/Brand - Now clickable */}
              <button
                onClick={() => setNavigationMode('generator')}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 ${
                  navigationMode === 'generator'
                    ? isDarkMode
                      ? 'bg-blue-600 text-white shadow-lg'
                      : 'bg-blue-600 text-white shadow-lg'
                    : isDarkMode
                    ? 'text-gray-300 hover:text-white hover:bg-gray-700'
                    : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                <Cube className="w-6 h-6" />
                <span className="text-lg font-bold">3D Model Generator</span>
              </button>

              {/* Projects Menu Item */}
              <button
                onClick={() => setNavigationMode('projects')}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 ${
                  navigationMode === 'projects'
                    ? isDarkMode
                      ? 'bg-blue-600 text-white shadow-lg'
                      : 'bg-blue-600 text-white shadow-lg'
                    : isDarkMode
                    ? 'text-gray-300 hover:text-white hover:bg-gray-700'
                    : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                <FolderOpen className="w-5 h-5" />
                <span className="font-medium">Projects</span>
              </button>
            </div>

            {/* Right Side Controls */}
            <div className="flex items-center gap-2">
              <button
                onClick={() => setIsModelManagerOpen(true)}
                className={`p-2 rounded-lg transition-colors ${
                  isDarkMode
                    ? 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
                }`}
                title="Dependency Manager v2.0"
              >
                <Database className="w-5 h-5" />
              </button>
              <button
                onClick={() => setIsSettingsOpen(true)}
                className={`p-2 rounded-lg transition-colors ${
                  isDarkMode
                    ? 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
                }`}
                title="Text-to-Image Settings"
              >
                <SettingsIcon className="w-5 h-5" />
              </button>
              <button
                onClick={toggleTheme}
                className={`p-2 rounded-lg transition-colors ${
                  isDarkMode
                    ? 'bg-gray-700 hover:bg-gray-600 text-yellow-300'
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
                }`}
                title="Toggle Theme"
              >
                {isDarkMode ? <Sun className="w-5 h-5" /> : <Moon className="w-5 h-5" />}
              </button>
            </div>
          </div>
        </div>
      </header>

      <main className="h-[calc(100vh-80px)] px-4 py-8 sm:px-6 lg:px-8 overflow-auto">
        {navigationMode === 'generator' ? (
          <div className="h-full flex flex-col lg:flex-row gap-6 max-w-full mx-auto">
          {/* Left Panel - Fixed Width */}
          <div className="w-full lg:w-80 lg:flex-shrink-0 space-y-6">
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-6 shadow-lg`}>
              {/* Mode Toggle */}
              <div className="flex items-center gap-2 mb-4">
                <div className={`flex rounded-lg p-1 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <button
                    onClick={() => handleModeSwitch('image')}
                    className={`flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      inputMode === 'image'
                        ? isDarkMode
                          ? 'bg-blue-500 text-white'
                          : 'bg-blue-600 text-white'
                        : isDarkMode
                        ? 'text-gray-300 hover:text-white'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    <ImageIcon className="w-4 h-4" />
                    Image
                  </button>
                  <button
                    onClick={() => handleModeSwitch('text')}
                    className={`flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      inputMode === 'text'
                        ? isDarkMode
                          ? 'bg-blue-500 text-white'
                          : 'bg-blue-600 text-white'
                        : isDarkMode
                        ? 'text-gray-300 hover:text-white'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    <TypeIcon className="w-4 h-4" />
                    Text
                  </button>
                </div>
              </div>

              {/* Dynamic Header */}
              <div className="flex items-center gap-2 mb-4">
                {inputMode === 'image' ? (
                  <ImageIcon className={`w-5 h-5 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`} />
                ) : (
                  <TypeIcon className={`w-5 h-5 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`} />
                )}
                <h2 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {inputMode === 'image' ? 'Input Image' : 'Text Prompt'}
                </h2>
              </div>
              {/* Conditional Input Area */}
              {inputMode === 'image' ? (
                selectedImage ? (
                  <div className="relative">
                    <img
                      src={delighterPreviewImage || selectedImage}
                      alt="Selected"
                      className="w-full h-[400px] object-contain rounded-lg"
                    />
                    {delighterPreviewImage && (
                      <div className="absolute bottom-2 left-2 bg-purple-600 text-white px-2 py-1 rounded text-xs font-medium">
                        De-Lighter Applied
                      </div>
                    )}
                    {isLoadingSample && (
                      <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg">
                        <div className="text-white text-center">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
                          <p>Processing sample image...</p>
                        </div>
                      </div>
                    )}
                    <button
                      onClick={() => {
                        setSelectedImage(null);
                        setUploadedFile(null);
                        setUploadError(null);
                        setDelighterPreviewImage(null);
                      }}
                      className={`absolute top-2 right-2 ${
                        isDarkMode ? 'bg-gray-700' : 'bg-white'
                      } rounded-full p-1 shadow-lg`}
                    >
                      ×
                    </button>
                  </div>
                ) : (
                  <ImageUpload onImageUpload={handleImageUpload} isDarkMode={isDarkMode} />
                )
              ) : (
                <TextInput
                  onTextSubmit={handleTextSubmit}
                  onTextChange={handleTextChange}
                  isDarkMode={isDarkMode}
                  isProcessing={isProcessing}
                  selectedModel={selectedImageModel}
                  onModelChange={handleImageModelChange}
                />
              )}
              {uploadError && (
                <div className={`mt-2 p-2 rounded ${isDarkMode ? 'bg-red-900 text-red-200' : 'bg-red-100 text-red-700'}`}>
                  <p className="text-sm">{uploadError}</p>
                </div>
              )}
            </div>

            {/* Agisoft De-Lighter Texture Enhancement - Show for both modes */}
            <AgisoftDelighter
              isDarkMode={isDarkMode}
              isEnabled={delighterEnabled}
              onEnabledChange={handleDelighterEnabledChange}
              currentQuality={delighterQuality}
              onQualityChange={handleDelighterQualityChange}
            />

            <Settings
              onSettingsChange={setSettings}
              isDarkMode={isDarkMode}
              initialSettings={settings}
              selectedPipeline={selectedPipeline}
              onPipelineChange={handlePipelineChange}
              availablePipelines={availablePipelines}
            />

            <div className="flex gap-4">
              <button
                onClick={handleGenerate}
                disabled={
                  (inputMode === 'image' && !selectedImage) ||
                  (inputMode === 'text' && !textPrompt.trim()) ||
                  isProcessing
                }
                className={`flex-1 flex items-center justify-center gap-2 px-4 py-2 rounded-lg text-white ${
                  (inputMode === 'image' && !selectedImage) ||
                  (inputMode === 'text' && !textPrompt.trim()) ||
                  isProcessing
                    ? 'bg-gray-400'
                    : isDarkMode
                    ? 'bg-blue-500 hover:bg-blue-600'
                    : 'bg-blue-600 hover:bg-blue-700'
                }`}
              >
                <Play className="w-5 h-5" />
                {isProcessing ? 'Processing...' : 'Generate Model'}
              </button>

              <a
                href={modelUrl || '#'}
                download
                disabled={!modelUrl}
                className={`flex items-center justify-center gap-2 px-4 py-2 rounded-lg text-white ${
                  !modelUrl
                    ? 'bg-gray-400'
                    : isDarkMode
                    ? 'bg-green-500 hover:bg-green-600'
                    : 'bg-green-600 hover:bg-green-700'
                } disabled:bg-gray-400 ${!modelUrl ? 'pointer-events-none' : ''}`}
              >
                <Download className="w-5 h-5" />
                Download {modelUrl ? (modelUrl.includes('.glb') ? 'GLB' : modelUrl.includes('.ply') ? 'PLY' : '3D Model') : '3D Model'}
              </a>
            </div>
          </div>

          {/* Center Panel - Flexible Width */}
          <div className="flex-1 min-w-0 flex flex-col gap-6">
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-6 shadow-lg flex-1 flex flex-col min-h-0`}>
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <Cube className={`w-5 h-5 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`} />
                  <h2 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>3D Preview</h2>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => setShowTextured(true)}
                    className={`px-3 py-1 rounded ${
                      showTextured
                        ? isDarkMode
                          ? 'bg-blue-500 text-white'
                          : 'bg-blue-600 text-white'
                        : isDarkMode
                        ? 'bg-gray-700 text-gray-300'
                        : 'bg-gray-100 text-gray-700'
                    }`}
                  >
                    Textured
                  </button>
                  <button
                    onClick={() => setShowTextured(false)}
                    className={`px-3 py-1 rounded ${
                      !showTextured
                        ? isDarkMode
                          ? 'bg-blue-500 text-white'
                          : 'bg-blue-600 text-white'
                        : isDarkMode
                        ? 'bg-gray-700 text-gray-300'
                        : 'bg-gray-100 text-gray-700'
                    }`}
                  >
                    Wireframe
                  </button>
                </div>
              </div>
              <div className="flex-1 min-h-[400px]">
                {modelError ? (
                  <div className={`flex flex-col items-center justify-center h-full ${isDarkMode ? 'text-red-400' : 'text-red-600'}`}>
                    <AlertCircle className="w-12 h-12 mb-4" />
                    <p className="text-center">{modelError}</p>
                  </div>
                ) : modelUrl ? (
                  <ModelViewer
                    isTextured={showTextured}
                    isDarkMode={isDarkMode}
                    modelUrl={modelUrl}
                    generationStats={generationStats}
                  />
                ) : videoUrl ? (
                  <video
                    src={videoUrl}
                    className="w-full h-full object-contain"
                    autoPlay
                    loop
                    muted
                    controls
                  />
                ) : (
                  <ModelViewer
                    isTextured={showTextured}
                    isDarkMode={isDarkMode}
                    modelUrl={null}
                    generationStats={generationStats}
                  />
                )}
              </div>
            </div>

            {/* Progress Bar - Compact when visible */}
            {isProcessing && (
              <div className="flex-shrink-0">
                <ProgressBar
                  isVisible={isProcessing}
                  isDarkMode={isDarkMode}
                  sessionId={uploadedFile?.image_id}
                  generationMode={inputMode === 'image' ? 'image-to-3d' : 'text-to-3d'}
                  selectedPipeline={selectedPipeline}
                  delighterEnabled={delighterEnabled}
                  onComplete={() => {
                    console.log('Generation completed!');
                  }}
                  onError={(error) => {
                    console.error('Generation error:', error);
                    setModelError(error);
                    setIsProcessing(false);
                  }}
                />
              </div>
            )}
          </div>

          {/* Right Panel - Fixed Width */}
          <div className="w-full lg:w-72 lg:flex-shrink-0 space-y-6">
            {inputMode === 'image' ? (
              <SampleGallery
                onSampleSelect={handleSampleSelect}
                isDarkMode={isDarkMode}
              />
            ) : inputMode === 'text' ? (
              <PromptCrafter
                onPromptSelect={handlePromptSelect}
                isDarkMode={isDarkMode}
                isProcessing={isProcessing}
              />
            ) : null}
          </div>
        </div>
        ) : (
          /* Projects View */
          <div className="h-full max-w-full mx-auto">
            <ProjectsGallery
              isDarkMode={isDarkMode}
            />
          </div>
        )}
      </main>

      {/* Settings Modal */}
      <SettingsModal
        isOpen={isSettingsOpen}
        onClose={() => setIsSettingsOpen(false)}
        isDarkMode={isDarkMode}
      />

      {/* Dependency Manager Modal */}
      <DependencyManager
        isOpen={isModelManagerOpen}
        onClose={() => setIsModelManagerOpen(false)}
        isDarkMode={isDarkMode}
      />
    </div>
  );
}

export default App;