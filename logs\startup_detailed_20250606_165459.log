2025-06-06 16:54:59,396 |     INFO | startup_detailed | log_system_info:68 | ================================================================================
2025-06-06 16:54:59,396 |     INFO | startup_detailed | log_system_info:69 | 3D AI STUDIO STARTUP - DETAILED LOGGING
2025-06-06 16:54:59,396 |     INFO | startup_detailed | log_system_info:70 | ================================================================================
2025-06-06 16:54:59,406 |     INFO | startup_detailed | log_system_info:94 | SYSTEM INFORMATION:
2025-06-06 16:54:59,406 |     INFO | startup_detailed | log_system_info:96 |   timestamp: 2025-06-06T16:54:59.397000
2025-06-06 16:54:59,406 |     INFO | startup_detailed | log_system_info:96 |   platform: Windows-11-10.0.26100-SP0
2025-06-06 16:54:59,408 |     INFO | startup_detailed | log_system_info:96 |   python_version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
2025-06-06 16:54:59,408 |     INFO | startup_detailed | log_system_info:96 |   python_executable: E:\3D AI Studio\.venv\Scripts\python.exe
2025-06-06 16:54:59,408 |     INFO | startup_detailed | log_system_info:96 |   working_directory: E:\3D AI Studio
2025-06-06 16:54:59,408 |     INFO | startup_detailed | log_system_info:96 |   cpu_count: 88
2025-06-06 16:54:59,408 |     INFO | startup_detailed | log_system_info:96 |   memory_total: 79.9 GB
2025-06-06 16:54:59,409 |     INFO | startup_detailed | log_system_info:96 |   disk_free: 529.7 GB
2025-06-06 16:54:59,409 |     INFO | startup_detailed | log_system_info:96 |   torch_version: 2.6.0+cu124
2025-06-06 16:54:59,409 |     INFO | startup_detailed | log_system_info:96 |   cuda_available: True
2025-06-06 16:54:59,409 |     INFO | startup_detailed | log_system_info:96 |   cuda_device: NVIDIA GeForce RTX 3060
2025-06-06 16:54:59,410 |     INFO | startup_detailed | log_system_info:96 |   cuda_memory: 12.0 GB
2025-06-06 16:54:59,410 |     INFO | startup_detailed | log_stage:104 | ============================================================
2025-06-06 16:54:59,410 |     INFO | startup_detailed | log_stage:105 | STAGE: APPLICATION STARTUP
2025-06-06 16:54:59,410 |     INFO | startup_detailed | log_stage:107 | DETAILS: 3D AI Studio with Hunyuan3D-2 Integration
2025-06-06 16:54:59,411 |     INFO | startup_detailed | log_stage:108 | ============================================================
2025-06-06 16:54:59,411 |     INFO | startup_detailed | log_step:112 | STEP: Server Initialization - STARTING
2025-06-06 16:54:59,411 |     INFO | startup_detailed | log_stage:104 | ============================================================
2025-06-06 16:54:59,412 |     INFO | startup_detailed | log_stage:105 | STAGE: DEPENDENCY CHECKS
2025-06-06 16:54:59,412 |     INFO | startup_detailed | log_stage:107 | DETAILS: Verifying critical dependencies
2025-06-06 16:54:59,412 |     INFO | startup_detailed | log_stage:108 | ============================================================
2025-06-06 16:54:59,415 |     INFO | startup_detailed | log_step:112 | STEP: Startup dependency checks - RUNNING
2025-06-06 16:55:11,820 |     INFO | startup_detailed | log_success:123 | SUCCESS: Startup dependency checks completed
2025-06-06 16:55:11,820 |     INFO | startup_detailed | log_stage:104 | ============================================================
2025-06-06 16:55:11,820 |     INFO | startup_detailed | log_stage:105 | STAGE: HUNYUAN3D-2 PIPELINE LOADING
2025-06-06 16:55:11,820 |     INFO | startup_detailed | log_stage:107 | DETAILS: Loading integrated pipeline with texture generation
2025-06-06 16:55:11,820 |     INFO | startup_detailed | log_stage:108 | ============================================================
2025-06-06 16:55:11,821 |     INFO | startup_detailed | log_step:112 | STEP: Hunyuan3D-2 pipeline initialization - STARTING
2025-06-06 16:55:11,821 |     INFO | startup_detailed | log_step:112 | STEP: Importing Hunyuan3D integrated pipeline - IMPORTING
2025-06-06 16:55:11,823 |     INFO | startup_detailed | log_success:123 | SUCCESS: Hunyuan3D integrated pipeline module imported
2025-06-06 16:55:11,823 |     INFO | startup_detailed | log_success:123 | SUCCESS: Hunyuan3D integrated pipeline instance created
2025-06-06 16:55:11,823 |     INFO | startup_detailed | log_step:112 | STEP: Loading pipeline components - LOADING
2025-06-06 16:55:11,824 |     INFO | startup_detailed | log_pipeline_loading:157 | PIPELINE: Hunyuan3D-2 - STARTING
2025-06-06 16:55:11,824 |     INFO | startup_detailed | log_progress:119 | PROGRESS: Hunyuan3D: Stage 1/6: Setting up Hunyuan3D-2 environment...
2025-06-06 16:55:11,825 |     INFO | startup_detailed | log_progress:119 | PROGRESS: Hunyuan3D: Stage 2/6: Loading background removal model...
2025-06-06 16:55:11,825 |     INFO | startup_detailed | log_step:112 | STEP: Background removal model - LOADING
2025-06-06 16:55:16,390 |     INFO | startup_detailed | log_model_loading:163 | MODEL: Background Removal - LOADED (4.6s)
2025-06-06 16:55:16,390 |    DEBUG | startup_detailed | log_model_loading:164 | Model path: hy3dgen.rembg
2025-06-06 16:55:16,390 |     INFO | startup_detailed | log_progress:119 | PROGRESS: Hunyuan3D: Stage 3/6: Loading shape generation pipeline...
2025-06-06 16:55:16,390 |     INFO | startup_detailed | log_step:112 | STEP: Shape generation imports - IMPORTING
2025-06-06 16:55:27,604 |    DEBUG | startup_detailed | log_import_attempt:142 | IMPORT SUCCESS: hy3dgen.shapegen
2025-06-06 16:55:27,605 |     INFO | startup_detailed | log_step:112 | STEP: Shape generation imports - COMPLETED (11.2s)
2025-06-06 16:55:27,605 |     INFO | startup_detailed | log_step:112 | STEP: Main shape generation pipeline - LOADING
2025-06-06 16:55:27,605 |     INFO | startup_detailed | log_model_loading:163 | MODEL: Hunyuan3D DiT - FAILED (0.0s)
2025-06-06 16:55:27,605 |    DEBUG | startup_detailed | log_model_loading:164 | Model path: tencent/Hunyuan3D-2mini/hunyuan3d-dit-v2-mini-turbo
2025-06-06 16:55:37,277 |     INFO | startup_detailed | log_model_loading:163 | MODEL: Hunyuan3D DiT - LOADED (9.7s)
2025-06-06 16:55:37,277 |    DEBUG | startup_detailed | log_model_loading:164 | Model path: tencent/Hunyuan3D-2mini/hunyuan3d-dit-v2-mini-turbo
2025-06-06 16:55:37,277 |     INFO | startup_detailed | log_step:112 | STEP: FlashVDM optimization - ENABLING
2025-06-06 16:55:39,708 |     INFO | startup_detailed | log_success:123 | SUCCESS: FlashVDM enabled with dmc algorithm
2025-06-06 16:55:39,709 |     INFO | startup_detailed | log_pipeline_loading:157 | PIPELINE: Shape Generation - LOADED (23.3s)
2025-06-06 16:55:39,709 |     INFO | startup_detailed | log_progress:119 | PROGRESS: Hunyuan3D: Stage 4/6: Loading mesh processing workers...
2025-06-06 16:55:39,709 |     INFO | startup_detailed | log_progress:119 | PROGRESS: Hunyuan3D: Stage 5/6: Loading texture generation pipeline...
2025-06-06 16:55:39,710 |     INFO | startup_detailed | log_step:112 | STEP: Texture generation pipeline - STARTING
2025-06-06 16:55:39,710 |     INFO | startup_detailed | log_step:112 | STEP: Texture generation imports - IMPORTING
2025-06-06 16:55:39,861 |    DEBUG | startup_detailed | log_import_attempt:142 | IMPORT SUCCESS: hy3dgen.texgen
2025-06-06 16:55:39,862 |     INFO | startup_detailed | log_step:112 | STEP: Texture model cache check - Checking Resources\Hunyuan3D2_WinPortable\HuggingFaceHub\models--tencent--Hunyuan3D-2\snapshots
2025-06-06 16:55:39,862 |     INFO | startup_detailed | log_step:112 | STEP: Texture model paths - Delight: True, Paint: True
2025-06-06 16:55:39,863 |     INFO | startup_detailed | log_progress:119 | PROGRESS: Hunyuan3D: Loading texture delight model...
2025-06-06 16:55:39,863 |     INFO | startup_detailed | log_step:112 | STEP: Texture pipeline loading - LOADING MODELS
2025-06-06 16:55:39,863 |     INFO | startup_detailed | log_step:112 | STEP: Using local texture model path - Resources\Hunyuan3D2_WinPortable\HuggingFaceHub\models--tencent--Hunyuan3D-2\snapshots\34e28261f71c32975727be8db0eace439a280f82
2025-06-06 16:55:40,215 |    ERROR | startup_detailed | log_error:134 | ERROR: Texture generation pipeline failed after 0.5s
2025-06-06 16:55:40,215 |    ERROR | startup_detailed | log_error:136 | Exception: 'SafeTensorFile' object has no attribute 'get_slice'
2025-06-06 16:55:40,227 |    ERROR | startup_detailed | log_error:137 | Traceback (most recent call last):
  File "E:\3D AI Studio\hunyuan3d_integrated_pipeline.py", line 252, in load_pipeline_components
    self.texgen_worker = Hunyuan3DPaintPipeline.from_pretrained(local_texgen_path)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\texgen\pipelines.py", line 86, in from_pretrained
    return cls(Hunyuan3DTexGenConfig(delight_model_path, multiview_model_path))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\texgen\pipelines.py", line 95, in __init__
    self.load_models()
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\texgen\pipelines.py", line 101, in load_models
    self.models['delight_model'] = Light_Shadow_Remover(self.config)
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\texgen\utils\dehighlight_utils.py", line 28, in __init__
    pipeline = StableDiffusionInstructPix2PixPipeline.from_pretrained(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\pipelines\pipeline_utils.py", line 961, in from_pretrained
    loaded_sub_model = load_sub_model(
                       ^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\pipelines\pipeline_loading_utils.py", line 777, in load_sub_model
    loaded_sub_model = load_method(os.path.join(cached_folder, name), **loading_kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\transformers\modeling_utils.py", line 309, in _wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\transformers\modeling_utils.py", line 4574, in from_pretrained
    ) = cls._load_pretrained_model(
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\transformers\modeling_utils.py", line 4833, in _load_pretrained_model
    load_state_dict(checkpoint_files[0], map_location="meta", weights_only=weights_only).keys()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\transformers\modeling_utils.py", line 541, in load_state_dict
    _slice = f.get_slice(k)
             ^^^^^^^^^^^
AttributeError: 'SafeTensorFile' object has no attribute 'get_slice'

2025-06-06 16:55:40,235 |     INFO | startup_detailed | log_progress:119 | PROGRESS: Hunyuan3D: Stage 6/6: Loading text-to-image pipeline...
2025-06-06 16:55:40,236 |     INFO | startup_detailed | log_step:112 | STEP: Text-to-image pipeline - LOADING
2025-06-06 16:55:41,700 |  WARNING | startup_detailed | log_warning:127 | WARNING: Text-to-image pipeline failed: 'SafeTensorFile' object has no attribute 'get_slice'
2025-06-06 16:55:41,704 |     INFO | startup_detailed | log_progress:119 | PROGRESS: Hunyuan3D: Finalizing pipeline setup...
2025-06-06 16:55:41,705 |     INFO | startup_detailed | log_progress:119 | PROGRESS: Hunyuan3D: Hunyuan3D-2 pipeline ready!
2025-06-06 16:55:41,705 |     INFO | startup_detailed | log_success:123 | SUCCESS: Hunyuan3D-2 pipeline loaded in 29.9s
2025-06-06 16:55:41,705 |     INFO | startup_detailed | log_pipeline_loading:157 | PIPELINE: Hunyuan3D-2 - COMPLETE (29.9s)
2025-06-06 16:55:41,705 |     INFO | startup_detailed | log_dependency_check:152 | DEPENDENCY: Texture Generation - MISSING
2025-06-06 16:55:41,705 |     INFO | startup_detailed | log_dependency_check:152 | DEPENDENCY: Text-to-Image - MISSING
2025-06-06 16:55:41,706 |     INFO | startup_detailed | log_success:123 | SUCCESS: Hunyuan3D-2 pipeline set as global
2025-06-06 16:55:41,706 |     INFO | startup_detailed | log_step:112 | STEP: Initializing modular pipeline system - STARTING
2025-06-06 16:55:43,963 |     INFO | startup_detailed | log_success:123 | SUCCESS: Trellis pipeline available for modular system
2025-06-06 16:55:43,964 |     INFO | startup_detailed | log_success:123 | SUCCESS: Modular pipeline system initialized
2025-06-06 16:55:43,965 |     INFO | startup_detailed | log_stage:104 | ============================================================
2025-06-06 16:55:43,965 |     INFO | startup_detailed | log_stage:105 | STAGE: HUNYUAN3D-2 COMPLETED
2025-06-06 16:55:43,965 |     INFO | startup_detailed | log_stage:107 | DETAILS: Total time: 32.1s
2025-06-06 16:55:43,965 |     INFO | startup_detailed | log_stage:108 | ============================================================
2025-06-06 16:55:43,965 |     INFO | startup_detailed | log_stage:104 | ============================================================
2025-06-06 16:55:43,965 |     INFO | startup_detailed | log_stage:105 | STAGE: FLASK SERVER STARTING
2025-06-06 16:55:43,965 |     INFO | startup_detailed | log_stage:107 | DETAILS: Total startup time: 44.6s
2025-06-06 16:55:43,966 |     INFO | startup_detailed | log_stage:108 | ============================================================
2025-06-06 16:55:43,966 |     INFO | startup_detailed | log_startup_complete:181 | ================================================================================
2025-06-06 16:55:43,966 |     INFO | startup_detailed | log_startup_complete:183 | STARTUP COMPLETE: SUCCESSFUL (Total time: 44.6s)
2025-06-06 16:55:43,966 |     INFO | startup_detailed | log_startup_complete:184 | ================================================================================
2025-06-06 16:55:43,966 |     INFO | startup_detailed | log_startup_complete:187 | Detailed logs saved to: logs\startup_detailed_20250606_165459.log
2025-06-06 16:55:43,966 |     INFO | startup_detailed | log_success:123 | SUCCESS: 3D AI Studio ready to serve requests
