2025-06-06 15:21:45,270 |     INFO | startup_detailed | log_system_info:68 | ================================================================================
2025-06-06 15:21:45,271 |     INFO | startup_detailed | log_system_info:69 | 3D AI STUDIO STARTUP - DETAILED LOGGING
2025-06-06 15:21:45,271 |     INFO | startup_detailed | log_system_info:70 | ================================================================================
2025-06-06 15:22:02,273 |     INFO | startup_detailed | log_system_info:94 | SYSTEM INFORMATION:
2025-06-06 15:22:02,273 |     INFO | startup_detailed | log_system_info:96 |   timestamp: 2025-06-06T15:22:02.085886
2025-06-06 15:22:02,274 |     INFO | startup_detailed | log_system_info:96 |   platform: Windows-11-10.0.26100-SP0
2025-06-06 15:22:02,275 |     INFO | startup_detailed | log_system_info:96 |   python_version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
2025-06-06 15:22:02,276 |     INFO | startup_detailed | log_system_info:96 |   python_executable: E:\3D AI Studio\.venv\Scripts\python.exe
2025-06-06 15:22:02,276 |     INFO | startup_detailed | log_system_info:96 |   working_directory: E:\3D AI Studio
2025-06-06 15:22:02,276 |     INFO | startup_detailed | log_system_info:96 |   cpu_count: 88
2025-06-06 15:22:02,276 |     INFO | startup_detailed | log_system_info:96 |   memory_total: 79.9 GB
2025-06-06 15:22:02,277 |     INFO | startup_detailed | log_system_info:96 |   disk_free: 529.8 GB
2025-06-06 15:22:02,277 |     INFO | startup_detailed | log_system_info:96 |   torch_version: 2.5.1+cu124
2025-06-06 15:22:02,278 |     INFO | startup_detailed | log_system_info:96 |   cuda_available: True
2025-06-06 15:22:02,278 |     INFO | startup_detailed | log_system_info:96 |   cuda_device: NVIDIA GeForce RTX 3060
2025-06-06 15:22:02,278 |     INFO | startup_detailed | log_system_info:96 |   cuda_memory: 12.0 GB
2025-06-06 15:23:12,458 |     INFO | startup_detailed | log_pipeline_loading:157 | PIPELINE: Hunyuan3D-2 - STARTING
2025-06-06 15:23:12,459 |     INFO | startup_detailed | log_step:112 | STEP: Background removal model - LOADING
2025-06-06 15:23:14,143 |     INFO | startup_detailed | log_model_loading:163 | MODEL: Background Removal - LOADED (1.7s)
2025-06-06 15:23:14,143 |    DEBUG | startup_detailed | log_model_loading:164 | Model path: hy3dgen.rembg
2025-06-06 15:23:14,143 |     INFO | startup_detailed | log_step:112 | STEP: Shape generation imports - IMPORTING
2025-06-06 15:23:14,144 |    DEBUG | startup_detailed | log_import_attempt:142 | IMPORT SUCCESS: hy3dgen.shapegen
2025-06-06 15:23:14,144 |     INFO | startup_detailed | log_step:112 | STEP: Shape generation imports - COMPLETED (0.0s)
2025-06-06 15:23:14,144 |     INFO | startup_detailed | log_step:112 | STEP: Main shape generation pipeline - LOADING
2025-06-06 15:23:14,145 |     INFO | startup_detailed | log_model_loading:163 | MODEL: Hunyuan3D DiT - FAILED (0.0s)
2025-06-06 15:23:14,145 |    DEBUG | startup_detailed | log_model_loading:164 | Model path: tencent/Hunyuan3D-2mini/hunyuan3d-dit-v2-mini-turbo
2025-06-06 15:23:28,431 |     INFO | startup_detailed | log_model_loading:163 | MODEL: Hunyuan3D DiT - LOADED (14.3s)
2025-06-06 15:23:28,432 |    DEBUG | startup_detailed | log_model_loading:164 | Model path: tencent/Hunyuan3D-2mini/hunyuan3d-dit-v2-mini-turbo
2025-06-06 15:23:28,433 |     INFO | startup_detailed | log_step:112 | STEP: FlashVDM optimization - ENABLING
2025-06-06 15:23:31,668 |     INFO | startup_detailed | log_success:123 | SUCCESS: FlashVDM enabled with dmc algorithm
2025-06-06 15:23:31,670 |     INFO | startup_detailed | log_pipeline_loading:157 | PIPELINE: Shape Generation - LOADED (17.5s)
2025-06-06 15:23:31,671 |     INFO | startup_detailed | log_step:112 | STEP: Texture generation pipeline - STARTING
2025-06-06 15:23:31,671 |     INFO | startup_detailed | log_step:112 | STEP: Texture generation imports - IMPORTING
2025-06-06 15:23:31,671 |    DEBUG | startup_detailed | log_import_attempt:142 | IMPORT SUCCESS: hy3dgen.texgen
2025-06-06 15:23:31,672 |     INFO | startup_detailed | log_step:112 | STEP: Texture model cache check - Checking Resources\Hunyuan3D2_WinPortable\HuggingFaceHub\models--tencent--Hunyuan3D-2\snapshots
2025-06-06 15:23:31,673 |     INFO | startup_detailed | log_step:112 | STEP: Texture model paths - Delight: True, Paint: True
2025-06-06 15:23:31,674 |     INFO | startup_detailed | log_step:112 | STEP: Texture pipeline loading - LOADING MODELS
2025-06-06 15:23:31,675 |     INFO | startup_detailed | log_step:112 | STEP: Using local texture model path - Resources\Hunyuan3D2_WinPortable\HuggingFaceHub\models--tencent--Hunyuan3D-2\snapshots\34e28261f71c32975727be8db0eace439a280f82
2025-06-06 15:23:32,036 |    ERROR | startup_detailed | log_error:134 | ERROR: Texture generation pipeline failed after 0.4s
2025-06-06 15:23:32,037 |    ERROR | startup_detailed | log_error:136 | Exception: DLL load failed while importing custom_rasterizer_kernel: The specified procedure could not be found.
2025-06-06 15:23:32,076 |    ERROR | startup_detailed | log_error:137 | Traceback (most recent call last):
  File "E:\3D AI Studio\hunyuan3d_integrated_pipeline.py", line 252, in load_pipeline_components
    self.texgen_worker = Hunyuan3DPaintPipeline.from_pretrained(local_texgen_path)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\texgen\pipelines.py", line 86, in from_pretrained
    return cls(Hunyuan3DTexGenConfig(delight_model_path, multiview_model_path))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\texgen\pipelines.py", line 91, in __init__
    self.render = MeshRender(
                  ^^^^^^^^^^^
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\texgen\differentiable_renderer\mesh_render.py", line 145, in __init__
    import custom_rasterizer as cr
  File "E:\3D AI Studio\.venv\Lib\site-packages\custom_rasterizer\__init__.py", line 22, in <module>
    from .render import *
  File "E:\3D AI Studio\.venv\Lib\site-packages\custom_rasterizer\render.py", line 15, in <module>
    import custom_rasterizer_kernel
ImportError: DLL load failed while importing custom_rasterizer_kernel: The specified procedure could not be found.

2025-06-06 15:23:32,087 |     INFO | startup_detailed | log_step:112 | STEP: Text-to-image pipeline - LOADING
2025-06-06 15:23:34,842 |  WARNING | startup_detailed | log_warning:127 | WARNING: Text-to-image pipeline failed: 'SafeTensorFile' object has no attribute 'get_slice'
