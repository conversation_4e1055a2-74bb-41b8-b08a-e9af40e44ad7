"""
CUDA Environment Setup for Hunyuan3D-2
Sets up PyTorch with CUDA support for RTX 3060
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description=""):
    """Run a command and return success status."""
    print(f"\n{'='*60}")
    print(f"RUNNING: {description}")
    print(f"COMMAND: {command}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print("SUCCESS!")
        if result.stdout:
            print("STDOUT:", result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"ERROR: {e}")
        if e.stdout:
            print("STDOUT:", e.stdout)
        if e.stderr:
            print("STDERR:", e.stderr)
        return False

def check_cuda_availability():
    """Check if CUDA is available on the system."""
    try:
        result = subprocess.run("nvidia-smi", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print("NVIDIA GPU detected:")
            print(result.stdout)
            return True
        else:
            print("NVIDIA GPU not detected or nvidia-smi not available")
            return False
    except Exception as e:
        print(f"Error checking CUDA: {e}")
        return False

def setup_cuda_pytorch():
    """Install PyTorch with CUDA support."""
    print("\n" + "="*80)
    print("SETTING UP CUDA PYTORCH FOR HUNYUAN3D-2")
    print("="*80)
    
    # Check CUDA availability
    if not check_cuda_availability():
        print("WARNING: NVIDIA GPU not detected. Proceeding anyway...")
    
    # Uninstall existing PyTorch packages
    print("\nStep 1: Uninstalling existing PyTorch packages...")
    pytorch_packages = [
        "torch", "torchvision", "torchaudio", "xformers", 
        "flash-attn", "triton"
    ]
    
    for package in pytorch_packages:
        run_command(f"pip uninstall {package} -y", f"Uninstalling {package}")
    
    # Install PyTorch with CUDA 12.4 (matching WinPortable)
    print("\nStep 2: Installing PyTorch with CUDA 12.4...")
    pytorch_install_cmd = (
        "pip install torch==2.6.0+cu124 torchvision==0.21.0+cu124 torchaudio==2.6.0+cu124 "
        "--index-url https://download.pytorch.org/whl/cu124"
    )
    
    if not run_command(pytorch_install_cmd, "Installing PyTorch with CUDA 12.4"):
        print("ERROR: Failed to install PyTorch with CUDA")
        return False
    
    # Install xFormers for CUDA
    print("\nStep 3: Installing xFormers for CUDA...")
    xformers_cmd = "pip install xformers==0.0.29.post1 --index-url https://download.pytorch.org/whl/cu124"
    
    if not run_command(xformers_cmd, "Installing xFormers for CUDA"):
        print("WARNING: xFormers installation failed, trying alternative...")
        # Try without specific version
        run_command("pip install xformers", "Installing xFormers (latest)")
    
    # Install Flash Attention
    print("\nStep 4: Installing Flash Attention...")
    flash_attn_cmd = "pip install flash-attn --no-build-isolation"
    
    if not run_command(flash_attn_cmd, "Installing Flash Attention"):
        print("WARNING: Flash Attention installation failed")
        print("This may cause performance issues but should not prevent basic functionality")
    
    # Install additional CUDA-related packages
    print("\nStep 5: Installing additional CUDA packages...")
    additional_packages = [
        "ninja",  # Required for compilation
        "triton",  # GPU kernels
    ]
    
    for package in additional_packages:
        run_command(f"pip install {package}", f"Installing {package}")
    
    # Verify installation
    print("\nStep 6: Verifying CUDA PyTorch installation...")
    verify_script = '''
import torch
import sys

print("="*60)
print("CUDA PYTORCH VERIFICATION")
print("="*60)
print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")

if torch.cuda.is_available():
    print(f"CUDA version: {torch.version.cuda}")
    print(f"GPU count: {torch.cuda.device_count()}")
    for i in range(torch.cuda.device_count()):
        print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
        print(f"GPU {i} memory: {torch.cuda.get_device_properties(i).total_memory / 1024**3:.1f} GB")
else:
    print("CUDA not available!")

# Test xFormers
try:
    import xformers
    print(f"xFormers version: {xformers.__version__}")
    print("xFormers: Available")
except ImportError as e:
    print(f"xFormers: Not available ({e})")

# Test Flash Attention
try:
    import flash_attn
    print(f"Flash Attention: Available")
except ImportError as e:
    print(f"Flash Attention: Not available ({e})")

print("="*60)
'''
    
    if not run_command(f'python -c "{verify_script}"', "Verifying CUDA setup"):
        print("ERROR: CUDA verification failed")
        return False
    
    print("\n" + "="*80)
    print("CUDA PYTORCH SETUP COMPLETED!")
    print("="*80)
    print("Your environment now has:")
    print("✓ PyTorch with CUDA 12.4 support")
    print("✓ xFormers for efficient attention")
    print("✓ Flash Attention (if installation succeeded)")
    print("✓ NVIDIA GPU support for Hunyuan3D-2")
    print("\nYou can now run Hunyuan3D-2 with GPU acceleration!")
    
    return True

if __name__ == "__main__":
    print("CUDA Environment Setup for Hunyuan3D-2")
    print("This will install PyTorch with CUDA support for your RTX 3060")
    print("\nPress Enter to continue or Ctrl+C to cancel...")
    
    try:
        input()
        success = setup_cuda_pytorch()
        if success:
            print("\nSetup completed successfully!")
        else:
            print("\nSetup completed with some errors. Check the output above.")
    except KeyboardInterrupt:
        print("\nSetup cancelled by user.")
        sys.exit(1)
