#!/usr/bin/env python3
"""
Hunyuan3D-2 Native Client
Communicates with the native Hunyuan3D-2 Gradio server for text-to-3D and image-to-3D generation
"""

import os
import time
import json
import requests
import tempfile
import shutil
from typing import Dict, Any, Optional, Callable
from pathlib import Path
import logging

# Set up logging
try:
    from utils.logging_system import get_logger
    logger = get_logger()
except ImportError:
    import logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

class Hunyuan3DNativeClient:
    """Client for communicating with the native Hunyuan3D-2 Gradio server."""
    
    def __init__(self, server_url: str = "http://localhost:8080"):
        self.server_url = server_url
        self.session = requests.Session()
        self.session.timeout = 30
        
    def is_server_available(self) -> bool:
        """Check if the Hunyuan3D-2 server is available."""
        try:
            response = self.session.get(f"{self.server_url}/", timeout=10)
            return response.status_code in [200, 404, 422]
        except requests.RequestException:
            return False
    
    def wait_for_server(self, timeout: int = 300, progress_callback: Optional[Callable] = None) -> bool:
        """Wait for the server to become available."""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if self.is_server_available():
                logger.info("Hunyuan3D-2 native server is ready", component="HUNYUAN3D_CLIENT")
                if progress_callback:
                    progress_callback("Hunyuan3D-2 server is ready")
                return True
            
            if progress_callback:
                elapsed = int(time.time() - start_time)
                progress_callback(f"Waiting for Hunyuan3D-2 server ({elapsed}s/{timeout}s)...")
            
            time.sleep(5)
        
        logger.error(f"Hunyuan3D-2 server failed to become available within {timeout} seconds", component="HUNYUAN3D_CLIENT")
        return False
    
    def generate_text_to_3d(self, prompt: str, settings: Optional[Dict] = None, 
                           session_id: Optional[str] = None, 
                           progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        Generate 3D model from text using the native Hunyuan3D-2 server.
        
        Args:
            prompt: Text prompt for generation
            settings: Generation settings
            session_id: Session ID for progress tracking
            progress_callback: Progress callback function
            
        Returns:
            Dictionary with generation results
        """
        if not self.is_server_available():
            return {
                'success': False,
                'error': 'Hunyuan3D-2 native server is not available'
            }
        
        # Default settings
        default_settings = {
            'seed': 1234,
            'octree_resolution': 128,
            'num_inference_steps': 5,
            'guidance_scale': 5.0,
            'enable_texture': False,
            'face_count': 40000
        }
        
        if settings:
            default_settings.update(settings)
        
        try:
            if progress_callback:
                progress_callback("Starting text-to-3D generation...")
            
            logger.info(f"Starting text-to-3D generation with prompt: {prompt[:100]}...", component="HUNYUAN3D_CLIENT")
            
            # Prepare the request data for Gradio API
            data = {
                "data": [
                    prompt,  # text prompt
                    default_settings.get('seed', 1234),  # seed
                    default_settings.get('octree_resolution', 128),  # octree resolution
                    default_settings.get('num_inference_steps', 5),  # inference steps
                    default_settings.get('guidance_scale', 5.0),  # guidance scale
                    default_settings.get('enable_texture', False),  # enable texture
                    default_settings.get('face_count', 40000)  # face count
                ],
                "fn_index": 0,  # Assuming text-to-3D is the first function
                "session_hash": session_id or "default_session"
            }
            
            if progress_callback:
                progress_callback("Sending request to Hunyuan3D-2 server...")
            
            # Send request to Gradio API
            response = self.session.post(
                f"{self.server_url}/api/predict",
                json=data,
                timeout=1800  # 30 minutes timeout for generation
            )
            
            if response.status_code != 200:
                logger.error(f"Hunyuan3D-2 server returned status {response.status_code}: {response.text}", component="HUNYUAN3D_CLIENT")
                return {
                    'success': False,
                    'error': f'Server returned status {response.status_code}: {response.text}'
                }
            
            result = response.json()
            
            if progress_callback:
                progress_callback("Processing generation results...")
            
            # Extract the generated files from the result
            if 'data' in result and result['data']:
                output_data = result['data']
                
                # The output should contain file paths or file objects
                generated_files = []
                for item in output_data:
                    if isinstance(item, dict) and 'name' in item:
                        # This is a file object from Gradio
                        generated_files.append(item['name'])
                    elif isinstance(item, str) and os.path.exists(item):
                        # This is a file path
                        generated_files.append(item)
                
                if generated_files:
                    # Copy files to our output directory
                    output_dir = os.path.join(os.path.dirname(__file__), 'output')
                    os.makedirs(output_dir, exist_ok=True)
                    
                    final_files = []
                    for file_path in generated_files:
                        if os.path.exists(file_path):
                            filename = os.path.basename(file_path)
                            dest_path = os.path.join(output_dir, f"hunyuan3d_text_{session_id or 'default'}_{filename}")
                            shutil.copy2(file_path, dest_path)
                            final_files.append(dest_path)
                    
                    logger.info(f"Text-to-3D generation completed successfully. Generated {len(final_files)} files", component="HUNYUAN3D_CLIENT")
                    
                    return {
                        'success': True,
                        'output_files': final_files,
                        'primary_output': final_files[0] if final_files else None,
                        'type': 'mesh',
                        'format': 'glb'
                    }
                else:
                    logger.warning("No generated files found in server response", component="HUNYUAN3D_CLIENT")
                    return {
                        'success': False,
                        'error': 'No generated files found in server response',
                        'raw_response': result
                    }
            else:
                logger.error(f"Invalid response format from server: {result}", component="HUNYUAN3D_CLIENT")
                return {
                    'success': False,
                    'error': 'Invalid response format from server',
                    'raw_response': result
                }
                
        except requests.Timeout:
            logger.error("Text-to-3D generation timed out", component="HUNYUAN3D_CLIENT")
            return {
                'success': False,
                'error': 'Generation timed out'
            }
        except Exception as e:
            logger.error(f"Error during text-to-3D generation: {e}", component="HUNYUAN3D_CLIENT")
            return {
                'success': False,
                'error': f'Generation failed: {str(e)}'
            }
    
    def generate_image_to_3d(self, image_path: str, settings: Optional[Dict] = None,
                            session_id: Optional[str] = None,
                            progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        Generate 3D model from image using the native Hunyuan3D-2 server.
        
        Args:
            image_path: Path to input image
            settings: Generation settings
            session_id: Session ID for progress tracking
            progress_callback: Progress callback function
            
        Returns:
            Dictionary with generation results
        """
        if not self.is_server_available():
            return {
                'success': False,
                'error': 'Hunyuan3D-2 native server is not available'
            }
        
        if not os.path.exists(image_path):
            return {
                'success': False,
                'error': f'Input image not found: {image_path}'
            }
        
        # Default settings
        default_settings = {
            'seed': 1234,
            'octree_resolution': 128,
            'num_inference_steps': 5,
            'guidance_scale': 5.0,
            'enable_texture': False,
            'face_count': 40000
        }
        
        if settings:
            default_settings.update(settings)
        
        try:
            if progress_callback:
                progress_callback("Starting image-to-3D generation...")
            
            logger.info(f"Starting image-to-3D generation with image: {image_path}", component="HUNYUAN3D_CLIENT")
            
            # Upload the image file
            with open(image_path, 'rb') as f:
                files = {'file': f}
                upload_response = self.session.post(
                    f"{self.server_url}/upload",
                    files=files,
                    timeout=60
                )
            
            if upload_response.status_code != 200:
                return {
                    'success': False,
                    'error': f'Failed to upload image: {upload_response.text}'
                }
            
            upload_result = upload_response.json()
            uploaded_file_path = upload_result.get('file_path') or upload_result.get('name')
            
            if progress_callback:
                progress_callback("Image uploaded, starting 3D generation...")
            
            # Prepare the request data for image-to-3D
            data = {
                "data": [
                    uploaded_file_path,  # uploaded image
                    default_settings.get('seed', 1234),  # seed
                    default_settings.get('octree_resolution', 128),  # octree resolution
                    default_settings.get('num_inference_steps', 5),  # inference steps
                    default_settings.get('guidance_scale', 5.0),  # guidance scale
                    default_settings.get('enable_texture', False),  # enable texture
                    default_settings.get('face_count', 40000)  # face count
                ],
                "fn_index": 1,  # Assuming image-to-3D is the second function
                "session_hash": session_id or "default_session"
            }
            
            # Send request to Gradio API
            response = self.session.post(
                f"{self.server_url}/api/predict",
                json=data,
                timeout=1800  # 30 minutes timeout for generation
            )
            
            if response.status_code != 200:
                logger.error(f"Hunyuan3D-2 server returned status {response.status_code}: {response.text}", component="HUNYUAN3D_CLIENT")
                return {
                    'success': False,
                    'error': f'Server returned status {response.status_code}: {response.text}'
                }
            
            result = response.json()
            
            if progress_callback:
                progress_callback("Processing generation results...")
            
            # Process results similar to text-to-3D
            if 'data' in result and result['data']:
                output_data = result['data']
                
                generated_files = []
                for item in output_data:
                    if isinstance(item, dict) and 'name' in item:
                        generated_files.append(item['name'])
                    elif isinstance(item, str) and os.path.exists(item):
                        generated_files.append(item)
                
                if generated_files:
                    output_dir = os.path.join(os.path.dirname(__file__), 'output')
                    os.makedirs(output_dir, exist_ok=True)
                    
                    final_files = []
                    for file_path in generated_files:
                        if os.path.exists(file_path):
                            filename = os.path.basename(file_path)
                            dest_path = os.path.join(output_dir, f"hunyuan3d_image_{session_id or 'default'}_{filename}")
                            shutil.copy2(file_path, dest_path)
                            final_files.append(dest_path)
                    
                    logger.info(f"Image-to-3D generation completed successfully. Generated {len(final_files)} files", component="HUNYUAN3D_CLIENT")
                    
                    return {
                        'success': True,
                        'output_files': final_files,
                        'primary_output': final_files[0] if final_files else None,
                        'type': 'mesh',
                        'format': 'glb'
                    }
                else:
                    return {
                        'success': False,
                        'error': 'No generated files found in server response',
                        'raw_response': result
                    }
            else:
                return {
                    'success': False,
                    'error': 'Invalid response format from server',
                    'raw_response': result
                }
                
        except requests.Timeout:
            logger.error("Image-to-3D generation timed out", component="HUNYUAN3D_CLIENT")
            return {
                'success': False,
                'error': 'Generation timed out'
            }
        except Exception as e:
            logger.error(f"Error during image-to-3D generation: {e}", component="HUNYUAN3D_CLIENT")
            return {
                'success': False,
                'error': f'Generation failed: {str(e)}'
            }


class Hunyuan3DNativeClientWrapper:
    """Wrapper to provide compatibility with the expected pipeline interface."""

    def __init__(self):
        self.client = Hunyuan3DNativeClient()

    def is_available(self) -> bool:
        """Check if the native server is available."""
        return self.client.is_server_available()

    def is_server_available(self) -> bool:
        """Check if the native server is available."""
        return self.client.is_server_available()

    def generate_text_to_3d(self, prompt: str, settings: Optional[Dict] = None,
                           session_id: Optional[str] = None,
                           progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """Generate 3D model from text."""
        return self.client.generate_text_to_3d(prompt, settings, session_id, progress_callback)

    def generate_image_to_3d(self, image_path: str, settings: Optional[Dict] = None,
                            session_id: Optional[str] = None,
                            progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """Generate 3D model from image."""
        return self.client.generate_image_to_3d(image_path, settings, session_id, progress_callback)

    def process_image(self, image_path: str, settings: Optional[Dict] = None,
                     session_id: Optional[str] = None) -> Dict[str, Any]:
        """Process image to 3D (compatibility method)."""
        return self.client.generate_image_to_3d(image_path, settings, session_id)


# Global client instance
native_client = Hunyuan3DNativeClientWrapper()

def get_native_client() -> Hunyuan3DNativeClientWrapper:
    """Get the global native client instance."""
    return native_client
