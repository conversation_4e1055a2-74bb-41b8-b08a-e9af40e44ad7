import React, { useState, useEffect } from 'react';
import { 
  X, 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  RefreshCw, 
  Activity,
  Server,
  Database,
  Cpu,
  Zap,
  Globe,
  HardDrive,
  Monitor
} from 'lucide-react';

interface SystemStatus {
  id: string;
  name: string;
  description: string;
  status: 'ready' | 'warning' | 'error' | 'loading';
  details: string;
  lastChecked: string;
  uptime?: string;
  version?: string;
  dependencies?: string[];
}

interface ApplicationHealthSystemProps {
  isOpen: boolean;
  onClose: () => void;
  isDarkMode: boolean;
}

export function ApplicationHealthSystem({ isOpen, onClose, isDarkMode }: ApplicationHealthSystemProps) {
  const [systems, setSystems] = useState<SystemStatus[]>([]);
  const [loading, setLoading] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);
  const [overallHealth, setOverallHealth] = useState<'healthy' | 'warning' | 'critical'>('healthy');

  // Load system status on component mount and when opened
  useEffect(() => {
    if (isOpen) {
      loadSystemStatus();
      // Auto-refresh every 30 seconds when open
      const interval = setInterval(loadSystemStatus, 30000);
      return () => clearInterval(interval);
    }
  }, [isOpen]);

  const loadSystemStatus = async () => {
    setLoading(true);
    try {
      // Load main application status
      const mainAppStatus = await checkMainApplicationStatus();
      
      // Load pipeline statuses
      const pipelineStatuses = await checkPipelineStatuses();
      
      // Load background services status
      const servicesStatus = await checkBackgroundServicesStatus();
      
      // Load system resources
      const resourcesStatus = await checkSystemResources();

      const allSystems = [
        mainAppStatus,
        ...pipelineStatuses,
        ...servicesStatus,
        resourcesStatus
      ];

      setSystems(allSystems);
      setLastRefresh(new Date());
      
      // Calculate overall health
      const hasErrors = allSystems.some(s => s.status === 'error');
      const hasWarnings = allSystems.some(s => s.status === 'warning');
      
      if (hasErrors) {
        setOverallHealth('critical');
      } else if (hasWarnings) {
        setOverallHealth('warning');
      } else {
        setOverallHealth('healthy');
      }
      
    } catch (error) {
      console.error('Error loading system status:', error);
    } finally {
      setLoading(false);
    }
  };

  const checkMainApplicationStatus = async (): Promise<SystemStatus> => {
    try {
      const response = await fetch('/api/health');
      const data = await response.json();
      
      return {
        id: 'main_app',
        name: 'Main Application',
        description: '3D AI Studio Core Application',
        status: response.ok ? 'ready' : 'error',
        details: response.ok ? 'All core services operational' : 'Core application issues detected',
        lastChecked: new Date().toLocaleTimeString(),
        version: '2.0.0',
        dependencies: ['Flask', 'Python', 'Node.js']
      };
    } catch (error) {
      return {
        id: 'main_app',
        name: 'Main Application',
        description: '3D AI Studio Core Application',
        status: 'error',
        details: 'Unable to connect to main application',
        lastChecked: new Date().toLocaleTimeString()
      };
    }
  };

  const checkPipelineStatuses = async (): Promise<SystemStatus[]> => {
    try {
      const response = await fetch('/api/pipelines/available');
      const data = await response.json();
      
      const pipelines: SystemStatus[] = [];
      
      // Check Trellis pipeline
      const trellisPipeline = data.pipelines?.find((p: any) => p.id === 'trellis');
      pipelines.push({
        id: 'trellis',
        name: 'Trellis Pipeline',
        description: 'High-quality 3D generation with structured latent diffusion',
        status: trellisPipeline?.available ? 'ready' : 'error',
        details: trellisPipeline?.available ? 'Ready for 3D generation' : 'Pipeline not available',
        lastChecked: new Date().toLocaleTimeString(),
        dependencies: ['PyTorch', 'Transformers', 'Trimesh']
      });
      
      // Check Hunyuan3D-2 pipeline
      const hunyuan3dPipeline = data.pipelines?.find((p: any) => p.id === 'hunyuan3d');
      pipelines.push({
        id: 'hunyuan3d',
        name: 'Hunyuan3D-2 Native',
        description: 'Fast 3D generation with built-in text-to-image',
        status: hunyuan3dPipeline?.available ? 'ready' : 'warning',
        details: hunyuan3dPipeline?.available ? 'Native server running' : 'Native server not available',
        lastChecked: new Date().toLocaleTimeString(),
        dependencies: ['Native Server', 'Gradio Client', 'CUDA (Optional)']
      });
      
      return pipelines;
    } catch (error) {
      return [
        {
          id: 'trellis',
          name: 'Trellis Pipeline',
          description: 'High-quality 3D generation',
          status: 'error',
          details: 'Unable to check pipeline status',
          lastChecked: new Date().toLocaleTimeString()
        },
        {
          id: 'hunyuan3d',
          name: 'Hunyuan3D-2 Native',
          description: 'Fast 3D generation',
          status: 'error',
          details: 'Unable to check pipeline status',
          lastChecked: new Date().toLocaleTimeString()
        }
      ];
    }
  };

  const checkBackgroundServicesStatus = async (): Promise<SystemStatus[]> => {
    try {
      const response = await fetch('/api/background-services/status');
      const data = await response.json();
      
      const services: SystemStatus[] = [];
      
      // Check Hunyuan3D native service
      if (data.services?.hunyuan3d_native) {
        const service = data.services.hunyuan3d_native;
        services.push({
          id: 'hunyuan3d_service',
          name: 'Hunyuan3D-2 Service',
          description: 'Background service for native 3D generation',
          status: service.status === 'running' ? 'ready' : service.status === 'stopped' ? 'warning' : 'error',
          details: service.status === 'running' ? `Running (PID: ${service.pid})` : 'Service not running',
          lastChecked: new Date().toLocaleTimeString(),
          uptime: service.uptime
        });
      }
      
      return services;
    } catch (error) {
      return [{
        id: 'background_services',
        name: 'Background Services',
        description: 'System background services',
        status: 'error',
        details: 'Unable to check service status',
        lastChecked: new Date().toLocaleTimeString()
      }];
    }
  };

  const checkSystemResources = async (): Promise<SystemStatus> => {
    try {
      const response = await fetch('/api/system/resources');
      const data = await response.json();
      
      // Determine status based on resource usage
      let status: 'ready' | 'warning' | 'error' = 'ready';
      let details = 'System resources optimal';
      
      if (data.memory_usage > 90) {
        status = 'error';
        details = 'High memory usage detected';
      } else if (data.memory_usage > 75) {
        status = 'warning';
        details = 'Moderate memory usage';
      }
      
      return {
        id: 'system_resources',
        name: 'System Resources',
        description: 'CPU, Memory, and Disk usage',
        status,
        details,
        lastChecked: new Date().toLocaleTimeString()
      };
    } catch (error) {
      return {
        id: 'system_resources',
        name: 'System Resources',
        description: 'CPU, Memory, and Disk usage',
        status: 'warning',
        details: 'Unable to check system resources',
        lastChecked: new Date().toLocaleTimeString()
      };
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ready':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'loading':
        return <RefreshCw className="w-5 h-5 text-blue-500 animate-spin" />;
      default:
        return <Activity className="w-5 h-5 text-gray-500" />;
    }
  };

  const getSystemIcon = (systemId: string) => {
    switch (systemId) {
      case 'main_app':
        return <Monitor className="w-5 h-5" />;
      case 'trellis':
        return <Cpu className="w-5 h-5" />;
      case 'hunyuan3d':
        return <Zap className="w-5 h-5" />;
      case 'hunyuan3d_service':
        return <Server className="w-5 h-5" />;
      case 'system_resources':
        return <HardDrive className="w-5 h-5" />;
      default:
        return <Database className="w-5 h-5" />;
    }
  };

  const getOverallHealthColor = () => {
    switch (overallHealth) {
      case 'healthy':
        return 'text-green-500';
      case 'warning':
        return 'text-yellow-500';
      case 'critical':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  };

  const getOverallHealthText = () => {
    switch (overallHealth) {
      case 'healthy':
        return 'All Systems Operational';
      case 'warning':
        return 'Some Issues Detected';
      case 'critical':
        return 'Critical Issues Detected';
      default:
        return 'Checking Systems...';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className={`w-full max-w-4xl h-5/6 ${isDarkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'} rounded-lg shadow-xl flex flex-col`}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <Activity className={`w-6 h-6 ${getOverallHealthColor()}`} />
            <div>
              <h2 className="text-xl font-semibold">Application Health System</h2>
              <p className={`text-sm ${getOverallHealthColor()}`}>
                {getOverallHealthText()}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={loadSystemStatus}
              disabled={loading}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              title="Refresh Status"
            >
              <RefreshCw className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
            </button>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Status Summary */}
        <div className="p-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Last updated: {lastRefresh?.toLocaleTimeString() || 'Never'}
              </div>
            </div>
            <div className="flex items-center space-x-4 text-sm">
              <div className="flex items-center space-x-1">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>{systems.filter(s => s.status === 'ready').length} Ready</span>
              </div>
              <div className="flex items-center space-x-1">
                <AlertTriangle className="w-4 h-4 text-yellow-500" />
                <span>{systems.filter(s => s.status === 'warning').length} Warning</span>
              </div>
              <div className="flex items-center space-x-1">
                <XCircle className="w-4 h-4 text-red-500" />
                <span>{systems.filter(s => s.status === 'error').length} Error</span>
              </div>
            </div>
          </div>
        </div>

        {/* Systems List */}
        <div className="flex-1 overflow-y-auto p-4">
          {loading && systems.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-2" />
                <p>Checking system status...</p>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              {systems.map((system) => (
                <div
                  key={system.id}
                  className={`p-4 rounded-lg border ${
                    isDarkMode ? 'border-gray-700 bg-gray-750' : 'border-gray-200 bg-gray-50'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      <div className={`p-2 rounded-lg ${
                        isDarkMode ? 'bg-gray-700' : 'bg-white'
                      }`}>
                        {getSystemIcon(system.id)}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <h3 className="font-semibold">{system.name}</h3>
                          {getStatusIcon(system.status)}
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                          {system.description}
                        </p>
                        <p className="text-sm mt-2">
                          {system.details}
                        </p>
                        {system.dependencies && (
                          <div className="mt-2">
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              Dependencies: {system.dependencies.join(', ')}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="text-right text-xs text-gray-500 dark:text-gray-400">
                      <div>Last checked</div>
                      <div>{system.lastChecked}</div>
                      {system.uptime && (
                        <div className="mt-1">
                          <div>Uptime</div>
                          <div>{system.uptime}</div>
                        </div>
                      )}
                      {system.version && (
                        <div className="mt-1">
                          <div>Version</div>
                          <div>{system.version}</div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
