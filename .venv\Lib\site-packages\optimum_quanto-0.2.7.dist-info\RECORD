optimum/quanto/__init__.py,sha256=gdrO-yRf2P8kE9J6A3zzDmK6nAnrIfG08aspeCdbwA8,764
optimum/quanto/__pycache__/__init__.cpython-312.pyc,,
optimum/quanto/__pycache__/calibrate.cpython-312.pyc,,
optimum/quanto/__pycache__/quantize.cpython-312.pyc,,
optimum/quanto/calibrate.py,sha256=u3jUpuRJNm58vv0VJ7R0W2pVVq3rnKWFggOZaQJIYwM,8353
optimum/quanto/library/README.md,sha256=mIts19nvncgoHuvPvv7vQ8VrrtmuRA_dcx0fdGz8wuo,353
optimum/quanto/library/__init__.py,sha256=YdTDpFITlPyRd_qfjRRgvSeK-5seCDDUz2enwO_Wek0,704
optimum/quanto/library/__pycache__/__init__.cpython-312.pyc,,
optimum/quanto/library/__pycache__/qbytes_mm.cpython-312.pyc,,
optimum/quanto/library/__pycache__/quantize.cpython-312.pyc,,
optimum/quanto/library/__pycache__/unpack.cpython-312.pyc,,
optimum/quanto/library/extensions/README.md,sha256=9MDb0WCG0OFWN-MSZhu-zCfV-mYDiuDKh2-3qARfqYY,1193
optimum/quanto/library/extensions/__init__.py,sha256=zeddi0rA-zEz4cy9toYtkflVlStfq-ltjAJqmcgsAl0,919
optimum/quanto/library/extensions/__pycache__/__init__.cpython-312.pyc,,
optimum/quanto/library/extensions/__pycache__/extension.cpython-312.pyc,,
optimum/quanto/library/extensions/cpp/README.md,sha256=2svPjO_2STlhiOAgi8b9juapKmuuFaEqkirxPRlIAt4,419
optimum/quanto/library/extensions/cpp/__init__.py,sha256=c6_vS1dQ0QhE9WJmF4y9jb638nUDb-jOw1skzvxPUu8,1007
optimum/quanto/library/extensions/cpp/__pycache__/__init__.cpython-312.pyc,,
optimum/quanto/library/extensions/cpp/pybind_module.cpp,sha256=D30uac3yFH-IaIIFgJNK8n_LT4iI2F3oM-1Qn0UgEEY,1175
optimum/quanto/library/extensions/cpp/unpack.cpp,sha256=6E1p2eL-Jp2-LylsNxa4PU_3xn4KF685IqJn44_kygc,1535
optimum/quanto/library/extensions/cpp/unpack.h,sha256=a3_8L7fOqTJEpuyVghyRZ4C_lhEVm0m4DttY6u0LUuM,700
optimum/quanto/library/extensions/cuda/README.md,sha256=ERwvPKNjFcoV2Ohz-BwH0C-pGZDBV1pNPkqGZJnxpBs,437
optimum/quanto/library/extensions/cuda/__init__.py,sha256=vUqwXrwgZyVlE7P4IKH2WMSiS05kSZnEhmpCLKz2k7g,6156
optimum/quanto/library/extensions/cuda/__pycache__/__init__.cpython-312.pyc,,
optimum/quanto/library/extensions/cuda/awq/dequantize.cuh,sha256=hh54c65TRqRYWP7AG9S__wp49l1XXRso810BlxeBu74,3931
optimum/quanto/library/extensions/cuda/awq/v2/gemm_cuda.cu,sha256=KufwXgBXuqqM2JSrgrNWc-m44gu86SBS3SfAee56Jsk,50002
optimum/quanto/library/extensions/cuda/awq/v2/gemm_cuda.h,sha256=jNwJw9FLkFIexCp3fyQuNkMzoui6ecE_uoKPj2wV-LU,156
optimum/quanto/library/extensions/cuda/awq/v2/gemv_cuda.cu,sha256=Gd34JvxRd4qDs9HC1JtovyDrPnti_G3ZHjY--RS5CMM,11626
optimum/quanto/library/extensions/cuda/awq/v2/gemv_cuda.h,sha256=dC_QLYogYNZr221_uI-QeM360pXByTMBWnQvoQJhl3Y,248
optimum/quanto/library/extensions/cuda/awq/v2/semaphore.h,sha256=xcn69YNzzYgJizqjkU2oa9SPlvWhXVUgFIB8dUQuGa4,3886
optimum/quanto/library/extensions/cuda/marlin/COPYRIGHT,sha256=YYtaPzgmAm9ksnhtI4m8Qlig0FAf90VW_7UBbQmydzw,751
optimum/quanto/library/extensions/cuda/marlin/fp8_marlin.cu,sha256=oHpDrDcv99LR1zvMsqOPr9riCfJzKemhPk7OAZCrt2M,51215
optimum/quanto/library/extensions/cuda/marlin/fp8_marlin.cuh,sha256=zSad7zLLCVmbANqt7ncTeWQfmyrTj3LopUIFP6Qf464,495
optimum/quanto/library/extensions/cuda/marlin/gptq_marlin.cuh,sha256=UcFGx3Bjr-R4YJKtKL0ytwkcATiGUXz20OQVimQ8QJI,2051
optimum/quanto/library/extensions/cuda/marlin/gptq_marlin_dtypes.cuh,sha256=QtF4W_zkdoruStR3vrk8ug8N1hp5qaFCKAXLKye0Hg8,1933
optimum/quanto/library/extensions/cuda/marlin/gptq_marlin_repack.cu,sha256=N8l6n1Ll8OM2rJjGxFd10TH5VJOgc1Xh1FWuEmwlvz8,11605
optimum/quanto/library/extensions/cuda/marlin/gptq_marlin_repack.cuh,sha256=v32ghhXX5c_YI9RKCEBKdkHEjYR6YRa21iLZflU0cCk,342
optimum/quanto/library/extensions/cuda/marlin/marlin_cuda.cpp,sha256=8ezIKOIih1th7MabIDH_IkbJbJzN0FQETnwiFGRUXSo,2270
optimum/quanto/library/extensions/cuda/marlin/marlin_cuda.h,sha256=H3Va7c4W7axksNsuWqExe-Vw4VjyE4PMpGD1DWccQRA,922
optimum/quanto/library/extensions/cuda/marlin/marlin_cuda_kernel.cu,sha256=dZzyH4v-tWmhWVVm_Dskz5TUMLYZsI5tgQZU_gK1UC8,35383
optimum/quanto/library/extensions/cuda/marlin/marlin_cuda_kernel.cuh,sha256=xHudD98DOTndCagu5c8p3lTH-y4Xnq_UVSmOWrueCqg,1005
optimum/quanto/library/extensions/cuda/pybind_module.cpp,sha256=xh_1GY1kze-P05hhweWRKtBlLOgGSElWKoikAOeRE2k,1734
optimum/quanto/library/extensions/cuda/unpack.cu,sha256=9wAXBL5lBPT4oKZ_WIq6YMLcE_lC7Z2H4eFfwYd34gQ,2937
optimum/quanto/library/extensions/cuda/unpack.h,sha256=a3_8L7fOqTJEpuyVghyRZ4C_lhEVm0m4DttY6u0LUuM,700
optimum/quanto/library/extensions/extension.py,sha256=dX08--fcXbO_iwqI_gYhkD5i8vyARG9oWyOGfZQqeuM,2671
optimum/quanto/library/extensions/hip/__init__.py,sha256=G5_fLc2ECRzfi5Fp5Yfcny18FTFNoJQZKQgWH8vI810,1014
optimum/quanto/library/extensions/hip/__pycache__/__init__.cpython-312.pyc,,
optimum/quanto/library/extensions/hip/pybind_module.cpp,sha256=CTGYUwlbd1hr46ytUJsgIusWY26Uma4C-9TT-ue8xxk,754
optimum/quanto/library/extensions/hip/unpack.cu,sha256=9wAXBL5lBPT4oKZ_WIq6YMLcE_lC7Z2H4eFfwYd34gQ,2937
optimum/quanto/library/extensions/hip/unpack.h,sha256=a3_8L7fOqTJEpuyVghyRZ4C_lhEVm0m4DttY6u0LUuM,700
optimum/quanto/library/extensions/mps/README.md,sha256=sNRE5aFvCcD5FIRYv9DbkuwgJZE8kg6cpnhVyft8fTI,379
optimum/quanto/library/extensions/mps/__init__.py,sha256=rOlwXu2l_4B4sgXU0C51AGr1sARBWS8Ho05Smv920y4,1011
optimum/quanto/library/extensions/mps/__pycache__/__init__.cpython-312.pyc,,
optimum/quanto/library/extensions/mps/pybind_module.cpp,sha256=CTGYUwlbd1hr46ytUJsgIusWY26Uma4C-9TT-ue8xxk,754
optimum/quanto/library/extensions/mps/unpack.h,sha256=8Wr5Rs7ncGsxUTntmr4yRDluRAnxDlHGvgXe5oBJP9E,710
optimum/quanto/library/extensions/mps/unpack.mm,sha256=SFw-iSlx88EdU1Jpzg6CNvisoxo-Nt06wAaW3eh9420,6396
optimum/quanto/library/qbytes_mm.py,sha256=wEtdtDRX2zI0AEXwDMkoEvoiUaOmoUTZw9ypBtrY7BM,5306
optimum/quanto/library/quantize.py,sha256=knDC3p2-XsixRGUjlRkItgRVK17i3C1bJHrqB80UGU8,3047
optimum/quanto/library/unpack.py,sha256=N55ufQoYGtEY6IZ2hRRvg9a0O8HHCUhacDgq0DWred0,1887
optimum/quanto/models/__init__.py,sha256=TFah2d-NCvr4sP7GcIV7qR64qRCxdLMREh5Pz-rYCVA,1070
optimum/quanto/models/__pycache__/__init__.cpython-312.pyc,,
optimum/quanto/models/__pycache__/diffusers_models.cpython-312.pyc,,
optimum/quanto/models/__pycache__/shared_dict.cpython-312.pyc,,
optimum/quanto/models/__pycache__/transformers_models.cpython-312.pyc,,
optimum/quanto/models/diffusers_models.py,sha256=6MDFGUnRBhiee1oAkyQrY9PB46iwCyngwPu-BwEeq24,8033
optimum/quanto/models/shared_dict.py,sha256=0AZ7hR0TTB1brkA18p8crZj4WjI0IHm30wYHWzd57ac,1761
optimum/quanto/models/transformers_models.py,sha256=zlwr-71wVy-pb9-8xZoaZV-4imSaIlRjTLacKODoBak,8275
optimum/quanto/nn/__init__.py,sha256=vxjA5qmPYJoC1Dr4duPNnza2iiFXyCVbtNSEk88fCX4,702
optimum/quanto/nn/__pycache__/__init__.cpython-312.pyc,,
optimum/quanto/nn/__pycache__/qconv2d.cpython-312.pyc,,
optimum/quanto/nn/__pycache__/qlayernorm.cpython-312.pyc,,
optimum/quanto/nn/__pycache__/qlinear.cpython-312.pyc,,
optimum/quanto/nn/__pycache__/qmodule.cpython-312.pyc,,
optimum/quanto/nn/qconv2d.py,sha256=Zjer9FhLXu9VB52Jl4bMii4VWAW_xT57b9KTqEqscKA,1766
optimum/quanto/nn/qlayernorm.py,sha256=M1WoETA4WoILHq1KIFtKlmsvWuGKdbYzH9JARzzuzWg,1797
optimum/quanto/nn/qlinear.py,sha256=pzJKz9tIPjffu0cdJtUoOLhzVaaUk1jy9BHaUfivppM,1550
optimum/quanto/nn/qmodule.py,sha256=FP46WCjwPEPudv9jiKbeY9EOqgBkr63uTGUwXtrDHmU,12251
optimum/quanto/quantize.py,sha256=MR9QfMPdCuEOIVhbSj4FWoCJbU2q9TufVfRW1d24Wfw,6438
optimum/quanto/subpackage/__init__.py,sha256=i4RJNG8Ba-LlhhERcQsUg7Yr1lGpPsyl4hlKDkz5Okw,631
optimum/quanto/subpackage/__pycache__/__init__.cpython-312.pyc,,
optimum/quanto/subpackage/commands/__init__.py,sha256=LZ_w99FreFTjPX9VRuJ0IEd2pv1D_VLom-FrxCExxI4,627
optimum/quanto/subpackage/commands/__pycache__/__init__.cpython-312.pyc,,
optimum/quanto/subpackage/commands/__pycache__/base.cpython-312.pyc,,
optimum/quanto/subpackage/commands/__pycache__/quantize.cpython-312.pyc,,
optimum/quanto/subpackage/commands/base.py,sha256=PbWvUbra0aSougMAMbkH0932Sqrwr-mHxMlat8ywBd8,1147
optimum/quanto/subpackage/commands/quantize.py,sha256=xVtp-ubA3xqPC3ck9NUF1zNPofHRaA5Yk8FfkSue2CM,4248
optimum/quanto/tensor/__init__.py,sha256=IiTLLqK2oWNry5Jn1mO8k142GU_PfRG0v1w6NLafKEw,813
optimum/quanto/tensor/__pycache__/__init__.cpython-312.pyc,,
optimum/quanto/tensor/__pycache__/core.cpython-312.pyc,,
optimum/quanto/tensor/__pycache__/function.cpython-312.pyc,,
optimum/quanto/tensor/__pycache__/grouped.cpython-312.pyc,,
optimum/quanto/tensor/__pycache__/packed.cpython-312.pyc,,
optimum/quanto/tensor/__pycache__/qbits.cpython-312.pyc,,
optimum/quanto/tensor/__pycache__/qbytes.cpython-312.pyc,,
optimum/quanto/tensor/__pycache__/qtensor.cpython-312.pyc,,
optimum/quanto/tensor/__pycache__/qtype.cpython-312.pyc,,
optimum/quanto/tensor/activations/__init__.py,sha256=vUCkGMZaQCXTvd2Ez_Odlr4pA3Kod59C4oYccUwbKts,50
optimum/quanto/tensor/activations/__pycache__/__init__.cpython-312.pyc,,
optimum/quanto/tensor/activations/__pycache__/qbytes.cpython-312.pyc,,
optimum/quanto/tensor/activations/__pycache__/qbytes_ops.cpython-312.pyc,,
optimum/quanto/tensor/activations/__pycache__/quantization.cpython-312.pyc,,
optimum/quanto/tensor/activations/qbytes.py,sha256=Ip8vYpyEL_ch6-UyhuxaEx6jm0bWKny3yUpqYHHa-HE,3537
optimum/quanto/tensor/activations/qbytes_ops.py,sha256=UAUkyCAEMPKbOrp6TmN9blxNrvJp-vaZ6cjfZmJaOcM,10495
optimum/quanto/tensor/activations/quantization.py,sha256=9jQpxfiBt64FjjGsKjI-HT7iQPuh6A5smMGY7jROVWU,1352
optimum/quanto/tensor/core.py,sha256=7zvEjtAyugpYMNjMl6jOgQCVKVxJ8qHlYU7-5aFQQGY,928
optimum/quanto/tensor/function.py,sha256=hvaXVTkorG_wWR6XCpQ2j_CIRmz4_3ywxMoNQAdX_rQ,2428
optimum/quanto/tensor/grouped.py,sha256=vnDUskhL2QPS661YcIffQjIY0xAx1GbSa0AF0U8PjIM,2183
optimum/quanto/tensor/optimizers/__init__.py,sha256=SIQjQ37MbHSN171dhumgSDOT8l92PPxX6l_CBKTBWJA,789
optimum/quanto/tensor/optimizers/__pycache__/__init__.cpython-312.pyc,,
optimum/quanto/tensor/optimizers/__pycache__/absmax_optimizer.cpython-312.pyc,,
optimum/quanto/tensor/optimizers/__pycache__/affine_optimizer.cpython-312.pyc,,
optimum/quanto/tensor/optimizers/__pycache__/hqq_optimizer.cpython-312.pyc,,
optimum/quanto/tensor/optimizers/__pycache__/max_optimizer.cpython-312.pyc,,
optimum/quanto/tensor/optimizers/__pycache__/optimizer.cpython-312.pyc,,
optimum/quanto/tensor/optimizers/__pycache__/symmetric_optimizer.cpython-312.pyc,,
optimum/quanto/tensor/optimizers/absmax_optimizer.py,sha256=s_eMrm6kaYlmYtX22Y5szoXzcmTAqGH1GML9xzue07M,1279
optimum/quanto/tensor/optimizers/affine_optimizer.py,sha256=vBdDtl8JGkKKDYC3mFTFGQC6o11jReuGeSIpovXeuk4,2473
optimum/quanto/tensor/optimizers/hqq_optimizer.py,sha256=UKlH4EfSc3WXHDlognwAUPIMe8TGwTBz0d5ozdvI4pc,3202
optimum/quanto/tensor/optimizers/max_optimizer.py,sha256=YuhhCi9U1sRodA70XSF93mnANsr5oBuKjYfkkwbYcWM,1312
optimum/quanto/tensor/optimizers/optimizer.py,sha256=8z7JEyEZJWaqfo3QDgV7OUFUmkdZS2JbAmJz4KqkBB4,939
optimum/quanto/tensor/optimizers/symmetric_optimizer.py,sha256=-3IaaWsn3iiyQOegHuEcctDe2qC6WHTzV0fBPABWVng,1344
optimum/quanto/tensor/packed.py,sha256=QAlM4eXQi0a6R18Cigawmt-1x-r49tI3rzdlPu4KKxk,6193
optimum/quanto/tensor/qbits.py,sha256=hnxLb9cIfpT4rtKJL4PQggaahBxV27tWFOr0C3JD0VQ,2190
optimum/quanto/tensor/qbytes.py,sha256=3G6Mb-cwgMdm1tIS55n1u3O88bNPD40W8tQOsQRYyXw,1559
optimum/quanto/tensor/qtensor.py,sha256=GzhnO9oe8X54ryTweiIGpeMJ-PPb6ztz0dBcU1qe14g,3251
optimum/quanto/tensor/qtype.py,sha256=1erVfjVF_YcUphR4eOmRBd1DHHFJ0AOwpMnhl4ppkVw,1931
optimum/quanto/tensor/weights/__init__.py,sha256=oE9x4TkGz3orOSfeGwnOtGM6BlN273uz16Ki-IoVjbQ,71
optimum/quanto/tensor/weights/__pycache__/__init__.cpython-312.pyc,,
optimum/quanto/tensor/weights/__pycache__/packing.cpython-312.pyc,,
optimum/quanto/tensor/weights/__pycache__/qbits.cpython-312.pyc,,
optimum/quanto/tensor/weights/__pycache__/qbytes.cpython-312.pyc,,
optimum/quanto/tensor/weights/__pycache__/quantization.cpython-312.pyc,,
optimum/quanto/tensor/weights/__pycache__/reordering.cpython-312.pyc,,
optimum/quanto/tensor/weights/awq/__init__.py,sha256=yM-yf_ZAyb0FzMqLiX4ixwkzIbfx4S_cvdh-cU4mVF8,43
optimum/quanto/tensor/weights/awq/__pycache__/__init__.cpython-312.pyc,,
optimum/quanto/tensor/weights/awq/__pycache__/packed.cpython-312.pyc,,
optimum/quanto/tensor/weights/awq/__pycache__/qbits.cpython-312.pyc,,
optimum/quanto/tensor/weights/awq/packed.py,sha256=a5CY2MTsIWCNsGxgLKqhvK7gGcg0H8OAgn5VdQcQ4Sk,11371
optimum/quanto/tensor/weights/awq/qbits.py,sha256=b_neaPSOEbVPKQDTy5iagynDCgqtU-t8SYeaoDdAH3A,6491
optimum/quanto/tensor/weights/marlin/__init__.py,sha256=2tKgfIgw_K1VUdpks6yhp-Wdfg8om--DzLiTSVGfdWc,67
optimum/quanto/tensor/weights/marlin/__pycache__/__init__.cpython-312.pyc,,
optimum/quanto/tensor/weights/marlin/__pycache__/permutations.cpython-312.pyc,,
optimum/quanto/tensor/weights/marlin/fp8/__init__.py,sha256=yM-yf_ZAyb0FzMqLiX4ixwkzIbfx4S_cvdh-cU4mVF8,43
optimum/quanto/tensor/weights/marlin/fp8/__pycache__/__init__.cpython-312.pyc,,
optimum/quanto/tensor/weights/marlin/fp8/__pycache__/packed.cpython-312.pyc,,
optimum/quanto/tensor/weights/marlin/fp8/__pycache__/qbits.cpython-312.pyc,,
optimum/quanto/tensor/weights/marlin/fp8/packed.py,sha256=_91-FtHZiX02nEuDrYr26QNl6SaHHvqJDAOcQCLuXB0,9220
optimum/quanto/tensor/weights/marlin/fp8/qbits.py,sha256=1C2KFFujzsCNYfxRh-wKHOhUdLR6V6ZrckyVUbmGs4c,7032
optimum/quanto/tensor/weights/marlin/int4/__init__.py,sha256=yM-yf_ZAyb0FzMqLiX4ixwkzIbfx4S_cvdh-cU4mVF8,43
optimum/quanto/tensor/weights/marlin/int4/__pycache__/__init__.cpython-312.pyc,,
optimum/quanto/tensor/weights/marlin/int4/__pycache__/packed.cpython-312.pyc,,
optimum/quanto/tensor/weights/marlin/int4/__pycache__/qbits.cpython-312.pyc,,
optimum/quanto/tensor/weights/marlin/int4/packed.py,sha256=ckcWmxcBtN2olCf3U3LdWG_otXMZR9DPT_QxhtIJ7FM,6050
optimum/quanto/tensor/weights/marlin/int4/qbits.py,sha256=pApza5cJleza-qLZu3RtDjgmWkbUqedkYDGfAA895UA,6847
optimum/quanto/tensor/weights/marlin/permutations.py,sha256=465BNvO8wr80eFvghPBPgQux5qfqfMTxpvubc47yRvU,1668
optimum/quanto/tensor/weights/packing.py,sha256=uc31Ug8ar-xpdrzApMtuqntzZ6YRvkT5SM5KbZ0pHuU,1432
optimum/quanto/tensor/weights/qbits.py,sha256=Z7zgyzV2uJJqgmfvlOFv0LINzFAR1_nu_zYM4mMrWA8,12583
optimum/quanto/tensor/weights/qbytes.py,sha256=Kv7UI4PaHQjMzkme0TJg9aR-pBXzQkSMCQxxzbsymZI,13089
optimum/quanto/tensor/weights/quantization.py,sha256=Rtl-EvBjSra3MedyizmLb3BlLBc1qJ87KEgPmRtDhEg,3076
optimum/quanto/tensor/weights/reordering.py,sha256=7TkTb5Xj9801FhYdpkfvkW6JeR7XxjSKkeidiGtov4M,1791
optimum/quanto/tensor/weights/tinygemm/__init__.py,sha256=yM-yf_ZAyb0FzMqLiX4ixwkzIbfx4S_cvdh-cU4mVF8,43
optimum/quanto/tensor/weights/tinygemm/__pycache__/__init__.cpython-312.pyc,,
optimum/quanto/tensor/weights/tinygemm/__pycache__/packed.cpython-312.pyc,,
optimum/quanto/tensor/weights/tinygemm/__pycache__/qbits.cpython-312.pyc,,
optimum/quanto/tensor/weights/tinygemm/packed.py,sha256=6cbUWiymDidrhWVdJ_nknKnYx4qB_v4WCZM1K5-rs0A,6121
optimum/quanto/tensor/weights/tinygemm/qbits.py,sha256=hkgtgkkJ8hQS-TRYF9ehWANAVsF5FSFcClYGlLRbvdU,7459
optimum_quanto-0.2.7.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
optimum_quanto-0.2.7.dist-info/LICENSE,sha256=_qUzP-KIxQzOSqB_wmj445jbIjSoSf5UNGYYqIU2OLs,11419
optimum_quanto-0.2.7.dist-info/METADATA,sha256=E88H-YZVPm-17PMe5iv3YOAXSJRWrU-JPTkNqiVJOK0,13254
optimum_quanto-0.2.7.dist-info/RECORD,,
optimum_quanto-0.2.7.dist-info/WHEEL,sha256=jB7zZ3N9hIM9adW7qlTAyycLYW9npaWKLRzaoVcLKcM,91
optimum_quanto-0.2.7.dist-info/top_level.txt,sha256=rc1rV-uPZnV1Ek7hCwh56pvMCKcip65JbHRRWs8Yqu8,8
