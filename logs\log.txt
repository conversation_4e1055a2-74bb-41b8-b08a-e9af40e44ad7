========================================
         3D AI Studio Launcher
========================================

[1/5] Checking Python installation...
[OK] Python found
[2/4] Checking virtual environment
🚀 3D AI Studio - Comprehensive Dependency Installer
======================================================================
This will install ALL dependencies for Trellis and Hunyuan3D-2 systems

Checking critical dependencies...
  ✓ Flask available
  ✓ Flask-CORS available
  ✓ requests available
  ✓ NumPy available
  ✓ Pillow available
  ✓ PyTorch available
  ✓ Transformers available
  ✓ Trimesh available
  ✓ ImageIO available
  ✓ HuggingFace Hub available

✅ All critical dependencies are available!
✅ Critical dependencies are available.
Run with --force to reinstall all dependencies.
[OK] Virtual environment ready
[3/4] Quick environment check
  [INFO] All dependencies installed automatically at startup
  [INFO] Trellis and Hunyuan3D-2 systems integrated in single environment
Quick environment check...
  [INFO] Full validation available through Dependency Manager
  ✓ Main Application: Basic setup OK
  ✓ Trellis: Basic setup OK
  ✓ Hunyuan3D-2: Basic setup OK
  [OK] Basic environments ready
[4/4] Activating Python environment
Installing missing dependencies
Warning: Some dependencies may not have installed correctly
The application will attempt to start with available functionality
  [OK] Python environment activated
Checking Node.js for Electron app
[OK] Node.js found
Preparing desktop application
[OK] Desktop application ready
Starting 3D AI Studio Desktop App

========================================
    3D AI Studio Desktop Application
    Starting in desktop window

    STREAMLINED SETUP
    All dependencies installed automatically at startup!

    FEATURES:
    [OK] Trellis pipeline for high-quality 3D generation
    [OK] Hunyuan3D-2 integrated pipeline with CUDA acceleration
    [OK] Fully portable application design
    [OK] Comprehensive progress tracking
    [OK] Single virtual environment for all systems

    Ready to use - no manual setup required!
========================================


> 3d-ai-studio@1.0.0 electron
> electron .


Starting 3D AI Studio...
Starting new server...
Starting server...
App path: E:\3D AI Studio
Python path: E:\3D AI Studio\.venv\Scripts\python.exe
App script: E:\3D AI Studio\app.py
Server: 09:19:05 | INFO | [SYSTEM](NO_SESSI) | System startup | Data: {"platform": "Windows-11-10.0.26100-SP0", "python_version": "3.12.10", "cpu_count": 88, "memory_total": "79.9 GB", "disk_free": "530.0 GB", "torch_version": "2.5.1+cu124", "cuda_available": true, "cuda_device": "NVIDIA GeForce RTX 3060", "cuda_memory": "12.0 GB"}
09:19:05 | INFO | [STARTUP](NO_SESSI) | 3D AI Studio starting up

Server: Starting 3D AI Studio core application...
All AI models and dependencies managed through Dependency Manager
Core application ready - use Dependency Manager for AI features
Starting 3D AI Studio server...
Running startup dependency checks...
============================================================
STARTUP DEPENDENCY CHECKS
============================================================
[STARTUP] Checking critical dependencies...

Server: [STARTUP] imageio: Already available

Server: [STARTUP] Installing Pillow...

Server: [STARTUP] Pillow: Installed successfully

Server: [STARTUP] numpy: Already available

Server: [STARTUP] requests: Already available

Server: [STARTUP] flask: Already available

Server: [STARTUP] flask-cors: Already available
[STARTUP] Critical dependencies check complete: 6/6 available

Server: [STARTUP] PIL imports working correctly

Server: [STARTUP] ImageIO already available
[STARTUP] Checking Hunyuan3D-2 setup...

Server: 09:19:19 | INFO | [HUNYUAN3D_DIRECT](NO_SESSI) | Initial setup status - Compilation: True, Models: True
[STARTUP] Hunyuan3D-2 initial setup already completed
============================================================
STARTUP CHECKS COMPLETE: 4/4 successful
============================================================

Server: Using integrated Hunyuan3D-2 pipeline (no background services needed)
============================================================
LOADING HUNYUAN3D-2 PIPELINE
============================================================
[HUNYUAN3D] Starting Hunyuan3D-2 pipeline initialization...
[HUNYUAN3D] Using CUDA acceleration with RTX 3060
09:19:19 | INFO | [HUNYUAN3D_INTEGRATED](NO_SESSI) | Loading Hunyuan3D-2 pipeline components...
[HUNYUAN3D] Stage 1/6: Setting up Hunyuan3D-2 environment...
09:19:19 | INFO | [HUNYUAN3D_INTEGRATED](NO_SESSI) | Hunyuan3D-2 environment setup completed

Server: [HUNYUAN3D] Stage 2/6: Loading background removal model...
09:19:23 | INFO | [HUNYUAN3D_INTEGRATED](NO_SESSI) | Background removal worker loaded

Server stderr: A matching Triton is not available, some optimizations will not be enabled
Traceback (most recent call last):
  File "E:\3D AI Studio\.venv\Lib\site-packages\xformers\__init__.py", line 57, in _is_triton_available
    import triton  # noqa
    ^^^^^^^^^^^^^
ModuleNotFoundError: No module named 'triton'

Server stderr: 2025-06-06 09:19:35,830 - hy3dgen.shapgen - INFO - Try to load model from local path: Resources\Hunyuan3D2_WinPortable\HuggingFaceHub\tencent/Hunyuan3D-2mini\hunyuan3d-dit-v2-mini-turbo
2025-06-06 09:19:35,830 - hy3dgen.shapgen - INFO - Model path not exists, try to download from huggingface

Server: [HUNYUAN3D] Stage 3/6: Loading shape generation pipeline...
Warning:
Unable to load the following plugins:

        filter_embree.dll: filter_embree.dll does not seem to be a Qt Plugin.

Cannot load library E:\3D AI Studio\.venv\Lib\site-packages\pymeshlab\lib\plugins\filter_embree.dll: The specified module could not be found.
        filter_func.dll: filter_func.dll does not seem to be a Qt Plugin.

Cannot load library E:\3D AI Studio\.venv\Lib\site-packages\pymeshlab\lib\plugins\filter_func.dll: The specified module could not be found.
        filter_mesh_alpha_wrap.dll: filter_mesh_alpha_wrap.dll does not seem to be a Qt Plugin.

Cannot load library E:\3D AI Studio\.venv\Lib\site-packages\pymeshlab\lib\plugins\filter_mesh_alpha_wrap.dll: The specified module could not be found.
        filter_mesh_booleans.dll: filter_mesh_booleans.dll does not seem to be a Qt Plugin.

Cannot load library E:\3D AI Studio\.venv\Lib\site-packages\pymeshlab\lib\plugins\filter_mesh_booleans.dll: The specified module could not be found.
        filter_sketchfab.dll: filter_sketchfab.dll does not seem to be a Qt Plugin.

Cannot load library E:\3D AI Studio\.venv\Lib\site-packages\pymeshlab\lib\plugins\filter_sketchfab.dll: The specified module could not be found.
        io_3ds.dll: io_3ds.dll does not seem to be a Qt Plugin.

Cannot load library E:\3D AI Studio\.venv\Lib\site-packages\pymeshlab\lib\plugins\io_3ds.dll: The specified module could not be found.
        io_e57.dll: io_e57.dll does not seem to be a Qt Plugin.

Cannot load library E:\3D AI Studio\.venv\Lib\site-packages\pymeshlab\lib\plugins\io_e57.dll: The specified module could not be found.


Fetching 3 files: 100%|##########| 3/3 [00:00<00:00, 3171.10it/s]

Server stderr: 2025-06-06 09:20:37,396 - hy3dgen.shapgen - INFO - Loading model from Resources\Hunyuan3D2_WinPortable\HuggingFaceHub\models--tencent--Hunyuan3D-2mini\snapshots\d3318f050d91fe232038820bbbabeab95c0de14e\hunyuan3d-dit-v2-mini-turbo\model.fp16.safetensors

Server stderr: 2025-06-06 09:20:47,685 - hy3dgen.shapgen - INFO - Try to load model from local path: Resources\Hunyuan3D2_WinPortable\HuggingFaceHub\tencent/Hunyuan3D-2mini\hunyuan3d-vae-v2-mini-turbo
2025-06-06 09:20:47,685 - hy3dgen.shapgen - INFO - Model path not exists, try to download from huggingface

Fetching 3 files:   0%|          | 0/3 [00:00<?, ?it/s]
Fetching 3 files: 100%|##########| 3/3 [00:00<00:00, 1461.09it/s]

Server stderr: 2025-06-06 09:21:36,920 - hy3dgen.shapgen - INFO - Loading model from Resources\Hunyuan3D2_WinPortable\HuggingFaceHub\models--tencent--Hunyuan3D-2mini\snapshots\d3318f050d91fe232038820bbbabeab95c0de14e\hunyuan3d-vae-v2-mini-turbo\model.fp16.safetensors

Server: 09:21:39 | INFO | [HUNYUAN3D_INTEGRATED](NO_SESSI) | Shape generation pipeline loaded
[HUNYUAN3D] Stage 4/6: Loading mesh processing workers...
09:21:39 | INFO | [HUNYUAN3D_INTEGRATED](NO_SESSI) | Mesh processing workers loaded

Server: [HUNYUAN3D] Stage 5/6: Loading texture generation pipeline...

Fetching 13 files:   0%|          | 0/13 [00:00<?, ?it/s]
Fetching 13 files: 100%|##########| 13/13 [00:00<00:00, 4278.89it/s]

Failed to start application: Error: Server startup timeout
    at Timeout._onTimeout (E:\3D AI Studio\Resources\UI\electron\main.js:227:16)
    at listOnTimeout (node:internal/timers:588:17)
    at process.processTimers (node:internal/timers:523:7)
