#!/usr/bin/env python3
"""
Comprehensive Dependency Installer for 3D AI Studio.
Installs ALL dependencies needed for Trellis and Hunyuan3D-2 systems in strategic order.
"""

import subprocess
import sys
import os
import time
import shutil
from pathlib import Path

def get_pip_executable():
    """Get the pip executable for the virtual environment."""
    venv_path = Path(__file__).parent / ".venv"
    if os.name == 'nt':  # Windows
        pip_exe = venv_path / "Scripts" / "pip.exe"
        python_exe = venv_path / "Scripts" / "python.exe"
    else:  # Unix/Linux/Mac
        pip_exe = venv_path / "bin" / "pip"
        python_exe = venv_path / "bin" / "python"

    if not pip_exe.exists():
        print("Error: Virtual environment not found or pip not available")
        print("Please run RunApplication.bat to create the virtual environment first")
        return None, None

    return str(pip_exe), str(python_exe)

def install_package_group(group_name, packages, pip_exe, timeout=300):
    """Install a group of packages with error handling."""
    print(f"\n{'='*60}")
    print(f"Installing {group_name}")
    print(f"{'='*60}")

    success_count = 0
    total_count = len(packages)

    for i, package_info in enumerate(packages):
        if isinstance(package_info, str):
            package = package_info
            special_args = []
        else:
            package = package_info['package']
            special_args = package_info.get('args', [])

        try:
            print(f"[{i+1}/{total_count}] Installing {package}...")

            cmd = [pip_exe, 'install'] + special_args + [package]

            result = subprocess.run(
                cmd,
                check=True,
                capture_output=True,
                text=True,
                timeout=timeout
            )

            print(f"  ✓ {package} installed successfully")
            success_count += 1

        except subprocess.TimeoutExpired:
            print(f"  ⏰ {package} installation timed out (continuing...)")
        except subprocess.CalledProcessError as e:
            print(f"  ❌ Failed to install {package}: {e.stderr.strip() if e.stderr else 'Unknown error'}")
            # For critical packages, we might want to retry or fail
            if package in ['torch', 'Flask', 'numpy']:
                print(f"  🔄 Retrying critical package {package}...")
                try:
                    # Retry with simpler command
                    simple_cmd = [pip_exe, 'install', package.split('>=')[0].split('==')[0]]
                    subprocess.run(simple_cmd, check=True, capture_output=True, text=True, timeout=timeout)
                    print(f"  ✓ {package} installed successfully (retry)")
                    success_count += 1
                except:
                    print(f"  ❌ Critical package {package} failed even on retry")
        except Exception as e:
            print(f"  ❌ Error installing {package}: {e}")

    print(f"\n{group_name} Summary: {success_count}/{total_count} packages installed successfully")
    return success_count, total_count

def install_all_dependencies():
    """Install ALL dependencies for the 3D AI Studio in strategic order."""

    pip_exe, python_exe = get_pip_executable()
    if not pip_exe:
        return False

    print("🚀 3D AI Studio - Comprehensive Dependency Installation")
    print("This will install ALL dependencies for Trellis and Hunyuan3D-2 systems")
    print("=" * 80)

    # Upgrade pip first
    print("Upgrading pip...")
    try:
        subprocess.run([pip_exe, 'install', '--upgrade', 'pip'], check=True, capture_output=True, timeout=120)
        print("✓ pip upgraded successfully")
    except:
        print("⚠ pip upgrade failed, continuing...")

    total_success = 0
    total_packages = 0

    # 1. CORE SYSTEM DEPENDENCIES (Essential for app startup)
    core_packages = [
        "Flask>=2.0.0",
        "Flask-CORS>=3.0.0",
        "requests>=2.25.0",
        "Werkzeug>=2.0.0",
        "psutil>=5.8.0",
        "urllib3>=1.26.0",
        "cryptography>=3.0.0"
    ]

    success, count = install_package_group("Core System Dependencies", core_packages, pip_exe, 180)
    total_success += success
    total_packages += count

    # 2. PYTHON CORE LIBRARIES
    python_core = [
        "numpy>=1.19.0",
        "Pillow>=8.0.0",
        "tqdm>=4.60.0",
        "pydantic>=1.8.0",
        "easydict>=1.9",
        "omegaconf>=2.3.0",
        "einops>=0.7.0"
    ]

    success, count = install_package_group("Python Core Libraries", python_core, pip_exe, 180)
    total_success += success
    total_packages += count

    # 3. PYTORCH ECOSYSTEM (Critical for both Trellis and Hunyuan3D-2)
    pytorch_packages = [
        {
            'package': 'torch==2.5.1',
            'args': ['--index-url', 'https://download.pytorch.org/whl/cu124']
        },
        {
            'package': 'torchvision==0.20.1',
            'args': ['--index-url', 'https://download.pytorch.org/whl/cu124']
        },
        {
            'package': 'torchaudio==2.5.1',
            'args': ['--index-url', 'https://download.pytorch.org/whl/cu124']
        }
    ]

    success, count = install_package_group("PyTorch Ecosystem", pytorch_packages, pip_exe, 600)
    total_success += success
    total_packages += count

    # 4. ML/AI CORE LIBRARIES
    ml_packages = [
        "transformers>=4.35.0",
        "diffusers>=0.24.0",
        "accelerate>=0.24.0",
        "huggingface_hub>=0.20.0",
        "tokenizers>=0.15.0",
        "safetensors>=0.4.0"
    ]

    success, count = install_package_group("ML/AI Core Libraries", ml_packages, pip_exe, 300)
    total_success += success
    total_packages += count

    # 5. IMAGE PROCESSING LIBRARIES
    image_packages = [
        "imageio>=2.9.0",
        "imageio-ffmpeg>=0.4.5",
        "opencv-python>=4.5.0",
        "scikit-image>=0.18.0",
        "scipy>=1.7.0",
        "matplotlib>=3.5.0"
    ]

    success, count = install_package_group("Image Processing Libraries", image_packages, pip_exe, 240)
    total_success += success
    total_packages += count

    # 6. 3D PROCESSING LIBRARIES
    mesh_packages = [
        "trimesh>=3.19.0",
        "pymeshlab>=2022.2",
        "pygltflib>=1.15.0",
        "xatlas>=0.0.9",
        "open3d>=0.17.0",
        "pyvista>=0.45.0",
        "pymeshfix>=0.17.0"
    ]

    success, count = install_package_group("3D Processing Libraries", mesh_packages, pip_exe, 360)
    total_success += success
    total_packages += count

    # 7. COMPILATION AND BUILD TOOLS
    build_packages = [
        "ninja>=1.10.0",
        "pybind11>=2.10.0",
        "setuptools>=65.0.0",
        "wheel>=0.37.0",
        "cmake>=3.22.0"
    ]

    success, count = install_package_group("Build Tools", build_packages, pip_exe, 180)
    total_success += success
    total_packages += count

    # 8. BACKGROUND REMOVAL AND ONNX
    rembg_packages = [
        "rembg>=2.0.38",
        "onnxruntime>=1.18.1",
        "onnx>=1.14.0"
    ]

    success, count = install_package_group("Background Removal", rembg_packages, pip_exe, 240)
    total_success += success
    total_packages += count

    # 9. SPECIALIZED CUDA LIBRARIES
    cuda_packages = [
        "xformers>=0.0.28",
        "spconv-cu120",
        "flash-attn>=2.0.0"  # Optional but beneficial
    ]

    success, count = install_package_group("CUDA Libraries", cuda_packages, pip_exe, 480)
    total_success += success
    total_packages += count

    # 10. WEB FRAMEWORK AND API
    web_packages = [
        "gradio>=4.44.0",
        "gradio_litmodel3d>=0.0.1",
        "fastapi>=0.100.0",
        "uvicorn>=0.23.0",
        "aiohttp>=3.10.0",
        "aiofiles>=24.1.0"
    ]

    success, count = install_package_group("Web Framework", web_packages, pip_exe, 240)
    total_success += success
    total_packages += count

    # 11. GIT AND DOWNLOAD UTILITIES
    git_packages = [
        "GitPython>=3.1.0",
        "pypdl>=1.5.4",
        "asyncio-throttle>=1.0.0"
    ]

    success, count = install_package_group("Git and Download Utilities", git_packages, pip_exe, 180)
    total_success += success
    total_packages += count

    # 12. TRELLIS SPECIFIC DEPENDENCIES
    trellis_packages = [
        {
            'package': 'git+https://github.com/EasternJournalist/utils3d.git@9a4eb15e4021b67b12c460c7057d642626897ec8',
            'args': []
        },
        "igraph>=0.10.0",
        "mcubes>=0.1.0",
        "marching_cubes>=0.0.7"
    ]

    success, count = install_package_group("Trellis Dependencies", trellis_packages, pip_exe, 360)
    total_success += success
    total_packages += count

    # 13. HUNYUAN3D-2 SPECIFIC DEPENDENCIES
    hunyuan_packages = [
        "ConfigArgParse>=1.5.0",
        "scikit-learn>=1.0.0",
        "scikit-image>=0.19.0",
        "gevent>=22.0.0",
        "geventhttpclient>=2.0.0",
        "facexlib>=0.3.0",
        "ipdb>=0.13.0",
        "pytorch_lightning>=2.0.0",
        "taming-transformers-rom1504>=0.0.6",
        "kornia>=0.7.0",
        "sentencepiece>=0.1.99",
        "tritonclient>=2.34.0",
        "mmgp==3.2.7",
        "diso>=0.1.0"
    ]

    success, count = install_package_group("Hunyuan3D-2 Dependencies", hunyuan_packages, pip_exe, 480)
    total_success += success
    total_packages += count

    # 14. INSTALL HUNYUAN3D-2 PACKAGE
    print(f"\n{'='*60}")
    print("Installing Hunyuan3D-2 Package")
    print(f"{'='*60}")

    hunyuan3d_path = Path(__file__).parent / "Resources" / "Hunyuan3D2_WinPortable" / "Hunyuan3D-2"
    if hunyuan3d_path.exists():
        try:
            print("Installing hy3dgen package from local source...")
            subprocess.run([
                pip_exe, 'install', '-e', str(hunyuan3d_path)
            ], check=True, capture_output=True, text=True, timeout=300)
            print("✓ hy3dgen package installed successfully")
            total_success += 1
        except Exception as e:
            print(f"❌ Failed to install hy3dgen package: {e}")
        total_packages += 1
    else:
        print("⚠ Hunyuan3D-2 source not found, skipping package installation")

    # FINAL SUMMARY
    print(f"\n{'='*80}")
    print("INSTALLATION COMPLETE")
    print(f"{'='*80}")
    print(f"Total packages processed: {total_packages}")
    print(f"Successfully installed: {total_success}")
    print(f"Failed installations: {total_packages - total_success}")
    print(f"Success rate: {(total_success/total_packages)*100:.1f}%" if total_packages > 0 else "N/A")

    if total_success >= total_packages * 0.8:  # 80% success rate
        print("\n✅ Installation completed successfully!")
        print("Both Trellis and Hunyuan3D-2 systems should be functional.")
        return True
    else:
        print("\n⚠️ Some critical dependencies may have failed to install.")
        print("The application may have limited functionality.")
        return False

def check_critical_dependencies():
    """Check if critical dependencies are available for basic functionality."""
    print("Checking critical dependencies...")

    critical_modules = [
        # Core Flask web framework
        ("flask", "Flask"),
        ("flask_cors", "Flask-CORS"),
        ("requests", "requests"),
        ("numpy", "NumPy"),
        ("PIL", "Pillow"),
        ("torch", "PyTorch"),
        ("transformers", "Transformers"),
        ("trimesh", "Trimesh"),
        ("imageio", "ImageIO")
    ]

    missing = []
    for module_name, package_name in critical_modules:
        try:
            __import__(module_name)
            print(f"  ✓ {package_name} available")
        except ImportError:
            print(f"  ❌ {package_name} missing")
            missing.append(package_name)

    if missing:
        print()
        print(f"Missing critical dependencies: {', '.join(missing)}")
        return False
    else:
        print()
        print("✅ All critical dependencies are available!")
        return True

def install_startup_dependencies():
    """Legacy function name for compatibility."""
    return install_all_dependencies()

def check_startup_dependencies():
    """Legacy function name for compatibility."""
    return check_critical_dependencies()

if __name__ == "__main__":
    print("🚀 3D AI Studio - Comprehensive Dependency Installer")
    print("=" * 70)
    print("This will install ALL dependencies for Trellis and Hunyuan3D-2 systems")
    print()

    # Check current status
    if check_critical_dependencies():
        print("✅ Critical dependencies are available.")
        print("Run with --force to reinstall all dependencies.")
    else:
        print("Installing all dependencies...")
        install_all_dependencies()
        print()
        print("Verifying installation...")
        check_critical_dependencies()
