"""
Modular Pipeline System for 3D AI Studio
Allows mixing and matching components from different pipelines
"""

import os
import sys
import time
import torch
from pathlib import Path
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass
from enum import Enum

# Import logging
from utils.logging_system import get_logger
logger = get_logger()

class PipelineComponent(Enum):
    """Available pipeline components."""
    MESH_GENERATION = "mesh_generation"
    TEXTURE_GENERATION = "texture_generation"
    DELIGHTER = "delighter"
    BACKGROUND_REMOVAL = "background_removal"

class PipelineProvider(Enum):
    """Available pipeline providers."""
    HUNYUAN3D = "hunyuan3d"
    TRELLIS = "trellis"

@dataclass
class PipelineConfig:
    """Configuration for modular pipeline."""
    mesh_provider: PipelineProvider
    texture_provider: PipelineProvider
    delighter_provider: Optional[PipelineProvider] = None
    background_removal_provider: PipelineProvider = PipelineProvider.HUNYUAN3D
    
    def __str__(self):
        return f"Mesh:{self.mesh_provider.value}, Texture:{self.texture_provider.value}, Delighter:{self.delighter_provider.value if self.delighter_provider else 'None'}"

class ModularPipelineSystem:
    """Modular pipeline system that combines components from different providers."""
    
    def __init__(self):
        self.components = {}
        self.available_providers = set()
        self.current_config = None
        
        # Component instances
        self.hunyuan3d_pipeline = None
        self.trellis_pipeline = None
        
        logger.info("Modular Pipeline System initialized", component="MODULAR_PIPELINE")
    
    def register_provider(self, provider: PipelineProvider, pipeline_instance):
        """Register a pipeline provider."""
        self.available_providers.add(provider)
        
        if provider == PipelineProvider.HUNYUAN3D:
            self.hunyuan3d_pipeline = pipeline_instance
        elif provider == PipelineProvider.TRELLIS:
            self.trellis_pipeline = pipeline_instance
            
        logger.info(f"Registered provider: {provider.value}", component="MODULAR_PIPELINE")
    
    def get_available_configs(self) -> List[PipelineConfig]:
        """Get all available pipeline configurations."""
        configs = []
        
        # Check what providers are available
        has_hunyuan3d = PipelineProvider.HUNYUAN3D in self.available_providers
        has_trellis = PipelineProvider.TRELLIS in self.available_providers
        
        if has_hunyuan3d and has_trellis:
            # All combinations available
            configs.extend([
                PipelineConfig(
                    mesh_provider=PipelineProvider.HUNYUAN3D,
                    texture_provider=PipelineProvider.TRELLIS,
                    delighter_provider=PipelineProvider.HUNYUAN3D
                ),
                PipelineConfig(
                    mesh_provider=PipelineProvider.TRELLIS,
                    texture_provider=PipelineProvider.TRELLIS,
                    delighter_provider=PipelineProvider.HUNYUAN3D
                ),
                PipelineConfig(
                    mesh_provider=PipelineProvider.HUNYUAN3D,
                    texture_provider=PipelineProvider.HUNYUAN3D,
                    delighter_provider=PipelineProvider.HUNYUAN3D
                ),
                PipelineConfig(
                    mesh_provider=PipelineProvider.TRELLIS,
                    texture_provider=PipelineProvider.TRELLIS,
                    delighter_provider=None
                )
            ])
        elif has_hunyuan3d:
            configs.append(PipelineConfig(
                mesh_provider=PipelineProvider.HUNYUAN3D,
                texture_provider=PipelineProvider.HUNYUAN3D,
                delighter_provider=PipelineProvider.HUNYUAN3D
            ))
        elif has_trellis:
            configs.append(PipelineConfig(
                mesh_provider=PipelineProvider.TRELLIS,
                texture_provider=PipelineProvider.TRELLIS,
                delighter_provider=None
            ))
        
        return configs
    
    def set_config(self, config: PipelineConfig):
        """Set the current pipeline configuration."""
        self.current_config = config
        logger.info(f"Pipeline configuration set: {config}", component="MODULAR_PIPELINE")
    
    def generate_mesh(self, image_path: str, settings: Dict[str, Any], progress_callback: Optional[Callable] = None) -> str:
        """Generate 3D mesh using the configured provider."""
        if not self.current_config:
            raise ValueError("No pipeline configuration set")
        
        provider = self.current_config.mesh_provider
        logger.info(f"Generating mesh using {provider.value}", component="MODULAR_PIPELINE")
        
        if provider == PipelineProvider.HUNYUAN3D:
            if not self.hunyuan3d_pipeline or not self.hunyuan3d_pipeline.is_available():
                raise RuntimeError("Hunyuan3D pipeline not available")
            
            # Use Hunyuan3D for mesh generation (without texture)
            mesh_settings = settings.copy()
            mesh_settings['enable_texture'] = False  # We'll handle texture separately
            
            return self.hunyuan3d_pipeline.generate(
                image_path=image_path,
                settings=mesh_settings,
                progress_callback=progress_callback
            )
            
        elif provider == PipelineProvider.TRELLIS:
            if not self.trellis_pipeline:
                raise RuntimeError("Trellis pipeline not available")
            
            # Use Trellis for mesh generation
            return self.trellis_pipeline.generate(
                image_path=image_path,
                settings=settings,
                progress_callback=progress_callback
            )
        
        else:
            raise ValueError(f"Unknown mesh provider: {provider}")
    
    def apply_texture(self, mesh_path: str, image_path: str, settings: Dict[str, Any], progress_callback: Optional[Callable] = None) -> str:
        """Apply texture to mesh using the configured provider."""
        if not self.current_config:
            raise ValueError("No pipeline configuration set")
        
        provider = self.current_config.texture_provider
        logger.info(f"Applying texture using {provider.value}", component="MODULAR_PIPELINE")
        
        if provider == PipelineProvider.HUNYUAN3D:
            if not self.hunyuan3d_pipeline or not self.hunyuan3d_pipeline.has_texturegen:
                raise RuntimeError("Hunyuan3D texture generation not available")
            
            # Use Hunyuan3D texture generation
            return self.hunyuan3d_pipeline.apply_texture_to_mesh(
                mesh_path=mesh_path,
                image_path=image_path,
                settings=settings,
                progress_callback=progress_callback
            )
            
        elif provider == PipelineProvider.TRELLIS:
            if not self.trellis_pipeline:
                raise RuntimeError("Trellis pipeline not available")
            
            # Use Trellis texture generation
            return self.trellis_pipeline.apply_texture_to_mesh(
                mesh_path=mesh_path,
                image_path=image_path,
                settings=settings,
                progress_callback=progress_callback
            )
        
        else:
            raise ValueError(f"Unknown texture provider: {provider}")
    
    def apply_delighter(self, image_path: str, settings: Dict[str, Any], progress_callback: Optional[Callable] = None) -> str:
        """Apply delighter to image using the configured provider."""
        if not self.current_config or not self.current_config.delighter_provider:
            logger.info("No delighter configured, skipping", component="MODULAR_PIPELINE")
            return image_path
        
        provider = self.current_config.delighter_provider
        logger.info(f"Applying delighter using {provider.value}", component="MODULAR_PIPELINE")
        
        if provider == PipelineProvider.HUNYUAN3D:
            if not self.hunyuan3d_pipeline:
                raise RuntimeError("Hunyuan3D pipeline not available")
            
            # Use Hunyuan3D delighter
            return self.hunyuan3d_pipeline.apply_delighter(
                image_path=image_path,
                settings=settings,
                progress_callback=progress_callback
            )
        
        else:
            raise ValueError(f"Unknown delighter provider: {provider}")
    
    def generate_full_pipeline(self, image_path: str, settings: Dict[str, Any], progress_callback: Optional[Callable] = None) -> str:
        """Run the full modular pipeline with the current configuration."""
        if not self.current_config:
            raise ValueError("No pipeline configuration set")
        
        logger.info(f"Starting full modular pipeline: {self.current_config}", component="MODULAR_PIPELINE")
        
        try:
            # Step 1: Apply delighter if configured
            processed_image = image_path
            if self.current_config.delighter_provider:
                if progress_callback:
                    progress_callback("Applying delighter...")
                processed_image = self.apply_delighter(processed_image, settings, progress_callback)
            
            # Step 2: Generate mesh
            if progress_callback:
                progress_callback(f"Generating mesh using {self.current_config.mesh_provider.value}...")
            mesh_path = self.generate_mesh(processed_image, settings, progress_callback)
            
            # Step 3: Apply texture
            if progress_callback:
                progress_callback(f"Applying texture using {self.current_config.texture_provider.value}...")
            final_mesh_path = self.apply_texture(mesh_path, processed_image, settings, progress_callback)
            
            logger.info(f"Modular pipeline completed successfully", component="MODULAR_PIPELINE")
            return final_mesh_path
            
        except Exception as e:
            logger.error(f"Modular pipeline failed: {e}", component="MODULAR_PIPELINE")
            raise
    
    def get_pipeline_info(self) -> Dict[str, Any]:
        """Get information about the current pipeline configuration."""
        if not self.current_config:
            return {"status": "no_config", "message": "No pipeline configuration set"}
        
        info = {
            "status": "configured",
            "config": str(self.current_config),
            "mesh_provider": self.current_config.mesh_provider.value,
            "texture_provider": self.current_config.texture_provider.value,
            "delighter_provider": self.current_config.delighter_provider.value if self.current_config.delighter_provider else None,
            "available_providers": [p.value for p in self.available_providers],
            "capabilities": {
                "mesh_generation": True,
                "texture_generation": True,
                "delighter": self.current_config.delighter_provider is not None,
                "background_removal": True
            }
        }
        
        return info

# Global modular pipeline instance
_modular_pipeline = None

def get_modular_pipeline() -> ModularPipelineSystem:
    """Get the global modular pipeline instance."""
    global _modular_pipeline
    if _modular_pipeline is None:
        _modular_pipeline = ModularPipelineSystem()
    return _modular_pipeline

def initialize_modular_pipeline(hunyuan3d_pipeline=None, trellis_pipeline=None) -> ModularPipelineSystem:
    """Initialize the modular pipeline with available providers."""
    modular_pipeline = get_modular_pipeline()
    
    if hunyuan3d_pipeline:
        modular_pipeline.register_provider(PipelineProvider.HUNYUAN3D, hunyuan3d_pipeline)
    
    if trellis_pipeline:
        modular_pipeline.register_provider(PipelineProvider.TRELLIS, trellis_pipeline)
    
    # Set default configuration
    available_configs = modular_pipeline.get_available_configs()
    if available_configs:
        # Prefer Hunyuan3D mesh + Trellis texture if available
        preferred_config = None
        for config in available_configs:
            if (config.mesh_provider == PipelineProvider.HUNYUAN3D and 
                config.texture_provider == PipelineProvider.TRELLIS):
                preferred_config = config
                break
        
        if not preferred_config:
            preferred_config = available_configs[0]
        
        modular_pipeline.set_config(preferred_config)
        logger.info(f"Default modular pipeline configuration set: {preferred_config}", component="MODULAR_PIPELINE")
    
    return modular_pipeline
