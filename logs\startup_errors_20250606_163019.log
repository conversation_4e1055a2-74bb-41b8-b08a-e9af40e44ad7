2025-06-06 16:31:00,023 |    ERROR | startup_detailed | log_error:134 | ERROR: Texture generation pipeline failed after 4.2s
2025-06-06 16:31:00,024 |    ERROR | startup_detailed | log_error:136 | Exception: 'SafeTensorFile' object has no attribute 'get_slice'
2025-06-06 16:31:00,046 |    ERROR | startup_detailed | log_error:137 | Traceback (most recent call last):
  File "E:\3D AI Studio\hunyuan3d_integrated_pipeline.py", line 252, in load_pipeline_components
    self.texgen_worker = Hunyuan3DPaintPipeline.from_pretrained(local_texgen_path)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\texgen\pipelines.py", line 86, in from_pretrained
    return cls(Hunyuan3DTexGenConfig(delight_model_path, multiview_model_path))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\texgen\pipelines.py", line 95, in __init__
    self.load_models()
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\texgen\pipelines.py", line 101, in load_models
    self.models['delight_model'] = Light_Shadow_Remover(self.config)
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\texgen\utils\dehighlight_utils.py", line 28, in __init__
    pipeline = StableDiffusionInstructPix2PixPipeline.from_pretrained(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\pipelines\pipeline_utils.py", line 961, in from_pretrained
    loaded_sub_model = load_sub_model(
                       ^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\pipelines\pipeline_loading_utils.py", line 777, in load_sub_model
    loaded_sub_model = load_method(os.path.join(cached_folder, name), **loading_kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\transformers\modeling_utils.py", line 309, in _wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\transformers\modeling_utils.py", line 4574, in from_pretrained
    ) = cls._load_pretrained_model(
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\transformers\modeling_utils.py", line 4833, in _load_pretrained_model
    load_state_dict(checkpoint_files[0], map_location="meta", weights_only=weights_only).keys()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\transformers\modeling_utils.py", line 541, in load_state_dict
    _slice = f.get_slice(k)
             ^^^^^^^^^^^
AttributeError: 'SafeTensorFile' object has no attribute 'get_slice'

