2025-06-06 00:04:54 | [31mERROR[0m | [HUNYUAN3D_INTEGRATED](NO_SESSI) | 3d_ai_studio.error | Error loading Hunyuan3D-2 pipeline components: Failed to import diffusers.models.transformers.pixart_transformer_2d because of the following error (look up to see its traceback):
DLL load failed while importing _C_flashattention: The specified module could not be found.
2025-06-06 00:04:55 | [0m[31mERROR[0m[0m | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.error | Traceback: Traceback (most recent call last):
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\utils\import_utils.py", line 820, in _get_module
    return importlib.import_module("." + module_name, self.__name__)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.12_3.12.2800.0_x64__qbz5n2kfra8p0\Lib\importlib\__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1310, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\models\transformers\__init__.py", line 5, in <module>
    from .auraflow_transformer_2d import AuraFlowTransformer2DModel
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\models\transformers\auraflow_transformer_2d.py", line 26, in <module>
    from ..attention_processor import (
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\models\attention_processor.py", line 35, in <module>
    import xformers.ops
  File "E:\3D AI Studio\.venv\Lib\site-packages\xformers\ops\__init__.py", line 9, in <module>
    from .fmha import (
  File "E:\3D AI Studio\.venv\Lib\site-packages\xformers\ops\fmha\__init__.py", line 10, in <module>
    from . import (
  File "E:\3D AI Studio\.venv\Lib\site-packages\xformers\ops\fmha\flash.py", line 55, in <module>
    from ... import _C_flashattention  # type: ignore[attr-defined]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ImportError: DLL load failed while importing _C_flashattention: The specified module could not be found.

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\3D AI Studio\hunyuan3d_integrated_pipeline.py", line 100, in load_pipeline_components
    from hy3dgen.shapegen import (
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\__init__.py", line 15, in <module>
    from .pipelines import Hunyuan3DDiTPipeline, Hunyuan3DDiTFlowMatchingPipeline
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\pipelines.py", line 30, in <module>
    from .models.autoencoders import ShapeVAE
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\models\__init__.py", line 26, in <module>
    from .autoencoders import ShapeVAE
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\models\autoencoders\__init__.py", line 18, in <module>
    from .model import ShapeVAE, VectsetVAE
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\models\autoencoders\model.py", line 22, in <module>
    from .surface_extractors import MCSurfaceExtractor, SurfaceExtractors
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\models\autoencoders\surface_extractors.py", line 79, in <module>
    from mmgp import offload
  File "E:\3D AI Studio\.venv\Lib\site-packages\mmgp\offload.py", line 70, in <module>
    from optimum.quanto import freeze,  qfloat8, qint4 , qint8, quantize, QModuleMixin, QLinear, QTensor,  quantize_module, register_qmodule
  File "E:\3D AI Studio\.venv\Lib\site-packages\optimum\quanto\__init__.py", line 19, in <module>
    from .models import *
  File "E:\3D AI Studio\.venv\Lib\site-packages\optimum\quanto\models\__init__.py", line 34, in <module>
    from .diffusers_models import *
  File "E:\3D AI Studio\.venv\Lib\site-packages\optimum\quanto\models\diffusers_models.py", line 30, in <module>
    from diffusers import PixArtTransformer2DModel
  File "<frozen importlib._bootstrap>", line 1412, in _handle_fromlist
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\utils\import_utils.py", line 811, in __getattr__
    value = getattr(module, name)
            ^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\utils\import_utils.py", line 810, in __getattr__
    module = self._get_module(self._class_to_module[name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\utils\import_utils.py", line 822, in _get_module
    raise RuntimeError(
RuntimeError: Failed to import diffusers.models.transformers.pixart_transformer_2d because of the following error (look up to see its traceback):
DLL load failed while importing _C_flashattention: The specified module could not be found.

2025-06-06 06:54:55 | [0m[31mERROR[0m[0m | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.error | Error loading Hunyuan3D-2 pipeline components: Failed to import diffusers.models.transformers.pixart_transformer_2d because of the following error (look up to see its traceback):
DLL load failed while importing _C_flashattention: The specified module could not be found.
2025-06-06 06:54:55 | [0m[31mERROR[0m[0m | [[HUNYUAN3D_INTEGRATED]]((NO_SESS) | 3d_ai_studio.error | Traceback: Traceback (most recent call last):
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\utils\import_utils.py", line 820, in _get_module
    return importlib.import_module("." + module_name, self.__name__)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.12_3.12.2800.0_x64__qbz5n2kfra8p0\Lib\importlib\__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1310, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\models\transformers\__init__.py", line 5, in <module>
    from .auraflow_transformer_2d import AuraFlowTransformer2DModel
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\models\transformers\auraflow_transformer_2d.py", line 26, in <module>
    from ..attention_processor import (
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\models\attention_processor.py", line 35, in <module>
    import xformers.ops
  File "E:\3D AI Studio\.venv\Lib\site-packages\xformers\ops\__init__.py", line 9, in <module>
    from .fmha import (
  File "E:\3D AI Studio\.venv\Lib\site-packages\xformers\ops\fmha\__init__.py", line 10, in <module>
    from . import (
  File "E:\3D AI Studio\.venv\Lib\site-packages\xformers\ops\fmha\flash.py", line 55, in <module>
    from ... import _C_flashattention  # type: ignore[attr-defined]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ImportError: DLL load failed while importing _C_flashattention: The specified module could not be found.

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\3D AI Studio\hunyuan3d_integrated_pipeline.py", line 103, in load_pipeline_components
    from hy3dgen.shapegen import (
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\__init__.py", line 15, in <module>
    from .pipelines import Hunyuan3DDiTPipeline, Hunyuan3DDiTFlowMatchingPipeline
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\pipelines.py", line 30, in <module>
    from .models.autoencoders import ShapeVAE
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\models\__init__.py", line 26, in <module>
    from .autoencoders import ShapeVAE
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\models\autoencoders\__init__.py", line 18, in <module>
    from .model import ShapeVAE, VectsetVAE
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\models\autoencoders\model.py", line 22, in <module>
    from .surface_extractors import MCSurfaceExtractor, SurfaceExtractors
  File "E:\3D AI Studio\Resources\Hunyuan3D2_WinPortable\Hunyuan3D-2\hy3dgen\shapegen\models\autoencoders\surface_extractors.py", line 79, in <module>
    from mmgp import offload
  File "E:\3D AI Studio\.venv\Lib\site-packages\mmgp\offload.py", line 70, in <module>
    from optimum.quanto import freeze,  qfloat8, qint4 , qint8, quantize, QModuleMixin, QLinear, QTensor,  quantize_module, register_qmodule
  File "E:\3D AI Studio\.venv\Lib\site-packages\optimum\quanto\__init__.py", line 19, in <module>
    from .models import *
  File "E:\3D AI Studio\.venv\Lib\site-packages\optimum\quanto\models\__init__.py", line 34, in <module>
    from .diffusers_models import *
  File "E:\3D AI Studio\.venv\Lib\site-packages\optimum\quanto\models\diffusers_models.py", line 30, in <module>
    from diffusers import PixArtTransformer2DModel
  File "<frozen importlib._bootstrap>", line 1412, in _handle_fromlist
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\utils\import_utils.py", line 811, in __getattr__
    value = getattr(module, name)
            ^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\utils\import_utils.py", line 810, in __getattr__
    module = self._get_module(self._class_to_module[name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\3D AI Studio\.venv\Lib\site-packages\diffusers\utils\import_utils.py", line 822, in _get_module
    raise RuntimeError(
RuntimeError: Failed to import diffusers.models.transformers.pixart_transformer_2d because of the following error (look up to see its traceback):
DLL load failed while importing _C_flashattention: The specified module could not be found.

