# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: model_config.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x12model_config.proto\x12\tinference\"\x96\x01\n\x10ModelRateLimiter\x12\x37\n\tresources\x18\x01 \x03(\x0b\x32$.inference.ModelRateLimiter.Resource\x12\x10\n\x08priority\x18\x02 \x01(\r\x1a\x37\n\x08Resource\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0e\n\x06global\x18\x02 \x01(\x08\x12\r\n\x05\x63ount\x18\x03 \x01(\r\"\x87\x04\n\x12ModelInstanceGroup\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x30\n\x04kind\x18\x04 \x01(\x0e\x32\".inference.ModelInstanceGroup.Kind\x12\r\n\x05\x63ount\x18\x02 \x01(\x05\x12\x31\n\x0crate_limiter\x18\x06 \x01(\x0b\x32\x1b.inference.ModelRateLimiter\x12\x0c\n\x04gpus\x18\x03 \x03(\x05\x12H\n\x11secondary_devices\x18\x08 \x03(\x0b\x32-.inference.ModelInstanceGroup.SecondaryDevice\x12\x0f\n\x07profile\x18\x05 \x03(\t\x12\x0f\n\x07passive\x18\x07 \x01(\x08\x12\x13\n\x0bhost_policy\x18\t \x01(\t\x1a\x9c\x01\n\x0fSecondaryDevice\x12O\n\x04kind\x18\x01 \x01(\x0e\x32\x41.inference.ModelInstanceGroup.SecondaryDevice.SecondaryDeviceKind\x12\x11\n\tdevice_id\x18\x02 \x01(\x03\"%\n\x13SecondaryDeviceKind\x12\x0e\n\nKIND_NVDLA\x10\x00\"A\n\x04Kind\x12\r\n\tKIND_AUTO\x10\x00\x12\x0c\n\x08KIND_GPU\x10\x01\x12\x0c\n\x08KIND_CPU\x10\x02\x12\x0e\n\nKIND_MODEL\x10\x03\"#\n\x12ModelTensorReshape\x12\r\n\x05shape\x18\x01 \x03(\x03\"\xd3\x02\n\nModelInput\x12\x0c\n\x04name\x18\x01 \x01(\t\x12&\n\tdata_type\x18\x02 \x01(\x0e\x32\x13.inference.DataType\x12,\n\x06\x66ormat\x18\x03 \x01(\x0e\x32\x1c.inference.ModelInput.Format\x12\x0c\n\x04\x64ims\x18\x04 \x03(\x03\x12.\n\x07reshape\x18\x05 \x01(\x0b\x32\x1d.inference.ModelTensorReshape\x12\x17\n\x0fis_shape_tensor\x18\x06 \x01(\x08\x12\x1a\n\x12\x61llow_ragged_batch\x18\x07 \x01(\x08\x12\x10\n\x08optional\x18\x08 \x01(\x08\x12\x1f\n\x17is_non_linear_format_io\x18\t \x01(\x08\";\n\x06\x46ormat\x12\x0f\n\x0b\x46ORMAT_NONE\x10\x00\x12\x0f\n\x0b\x46ORMAT_NHWC\x10\x01\x12\x0f\n\x0b\x46ORMAT_NCHW\x10\x02\"\xd3\x01\n\x0bModelOutput\x12\x0c\n\x04name\x18\x01 \x01(\t\x12&\n\tdata_type\x18\x02 \x01(\x0e\x32\x13.inference.DataType\x12\x0c\n\x04\x64ims\x18\x03 \x03(\x03\x12.\n\x07reshape\x18\x05 \x01(\x0b\x32\x1d.inference.ModelTensorReshape\x12\x16\n\x0elabel_filename\x18\x04 \x01(\t\x12\x17\n\x0fis_shape_tensor\x18\x06 \x01(\x08\x12\x1f\n\x17is_non_linear_format_io\x18\x07 \x01(\x08\"\xd9\x02\n\nBatchInput\x12(\n\x04kind\x18\x01 \x01(\x0e\x32\x1a.inference.BatchInput.Kind\x12\x13\n\x0btarget_name\x18\x02 \x03(\t\x12&\n\tdata_type\x18\x03 \x01(\x0e\x32\x13.inference.DataType\x12\x14\n\x0csource_input\x18\x04 \x03(\t\"\xcd\x01\n\x04Kind\x12\x17\n\x13\x42\x41TCH_ELEMENT_COUNT\x10\x00\x12#\n\x1f\x42\x41TCH_ACCUMULATED_ELEMENT_COUNT\x10\x01\x12-\n)BATCH_ACCUMULATED_ELEMENT_COUNT_WITH_ZERO\x10\x02\x12$\n BATCH_MAX_ELEMENT_COUNT_AS_SHAPE\x10\x03\x12\x14\n\x10\x42\x41TCH_ITEM_SHAPE\x10\x04\x12\x1c\n\x18\x42\x41TCH_ITEM_SHAPE_FLATTEN\x10\x05\"\x8f\x01\n\x0b\x42\x61tchOutput\x12\x13\n\x0btarget_name\x18\x01 \x03(\t\x12)\n\x04kind\x18\x02 \x01(\x0e\x32\x1b.inference.BatchOutput.Kind\x12\x14\n\x0csource_input\x18\x03 \x03(\t\"*\n\x04Kind\x12\"\n\x1e\x42\x41TCH_SCATTER_WITH_INPUT_SHAPE\x10\x00\"\x90\x02\n\x12ModelVersionPolicy\x12\x36\n\x06latest\x18\x01 \x01(\x0b\x32$.inference.ModelVersionPolicy.LatestH\x00\x12\x30\n\x03\x61ll\x18\x02 \x01(\x0b\x32!.inference.ModelVersionPolicy.AllH\x00\x12:\n\x08specific\x18\x03 \x01(\x0b\x32&.inference.ModelVersionPolicy.SpecificH\x00\x1a\x1e\n\x06Latest\x12\x14\n\x0cnum_versions\x18\x01 \x01(\r\x1a\x05\n\x03\x41ll\x1a\x1c\n\x08Specific\x12\x10\n\x08versions\x18\x01 \x03(\x03\x42\x0f\n\rpolicy_choice\"\xfd\r\n\x17ModelOptimizationPolicy\x12\x37\n\x05graph\x18\x01 \x01(\x0b\x32(.inference.ModelOptimizationPolicy.Graph\x12\x42\n\x08priority\x18\x02 \x01(\x0e\x32\x30.inference.ModelOptimizationPolicy.ModelPriority\x12\x35\n\x04\x63uda\x18\x03 \x01(\x0b\x32\'.inference.ModelOptimizationPolicy.Cuda\x12X\n\x16\x65xecution_accelerators\x18\x04 \x01(\x0b\x32\x38.inference.ModelOptimizationPolicy.ExecutionAccelerators\x12R\n\x13input_pinned_memory\x18\x05 \x01(\x0b\x32\x35.inference.ModelOptimizationPolicy.PinnedMemoryBuffer\x12S\n\x14output_pinned_memory\x18\x06 \x01(\x0b\x32\x35.inference.ModelOptimizationPolicy.PinnedMemoryBuffer\x12&\n\x1egather_kernel_buffer_threshold\x18\x07 \x01(\r\x12\x16\n\x0e\x65\x61ger_batching\x18\x08 \x01(\x08\x1a\x16\n\x05Graph\x12\r\n\x05level\x18\x01 \x01(\x05\x1a\xba\x05\n\x04\x43uda\x12\x0e\n\x06graphs\x18\x01 \x01(\x08\x12\x18\n\x10\x62usy_wait_events\x18\x02 \x01(\x08\x12\x45\n\ngraph_spec\x18\x03 \x03(\x0b\x32\x31.inference.ModelOptimizationPolicy.Cuda.GraphSpec\x12\x1a\n\x12output_copy_stream\x18\x04 \x01(\x08\x1a\xa4\x04\n\tGraphSpec\x12\x12\n\nbatch_size\x18\x01 \x01(\x05\x12K\n\x05input\x18\x02 \x03(\x0b\x32<.inference.ModelOptimizationPolicy.Cuda.GraphSpec.InputEntry\x12W\n\x11graph_lower_bound\x18\x03 \x01(\x0b\x32<.inference.ModelOptimizationPolicy.Cuda.GraphSpec.LowerBound\x1a\x14\n\x05Shape\x12\x0b\n\x03\x64im\x18\x01 \x03(\x03\x1a\xdf\x01\n\nLowerBound\x12\x12\n\nbatch_size\x18\x01 \x01(\x05\x12V\n\x05input\x18\x02 \x03(\x0b\x32G.inference.ModelOptimizationPolicy.Cuda.GraphSpec.LowerBound.InputEntry\x1a\x65\n\nInputEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x46\n\x05value\x18\x02 \x01(\x0b\x32\x37.inference.ModelOptimizationPolicy.Cuda.GraphSpec.Shape:\x02\x38\x01\x1a\x65\n\nInputEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x46\n\x05value\x18\x02 \x01(\x0b\x32\x37.inference.ModelOptimizationPolicy.Cuda.GraphSpec.Shape:\x02\x38\x01\x1a\xa4\x03\n\x15\x45xecutionAccelerators\x12g\n\x19gpu_execution_accelerator\x18\x01 \x03(\x0b\x32\x44.inference.ModelOptimizationPolicy.ExecutionAccelerators.Accelerator\x12g\n\x19\x63pu_execution_accelerator\x18\x02 \x03(\x0b\x32\x44.inference.ModelOptimizationPolicy.ExecutionAccelerators.Accelerator\x1a\xb8\x01\n\x0b\x41\x63\x63\x65lerator\x12\x0c\n\x04name\x18\x01 \x01(\t\x12h\n\nparameters\x18\x02 \x03(\x0b\x32T.inference.ModelOptimizationPolicy.ExecutionAccelerators.Accelerator.ParametersEntry\x1a\x31\n\x0fParametersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a$\n\x12PinnedMemoryBuffer\x12\x0e\n\x06\x65nable\x18\x01 \x01(\x08\"I\n\rModelPriority\x12\x14\n\x10PRIORITY_DEFAULT\x10\x00\x12\x10\n\x0cPRIORITY_MAX\x10\x01\x12\x10\n\x0cPRIORITY_MIN\x10\x02\"\xdb\x01\n\x10ModelQueuePolicy\x12\x41\n\x0etimeout_action\x18\x01 \x01(\x0e\x32).inference.ModelQueuePolicy.TimeoutAction\x12$\n\x1c\x64\x65\x66\x61ult_timeout_microseconds\x18\x02 \x01(\x04\x12\x1e\n\x16\x61llow_timeout_override\x18\x03 \x01(\x08\x12\x16\n\x0emax_queue_size\x18\x04 \x01(\r\"&\n\rTimeoutAction\x12\n\n\x06REJECT\x10\x00\x12\t\n\x05\x44\x45LAY\x10\x01\"\x9b\x03\n\x14ModelDynamicBatching\x12\x1c\n\x14preferred_batch_size\x18\x01 \x03(\x05\x12$\n\x1cmax_queue_delay_microseconds\x18\x02 \x01(\x04\x12\x19\n\x11preserve_ordering\x18\x03 \x01(\x08\x12\x17\n\x0fpriority_levels\x18\x04 \x01(\x04\x12\x1e\n\x16\x64\x65\x66\x61ult_priority_level\x18\x05 \x01(\x04\x12\x39\n\x14\x64\x65\x66\x61ult_queue_policy\x18\x06 \x01(\x0b\x32\x1b.inference.ModelQueuePolicy\x12W\n\x15priority_queue_policy\x18\x07 \x03(\x0b\x32\x38.inference.ModelDynamicBatching.PriorityQueuePolicyEntry\x1aW\n\x18PriorityQueuePolicyEntry\x12\x0b\n\x03key\x18\x01 \x01(\x04\x12*\n\x05value\x18\x02 \x01(\x0b\x32\x1b.inference.ModelQueuePolicy:\x02\x38\x01\"\xee\n\n\x15ModelSequenceBatching\x12\x41\n\x06\x64irect\x18\x03 \x01(\x0b\x32/.inference.ModelSequenceBatching.StrategyDirectH\x00\x12\x41\n\x06oldest\x18\x04 \x01(\x0b\x32/.inference.ModelSequenceBatching.StrategyOldestH\x00\x12&\n\x1emax_sequence_idle_microseconds\x18\x01 \x01(\x04\x12\x44\n\rcontrol_input\x18\x02 \x03(\x0b\x32-.inference.ModelSequenceBatching.ControlInput\x12\x35\n\x05state\x18\x05 \x03(\x0b\x32&.inference.ModelSequenceBatching.State\x12\x1a\n\x12iterative_sequence\x18\x06 \x01(\x08\x1a\xb1\x02\n\x07\x43ontrol\x12;\n\x04kind\x18\x01 \x01(\x0e\x32-.inference.ModelSequenceBatching.Control.Kind\x12\x18\n\x10int32_false_true\x18\x02 \x03(\x05\x12\x17\n\x0f\x66p32_false_true\x18\x03 \x03(\x02\x12\x17\n\x0f\x62ool_false_true\x18\x05 \x03(\x08\x12&\n\tdata_type\x18\x04 \x01(\x0e\x32\x13.inference.DataType\"u\n\x04Kind\x12\x1a\n\x16\x43ONTROL_SEQUENCE_START\x10\x00\x12\x1a\n\x16\x43ONTROL_SEQUENCE_READY\x10\x01\x12\x18\n\x14\x43ONTROL_SEQUENCE_END\x10\x02\x12\x1b\n\x17\x43ONTROL_SEQUENCE_CORRID\x10\x03\x1aW\n\x0c\x43ontrolInput\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x39\n\x07\x63ontrol\x18\x02 \x03(\x0b\x32(.inference.ModelSequenceBatching.Control\x1a\x8a\x01\n\x0cInitialState\x12&\n\tdata_type\x18\x01 \x01(\x0e\x32\x13.inference.DataType\x12\x0c\n\x04\x64ims\x18\x02 \x03(\x03\x12\x13\n\tzero_data\x18\x03 \x01(\x08H\x00\x12\x13\n\tdata_file\x18\x04 \x01(\tH\x00\x12\x0c\n\x04name\x18\x05 \x01(\tB\x0c\n\nstate_data\x1a\xf3\x01\n\x05State\x12\x12\n\ninput_name\x18\x01 \x01(\t\x12\x13\n\x0boutput_name\x18\x02 \x01(\t\x12&\n\tdata_type\x18\x03 \x01(\x0e\x32\x13.inference.DataType\x12\x0c\n\x04\x64ims\x18\x04 \x03(\x03\x12\x44\n\rinitial_state\x18\x05 \x03(\x0b\x32-.inference.ModelSequenceBatching.InitialState\x12(\n use_same_buffer_for_input_output\x18\x06 \x01(\x08\x12\x1b\n\x13use_growable_memory\x18\x07 \x01(\x08\x1aX\n\x0eStrategyDirect\x12$\n\x1cmax_queue_delay_microseconds\x18\x01 \x01(\x04\x12 \n\x18minimum_slot_utilization\x18\x02 \x01(\x02\x1a\x90\x01\n\x0eStrategyOldest\x12\x1f\n\x17max_candidate_sequences\x18\x01 \x01(\x05\x12\x1c\n\x14preferred_batch_size\x18\x02 \x03(\x05\x12$\n\x1cmax_queue_delay_microseconds\x18\x03 \x01(\x04\x12\x19\n\x11preserve_ordering\x18\x04 \x01(\x08\x42\x11\n\x0fstrategy_choice\"\xf6\x02\n\x0fModelEnsembling\x12-\n\x04step\x18\x01 \x03(\x0b\x32\x1f.inference.ModelEnsembling.Step\x1a\xb3\x02\n\x04Step\x12\x12\n\nmodel_name\x18\x01 \x01(\t\x12\x15\n\rmodel_version\x18\x02 \x01(\x03\x12@\n\tinput_map\x18\x03 \x03(\x0b\x32-.inference.ModelEnsembling.Step.InputMapEntry\x12\x42\n\noutput_map\x18\x04 \x03(\x0b\x32..inference.ModelEnsembling.Step.OutputMapEntry\x12\x17\n\x0fmodel_namespace\x18\x05 \x01(\t\x1a/\n\rInputMapEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\x30\n\x0eOutputMapEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"&\n\x0eModelParameter\x12\x14\n\x0cstring_value\x18\x01 \x01(\t\"\xd9\x02\n\x0bModelWarmup\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x12\n\nbatch_size\x18\x02 \x01(\r\x12\x32\n\x06inputs\x18\x03 \x03(\x0b\x32\".inference.ModelWarmup.InputsEntry\x12\r\n\x05\x63ount\x18\x04 \x01(\r\x1a\x97\x01\n\x05Input\x12&\n\tdata_type\x18\x01 \x01(\x0e\x32\x13.inference.DataType\x12\x0c\n\x04\x64ims\x18\x02 \x03(\x03\x12\x13\n\tzero_data\x18\x03 \x01(\x08H\x00\x12\x15\n\x0brandom_data\x18\x04 \x01(\x08H\x00\x12\x19\n\x0finput_data_file\x18\x05 \x01(\tH\x00\x42\x11\n\x0finput_data_type\x1aK\n\x0bInputsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12+\n\x05value\x18\x02 \x01(\x0b\x32\x1c.inference.ModelWarmup.Input:\x02\x38\x01\".\n\x0fModelOperations\x12\x1b\n\x13op_library_filename\x18\x01 \x03(\t\"+\n\x16ModelTransactionPolicy\x12\x11\n\tdecoupled\x18\x01 \x01(\x08\"\xe6\x01\n\x15ModelRepositoryAgents\x12\x36\n\x06\x61gents\x18\x01 \x03(\x0b\x32&.inference.ModelRepositoryAgents.Agent\x1a\x94\x01\n\x05\x41gent\x12\x0c\n\x04name\x18\x01 \x01(\t\x12J\n\nparameters\x18\x02 \x03(\x0b\x32\x36.inference.ModelRepositoryAgents.Agent.ParametersEntry\x1a\x31\n\x0fParametersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"$\n\x12ModelResponseCache\x12\x0e\n\x06\x65nable\x18\x01 \x01(\x08\"\xe2\x02\n\x0cModelMetrics\x12=\n\x0emetric_control\x18\x01 \x03(\x0b\x32%.inference.ModelMetrics.MetricControl\x1a\x92\x02\n\rMetricControl\x12Q\n\x11metric_identifier\x18\x01 \x01(\x0b\x32\x36.inference.ModelMetrics.MetricControl.MetricIdentifier\x12S\n\x11histogram_options\x18\x02 \x01(\x0b\x32\x36.inference.ModelMetrics.MetricControl.HistogramOptionsH\x00\x1a\"\n\x10MetricIdentifier\x12\x0e\n\x06\x66\x61mily\x18\x01 \x01(\t\x1a#\n\x10HistogramOptions\x12\x0f\n\x07\x62uckets\x18\x01 \x03(\x01\x42\x10\n\x0emetric_options\"\xf3\n\n\x0bModelConfig\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x10\n\x08platform\x18\x02 \x01(\t\x12\x0f\n\x07\x62\x61\x63kend\x18\x11 \x01(\t\x12\x0f\n\x07runtime\x18\x19 \x01(\t\x12\x35\n\x0eversion_policy\x18\x03 \x01(\x0b\x32\x1d.inference.ModelVersionPolicy\x12\x16\n\x0emax_batch_size\x18\x04 \x01(\x05\x12$\n\x05input\x18\x05 \x03(\x0b\x32\x15.inference.ModelInput\x12&\n\x06output\x18\x06 \x03(\x0b\x32\x16.inference.ModelOutput\x12*\n\x0b\x62\x61tch_input\x18\x14 \x03(\x0b\x32\x15.inference.BatchInput\x12,\n\x0c\x62\x61tch_output\x18\x15 \x03(\x0b\x32\x16.inference.BatchOutput\x12\x38\n\x0coptimization\x18\x0c \x01(\x0b\x32\".inference.ModelOptimizationPolicy\x12;\n\x10\x64ynamic_batching\x18\x0b \x01(\x0b\x32\x1f.inference.ModelDynamicBatchingH\x00\x12=\n\x11sequence_batching\x18\r \x01(\x0b\x32 .inference.ModelSequenceBatchingH\x00\x12\x39\n\x13\x65nsemble_scheduling\x18\x0f \x01(\x0b\x32\x1a.inference.ModelEnsemblingH\x00\x12\x35\n\x0einstance_group\x18\x07 \x03(\x0b\x32\x1d.inference.ModelInstanceGroup\x12\x1e\n\x16\x64\x65\x66\x61ult_model_filename\x18\x08 \x01(\t\x12H\n\x12\x63\x63_model_filenames\x18\t \x03(\x0b\x32,.inference.ModelConfig.CcModelFilenamesEntry\x12;\n\x0bmetric_tags\x18\n \x03(\x0b\x32&.inference.ModelConfig.MetricTagsEntry\x12:\n\nparameters\x18\x0e \x03(\x0b\x32&.inference.ModelConfig.ParametersEntry\x12,\n\x0cmodel_warmup\x18\x10 \x03(\x0b\x32\x16.inference.ModelWarmup\x12\x34\n\x10model_operations\x18\x12 \x01(\x0b\x32\x1a.inference.ModelOperations\x12\x43\n\x18model_transaction_policy\x18\x13 \x01(\x0b\x32!.inference.ModelTransactionPolicy\x12\x41\n\x17model_repository_agents\x18\x17 \x01(\x0b\x32 .inference.ModelRepositoryAgents\x12\x35\n\x0eresponse_cache\x18\x18 \x01(\x0b\x32\x1d.inference.ModelResponseCache\x12.\n\rmodel_metrics\x18\x1a \x01(\x0b\x32\x17.inference.ModelMetrics\x1a\x37\n\x15\x43\x63ModelFilenamesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\x31\n\x0fMetricTagsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1aL\n\x0fParametersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12(\n\x05value\x18\x02 \x01(\x0b\x32\x19.inference.ModelParameter:\x02\x38\x01\x42\x13\n\x11scheduling_choice*\xfa\x01\n\x08\x44\x61taType\x12\x10\n\x0cTYPE_INVALID\x10\x00\x12\r\n\tTYPE_BOOL\x10\x01\x12\x0e\n\nTYPE_UINT8\x10\x02\x12\x0f\n\x0bTYPE_UINT16\x10\x03\x12\x0f\n\x0bTYPE_UINT32\x10\x04\x12\x0f\n\x0bTYPE_UINT64\x10\x05\x12\r\n\tTYPE_INT8\x10\x06\x12\x0e\n\nTYPE_INT16\x10\x07\x12\x0e\n\nTYPE_INT32\x10\x08\x12\x0e\n\nTYPE_INT64\x10\t\x12\r\n\tTYPE_FP16\x10\n\x12\r\n\tTYPE_FP32\x10\x0b\x12\r\n\tTYPE_FP64\x10\x0c\x12\x0f\n\x0bTYPE_STRING\x10\r\x12\r\n\tTYPE_BF16\x10\x0e\x62\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'model_config_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _MODELOPTIMIZATIONPOLICY_CUDA_GRAPHSPEC_LOWERBOUND_INPUTENTRY._options = None
  _MODELOPTIMIZATIONPOLICY_CUDA_GRAPHSPEC_LOWERBOUND_INPUTENTRY._serialized_options = b'8\001'
  _MODELOPTIMIZATIONPOLICY_CUDA_GRAPHSPEC_INPUTENTRY._options = None
  _MODELOPTIMIZATIONPOLICY_CUDA_GRAPHSPEC_INPUTENTRY._serialized_options = b'8\001'
  _MODELOPTIMIZATIONPOLICY_EXECUTIONACCELERATORS_ACCELERATOR_PARAMETERSENTRY._options = None
  _MODELOPTIMIZATIONPOLICY_EXECUTIONACCELERATORS_ACCELERATOR_PARAMETERSENTRY._serialized_options = b'8\001'
  _MODELDYNAMICBATCHING_PRIORITYQUEUEPOLICYENTRY._options = None
  _MODELDYNAMICBATCHING_PRIORITYQUEUEPOLICYENTRY._serialized_options = b'8\001'
  _MODELENSEMBLING_STEP_INPUTMAPENTRY._options = None
  _MODELENSEMBLING_STEP_INPUTMAPENTRY._serialized_options = b'8\001'
  _MODELENSEMBLING_STEP_OUTPUTMAPENTRY._options = None
  _MODELENSEMBLING_STEP_OUTPUTMAPENTRY._serialized_options = b'8\001'
  _MODELWARMUP_INPUTSENTRY._options = None
  _MODELWARMUP_INPUTSENTRY._serialized_options = b'8\001'
  _MODELREPOSITORYAGENTS_AGENT_PARAMETERSENTRY._options = None
  _MODELREPOSITORYAGENTS_AGENT_PARAMETERSENTRY._serialized_options = b'8\001'
  _MODELCONFIG_CCMODELFILENAMESENTRY._options = None
  _MODELCONFIG_CCMODELFILENAMESENTRY._serialized_options = b'8\001'
  _MODELCONFIG_METRICTAGSENTRY._options = None
  _MODELCONFIG_METRICTAGSENTRY._serialized_options = b'8\001'
  _MODELCONFIG_PARAMETERSENTRY._options = None
  _MODELCONFIG_PARAMETERSENTRY._serialized_options = b'8\001'
  _DATATYPE._serialized_start=8776
  _DATATYPE._serialized_end=9026
  _MODELRATELIMITER._serialized_start=34
  _MODELRATELIMITER._serialized_end=184
  _MODELRATELIMITER_RESOURCE._serialized_start=129
  _MODELRATELIMITER_RESOURCE._serialized_end=184
  _MODELINSTANCEGROUP._serialized_start=187
  _MODELINSTANCEGROUP._serialized_end=706
  _MODELINSTANCEGROUP_SECONDARYDEVICE._serialized_start=483
  _MODELINSTANCEGROUP_SECONDARYDEVICE._serialized_end=639
  _MODELINSTANCEGROUP_SECONDARYDEVICE_SECONDARYDEVICEKIND._serialized_start=602
  _MODELINSTANCEGROUP_SECONDARYDEVICE_SECONDARYDEVICEKIND._serialized_end=639
  _MODELINSTANCEGROUP_KIND._serialized_start=641
  _MODELINSTANCEGROUP_KIND._serialized_end=706
  _MODELTENSORRESHAPE._serialized_start=708
  _MODELTENSORRESHAPE._serialized_end=743
  _MODELINPUT._serialized_start=746
  _MODELINPUT._serialized_end=1085
  _MODELINPUT_FORMAT._serialized_start=1026
  _MODELINPUT_FORMAT._serialized_end=1085
  _MODELOUTPUT._serialized_start=1088
  _MODELOUTPUT._serialized_end=1299
  _BATCHINPUT._serialized_start=1302
  _BATCHINPUT._serialized_end=1647
  _BATCHINPUT_KIND._serialized_start=1442
  _BATCHINPUT_KIND._serialized_end=1647
  _BATCHOUTPUT._serialized_start=1650
  _BATCHOUTPUT._serialized_end=1793
  _BATCHOUTPUT_KIND._serialized_start=1751
  _BATCHOUTPUT_KIND._serialized_end=1793
  _MODELVERSIONPOLICY._serialized_start=1796
  _MODELVERSIONPOLICY._serialized_end=2068
  _MODELVERSIONPOLICY_LATEST._serialized_start=1984
  _MODELVERSIONPOLICY_LATEST._serialized_end=2014
  _MODELVERSIONPOLICY_ALL._serialized_start=2016
  _MODELVERSIONPOLICY_ALL._serialized_end=2021
  _MODELVERSIONPOLICY_SPECIFIC._serialized_start=2023
  _MODELVERSIONPOLICY_SPECIFIC._serialized_end=2051
  _MODELOPTIMIZATIONPOLICY._serialized_start=2071
  _MODELOPTIMIZATIONPOLICY._serialized_end=3860
  _MODELOPTIMIZATIONPOLICY_GRAPH._serialized_start=2601
  _MODELOPTIMIZATIONPOLICY_GRAPH._serialized_end=2623
  _MODELOPTIMIZATIONPOLICY_CUDA._serialized_start=2626
  _MODELOPTIMIZATIONPOLICY_CUDA._serialized_end=3324
  _MODELOPTIMIZATIONPOLICY_CUDA_GRAPHSPEC._serialized_start=2776
  _MODELOPTIMIZATIONPOLICY_CUDA_GRAPHSPEC._serialized_end=3324
  _MODELOPTIMIZATIONPOLICY_CUDA_GRAPHSPEC_SHAPE._serialized_start=2975
  _MODELOPTIMIZATIONPOLICY_CUDA_GRAPHSPEC_SHAPE._serialized_end=2995
  _MODELOPTIMIZATIONPOLICY_CUDA_GRAPHSPEC_LOWERBOUND._serialized_start=2998
  _MODELOPTIMIZATIONPOLICY_CUDA_GRAPHSPEC_LOWERBOUND._serialized_end=3221
  _MODELOPTIMIZATIONPOLICY_CUDA_GRAPHSPEC_LOWERBOUND_INPUTENTRY._serialized_start=3120
  _MODELOPTIMIZATIONPOLICY_CUDA_GRAPHSPEC_LOWERBOUND_INPUTENTRY._serialized_end=3221
  _MODELOPTIMIZATIONPOLICY_CUDA_GRAPHSPEC_INPUTENTRY._serialized_start=3120
  _MODELOPTIMIZATIONPOLICY_CUDA_GRAPHSPEC_INPUTENTRY._serialized_end=3221
  _MODELOPTIMIZATIONPOLICY_EXECUTIONACCELERATORS._serialized_start=3327
  _MODELOPTIMIZATIONPOLICY_EXECUTIONACCELERATORS._serialized_end=3747
  _MODELOPTIMIZATIONPOLICY_EXECUTIONACCELERATORS_ACCELERATOR._serialized_start=3563
  _MODELOPTIMIZATIONPOLICY_EXECUTIONACCELERATORS_ACCELERATOR._serialized_end=3747
  _MODELOPTIMIZATIONPOLICY_EXECUTIONACCELERATORS_ACCELERATOR_PARAMETERSENTRY._serialized_start=3698
  _MODELOPTIMIZATIONPOLICY_EXECUTIONACCELERATORS_ACCELERATOR_PARAMETERSENTRY._serialized_end=3747
  _MODELOPTIMIZATIONPOLICY_PINNEDMEMORYBUFFER._serialized_start=3749
  _MODELOPTIMIZATIONPOLICY_PINNEDMEMORYBUFFER._serialized_end=3785
  _MODELOPTIMIZATIONPOLICY_MODELPRIORITY._serialized_start=3787
  _MODELOPTIMIZATIONPOLICY_MODELPRIORITY._serialized_end=3860
  _MODELQUEUEPOLICY._serialized_start=3863
  _MODELQUEUEPOLICY._serialized_end=4082
  _MODELQUEUEPOLICY_TIMEOUTACTION._serialized_start=4044
  _MODELQUEUEPOLICY_TIMEOUTACTION._serialized_end=4082
  _MODELDYNAMICBATCHING._serialized_start=4085
  _MODELDYNAMICBATCHING._serialized_end=4496
  _MODELDYNAMICBATCHING_PRIORITYQUEUEPOLICYENTRY._serialized_start=4409
  _MODELDYNAMICBATCHING_PRIORITYQUEUEPOLICYENTRY._serialized_end=4496
  _MODELSEQUENCEBATCHING._serialized_start=4499
  _MODELSEQUENCEBATCHING._serialized_end=5889
  _MODELSEQUENCEBATCHING_CONTROL._serialized_start=4852
  _MODELSEQUENCEBATCHING_CONTROL._serialized_end=5157
  _MODELSEQUENCEBATCHING_CONTROL_KIND._serialized_start=5040
  _MODELSEQUENCEBATCHING_CONTROL_KIND._serialized_end=5157
  _MODELSEQUENCEBATCHING_CONTROLINPUT._serialized_start=5159
  _MODELSEQUENCEBATCHING_CONTROLINPUT._serialized_end=5246
  _MODELSEQUENCEBATCHING_INITIALSTATE._serialized_start=5249
  _MODELSEQUENCEBATCHING_INITIALSTATE._serialized_end=5387
  _MODELSEQUENCEBATCHING_STATE._serialized_start=5390
  _MODELSEQUENCEBATCHING_STATE._serialized_end=5633
  _MODELSEQUENCEBATCHING_STRATEGYDIRECT._serialized_start=5635
  _MODELSEQUENCEBATCHING_STRATEGYDIRECT._serialized_end=5723
  _MODELSEQUENCEBATCHING_STRATEGYOLDEST._serialized_start=5726
  _MODELSEQUENCEBATCHING_STRATEGYOLDEST._serialized_end=5870
  _MODELENSEMBLING._serialized_start=5892
  _MODELENSEMBLING._serialized_end=6266
  _MODELENSEMBLING_STEP._serialized_start=5959
  _MODELENSEMBLING_STEP._serialized_end=6266
  _MODELENSEMBLING_STEP_INPUTMAPENTRY._serialized_start=6169
  _MODELENSEMBLING_STEP_INPUTMAPENTRY._serialized_end=6216
  _MODELENSEMBLING_STEP_OUTPUTMAPENTRY._serialized_start=6218
  _MODELENSEMBLING_STEP_OUTPUTMAPENTRY._serialized_end=6266
  _MODELPARAMETER._serialized_start=6268
  _MODELPARAMETER._serialized_end=6306
  _MODELWARMUP._serialized_start=6309
  _MODELWARMUP._serialized_end=6654
  _MODELWARMUP_INPUT._serialized_start=6426
  _MODELWARMUP_INPUT._serialized_end=6577
  _MODELWARMUP_INPUTSENTRY._serialized_start=6579
  _MODELWARMUP_INPUTSENTRY._serialized_end=6654
  _MODELOPERATIONS._serialized_start=6656
  _MODELOPERATIONS._serialized_end=6702
  _MODELTRANSACTIONPOLICY._serialized_start=6704
  _MODELTRANSACTIONPOLICY._serialized_end=6747
  _MODELREPOSITORYAGENTS._serialized_start=6750
  _MODELREPOSITORYAGENTS._serialized_end=6980
  _MODELREPOSITORYAGENTS_AGENT._serialized_start=6832
  _MODELREPOSITORYAGENTS_AGENT._serialized_end=6980
  _MODELREPOSITORYAGENTS_AGENT_PARAMETERSENTRY._serialized_start=3698
  _MODELREPOSITORYAGENTS_AGENT_PARAMETERSENTRY._serialized_end=3747
  _MODELRESPONSECACHE._serialized_start=6982
  _MODELRESPONSECACHE._serialized_end=7018
  _MODELMETRICS._serialized_start=7021
  _MODELMETRICS._serialized_end=7375
  _MODELMETRICS_METRICCONTROL._serialized_start=7101
  _MODELMETRICS_METRICCONTROL._serialized_end=7375
  _MODELMETRICS_METRICCONTROL_METRICIDENTIFIER._serialized_start=7286
  _MODELMETRICS_METRICCONTROL_METRICIDENTIFIER._serialized_end=7320
  _MODELMETRICS_METRICCONTROL_HISTOGRAMOPTIONS._serialized_start=7322
  _MODELMETRICS_METRICCONTROL_HISTOGRAMOPTIONS._serialized_end=7357
  _MODELCONFIG._serialized_start=7378
  _MODELCONFIG._serialized_end=8773
  _MODELCONFIG_CCMODELFILENAMESENTRY._serialized_start=8568
  _MODELCONFIG_CCMODELFILENAMESENTRY._serialized_end=8623
  _MODELCONFIG_METRICTAGSENTRY._serialized_start=8625
  _MODELCONFIG_METRICTAGSENTRY._serialized_end=8674
  _MODELCONFIG_PARAMETERSENTRY._serialized_start=8676
  _MODELCONFIG_PARAMETERSENTRY._serialized_end=8752
# @@protoc_insertion_point(module_scope)
